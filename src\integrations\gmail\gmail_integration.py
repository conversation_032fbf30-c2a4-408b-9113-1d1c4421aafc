"""Gmail integration with email intelligence."""

import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from ...core.logging import logger
from ...agents.email_intelligence_service import email_intelligence_service
from ...agents.email_response_generator import email_response_generator
from ...knowledge.database import get_db
from ...knowledge.models import Email, LearningFeedback
from .gmail_service import gmail_service, GmailMessage


@dataclass
class ProcessedEmail:
    """Result of processing a Gmail message."""
    gmail_message: GmailMessage
    analysis: Any  # EmailAnalysis
    suggested_response: Optional[str] = None
    actions_taken: List[str] = None
    requires_attention: bool = False


class GmailIntegration:
    """Gmail integration with intelligent email processing."""
    
    def __init__(self):
        self.logger = logger
        self.gmail_service = gmail_service
        self.last_sync_time = None
        
        # Processing settings
        self.auto_label_enabled = True
        self.auto_reply_enabled = False  # Disabled by default for safety
        self.priority_threshold = 0.7  # Urgency score threshold for priority
        
        # Labels for organization
        self.priority_labels = {
            'critical': 'AI-Assistant/Critical',
            'high': 'AI-Assistant/High-Priority',
            'medium': 'AI-Assistant/Medium-Priority',
            'low': 'AI-Assistant/Low-Priority',
            'automated': 'AI-Assistant/Automated'
        }
    
    async def initialize(self) -> bool:
        """Initialize Gmail integration."""
        try:
            self.logger.info("Initializing Gmail integration...")
            
            # Authenticate with Gmail
            if not await self.gmail_service.authenticate():
                self.logger.error("Gmail authentication failed")
                return False
            
            self.logger.info(f"Gmail integration initialized for: {self.gmail_service.user_email}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error initializing Gmail integration: {e}")
            return False
    
    async def process_unread_emails(self, max_emails: int = 50) -> List[ProcessedEmail]:
        """Process unread emails with intelligence analysis."""
        try:
            self.logger.info(f"Processing unread emails (max: {max_emails})")
            
            # Get unread messages
            unread_messages = await self.gmail_service.get_unread_messages(max_emails)
            
            if not unread_messages:
                self.logger.info("No unread emails found")
                return []
            
            self.logger.info(f"Found {len(unread_messages)} unread emails")
            
            # Process each message
            processed_emails = []
            for message in unread_messages:
                try:
                    processed = await self._process_single_email(message)
                    if processed:
                        processed_emails.append(processed)
                        
                        # Auto-label if enabled
                        if self.auto_label_enabled:
                            await self._auto_label_email(message, processed.analysis)
                        
                except Exception as e:
                    self.logger.error(f"Error processing email {message.id}: {e}")
                    continue
            
            # Sort by priority and urgency
            processed_emails.sort(
                key=lambda x: (x.analysis.priority.value, -x.analysis.urgency_score),
                reverse=True
            )
            
            self.logger.info(f"Successfully processed {len(processed_emails)} emails")
            return processed_emails
            
        except Exception as e:
            self.logger.error(f"Error processing unread emails: {e}")
            return []
    
    async def get_priority_inbox(self, max_emails: int = 20) -> List[ProcessedEmail]:
        """Get priority inbox with intelligent analysis."""
        try:
            # Get recent important and unread messages
            important_messages = await self.gmail_service.get_important_messages(max_emails // 2)
            recent_messages = await self.gmail_service.get_recent_messages(hours=24, max_results=max_emails)
            
            # Combine and deduplicate
            all_messages = {msg.id: msg for msg in important_messages + recent_messages}
            messages = list(all_messages.values())
            
            # Process with intelligence
            processed_emails = []
            for message in messages:
                try:
                    processed = await self._process_single_email(message)
                    if processed and (processed.analysis.urgency_score >= self.priority_threshold or 
                                    processed.analysis.priority.value in ['critical', 'high']):
                        processed_emails.append(processed)
                except Exception as e:
                    self.logger.error(f"Error processing priority email {message.id}: {e}")
                    continue
            
            # Sort by priority
            processed_emails.sort(
                key=lambda x: (x.analysis.urgency_score, x.analysis.importance_score),
                reverse=True
            )
            
            return processed_emails[:max_emails]
            
        except Exception as e:
            self.logger.error(f"Error getting priority inbox: {e}")
            return []
    
    async def _process_single_email(self, gmail_message: GmailMessage) -> Optional[ProcessedEmail]:
        """Process a single Gmail message with intelligence."""
        try:
            # Convert Gmail message to email data format
            email_data = {
                'id': gmail_message.id,
                'subject': gmail_message.subject,
                'body': gmail_message.body_text,
                'sender_email': gmail_message.sender_email,
                'sender_name': gmail_message.sender_name,
                'recipients': gmail_message.recipient_emails,
                'received_date': gmail_message.received_date,
                'is_unread': gmail_message.is_unread,
                'is_important': gmail_message.is_important,
                'labels': gmail_message.labels,
                'snippet': gmail_message.snippet
            }
            
            # Analyze with email intelligence
            analysis = await email_intelligence_service.analyze_email(email_data)
            
            # Determine if requires attention
            requires_attention = (
                analysis.priority.value in ['critical', 'high'] or
                analysis.urgency_score >= self.priority_threshold or
                analysis.requires_decision or
                len(analysis.questions_asked) > 0
            )
            
            # Save to database for learning
            await self._save_email_to_database(gmail_message, analysis)
            
            return ProcessedEmail(
                gmail_message=gmail_message,
                analysis=analysis,
                requires_attention=requires_attention,
                actions_taken=[]
            )
            
        except Exception as e:
            self.logger.error(f"Error processing single email: {e}")
            return None
    
    async def _save_email_to_database(self, gmail_message: GmailMessage, analysis: Any):
        """Save email and analysis to database for learning."""
        try:
            with next(get_db()) as session:
                # Create email record
                email_record = Email(
                    external_id=gmail_message.id,
                    subject=gmail_message.subject,
                    sender_email=gmail_message.sender_email,
                    sender_name=gmail_message.sender_name,
                    recipient_emails=gmail_message.recipient_emails,
                    body_text=gmail_message.body_text,
                    body_html=gmail_message.body_html,
                    received_date=gmail_message.received_date,
                    source='gmail',
                    email_metadata={
                        'thread_id': gmail_message.thread_id,
                        'labels': gmail_message.labels,
                        'is_unread': gmail_message.is_unread,
                        'is_important': gmail_message.is_important,
                        'has_attachments': gmail_message.has_attachments,
                        'snippet': gmail_message.snippet,
                        'analysis': {
                            'priority': analysis.priority.value,
                            'urgency_score': analysis.urgency_score,
                            'importance_score': analysis.importance_score,
                            'sentiment': analysis.sentiment,
                            'recommended_action': analysis.recommended_action.value,
                            'confidence': analysis.confidence,
                            'reasoning': analysis.reasoning
                        }
                    }
                )
                
                session.add(email_record)
                session.commit()
                
        except Exception as e:
            self.logger.error(f"Error saving email to database: {e}")
    
    async def _auto_label_email(self, gmail_message: GmailMessage, analysis: Any):
        """Automatically label email based on analysis."""
        try:
            priority = analysis.priority.value
            label_name = self.priority_labels.get(priority)
            
            if label_name:
                await self.gmail_service.add_label(gmail_message.id, label_name)
                self.logger.debug(f"Added label '{label_name}' to email {gmail_message.id}")
                
        except Exception as e:
            self.logger.error(f"Error auto-labeling email: {e}")
    
    async def generate_and_send_reply(
        self, 
        gmail_message: GmailMessage, 
        user_decision: str,
        send_immediately: bool = False
    ) -> Optional[str]:
        """Generate and optionally send a reply."""
        try:
            # First analyze the email if not already done
            email_data = {
                'id': gmail_message.id,
                'subject': gmail_message.subject,
                'body': gmail_message.body_text,
                'sender_email': gmail_message.sender_email,
                'received_date': gmail_message.received_date
            }
            
            analysis = await email_intelligence_service.analyze_email(email_data)
            
            # Generate response
            context = {
                'subject': gmail_message.subject,
                'sender': gmail_message.sender_email,
                'sender_name': gmail_message.sender_name
            }
            
            response_draft = await email_response_generator.generate_response(
                analysis, user_decision, context
            )
            
            if send_immediately and not response_draft.requires_review:
                # Send the reply
                success = await self.gmail_service.send_reply(
                    gmail_message.id,
                    response_draft.body,
                    response_draft.subject
                )
                
                if success:
                    self.logger.info(f"Reply sent automatically for email {gmail_message.id}")
                    return "Reply sent successfully"
                else:
                    return "Failed to send reply"
            else:
                # Return draft for review
                return response_draft.body
                
        except Exception as e:
            self.logger.error(f"Error generating/sending reply: {e}")
            return None
    
    async def mark_email_processed(self, gmail_message: GmailMessage, action_taken: str):
        """Mark email as processed and optionally mark as read."""
        try:
            # Mark as read if it was unread
            if gmail_message.is_unread:
                await self.gmail_service.mark_as_read(gmail_message.id)
            
            # Add processed label
            await self.gmail_service.add_label(gmail_message.id, 'AI-Assistant/Processed')
            
            # Log the action for learning
            with next(get_db()) as session:
                feedback = LearningFeedback(
                    entity_type='email',
                    entity_id=gmail_message.id,
                    feedback_type='email_action',
                    original_value={'action': action_taken},
                    corrected_value={'timestamp': datetime.now().isoformat()},
                    user_action=action_taken,
                    feedback_metadata={'gmail_integration': True}
                )
                session.add(feedback)
                session.commit()
                
        except Exception as e:
            self.logger.error(f"Error marking email as processed: {e}")
    
    async def sync_recent_emails(self, hours: int = 1) -> int:
        """Sync recent emails for processing."""
        try:
            self.logger.info(f"Syncing emails from last {hours} hours")
            
            # Get recent messages
            recent_messages = await self.gmail_service.get_recent_messages(
                hours=hours, 
                max_results=100
            )
            
            processed_count = 0
            for message in recent_messages:
                try:
                    # Check if already processed
                    if 'AI-Assistant/Processed' in message.labels:
                        continue
                    
                    # Process the email
                    processed = await self._process_single_email(message)
                    if processed:
                        processed_count += 1
                        
                        # Auto-label if enabled
                        if self.auto_label_enabled:
                            await self._auto_label_email(message, processed.analysis)
                            
                except Exception as e:
                    self.logger.error(f"Error syncing email {message.id}: {e}")
                    continue
            
            self.last_sync_time = datetime.now()
            self.logger.info(f"Synced {processed_count} new emails")
            return processed_count
            
        except Exception as e:
            self.logger.error(f"Error syncing recent emails: {e}")
            return 0
    
    def get_status(self) -> Dict[str, Any]:
        """Get Gmail integration status."""
        return {
            'authenticated': self.gmail_service.service is not None,
            'user_email': self.gmail_service.user_email,
            'last_sync': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'auto_label_enabled': self.auto_label_enabled,
            'auto_reply_enabled': self.auto_reply_enabled,
            'priority_threshold': self.priority_threshold
        }


# Global Gmail integration instance
gmail_integration = GmailIntegration()
