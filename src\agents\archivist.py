"""Archivist Agent for the Assistant."""

import hashlib
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Set, Tuple
from dataclasses import dataclass
from pathlib import Path

from ..core.logging import logger
from ..core.config import settings


@dataclass
class ArchiveItem:
    """Represents an item in the archive."""
    id: str
    content_type: str  # 'note', 'document', 'email', 'task', 'project'
    title: str
    content_hash: str
    file_path: Optional[str]
    metadata: Dict[str, Any]
    archived_at: datetime
    original_created_at: Optional[datetime]
    tags: List[str]
    importance_score: float


@dataclass
class DuplicateItem:
    """Represents a potential duplicate item."""
    item1_id: str
    item2_id: str
    similarity_score: float
    duplicate_type: str  # 'exact', 'near_duplicate', 'version'
    recommended_action: str  # 'merge', 'keep_both', 'archive_older'


@dataclass
class ArchiveStats:
    """Archive statistics."""
    total_items: int
    items_by_type: Dict[str, int]
    storage_size_mb: float
    oldest_item_date: Optional[datetime]
    newest_item_date: Optional[datetime]
    duplicates_found: int
    compression_ratio: float


class ArchivistAgent:
    """
    Agent responsible for intelligent archiving and content management.
    
    Key responsibilities:
    - Archive old and unused content intelligently
    - Detect and manage duplicate content
    - Organize archived content with proper metadata
    - Provide efficient search and retrieval of archived items
    - Maintain archive integrity and optimize storage
    """
    
    def __init__(self):
        self.config = settings.agents.archivist
        self.archive_path = Path(getattr(self.config, 'archive_path', 'data/archive'))
        self.auto_archive = getattr(self.config, 'auto_archive', True)
        self.duplicate_detection = getattr(self.config, 'duplicate_detection', True)
        
        # Archive policies
        self.archive_policies = {
            'notes': {
                'age_threshold_days': 365,  # Archive notes older than 1 year
                'importance_threshold': 0.3,  # Archive low importance notes
                'access_threshold_days': 180  # Archive if not accessed in 6 months
            },
            'documents': {
                'age_threshold_days': 730,  # Archive documents older than 2 years
                'importance_threshold': 0.2,
                'access_threshold_days': 365
            },
            'emails': {
                'age_threshold_days': 1095,  # Archive emails older than 3 years
                'importance_threshold': 0.1,
                'access_threshold_days': 730
            },
            'tasks': {
                'age_threshold_days': 90,  # Archive completed tasks after 3 months
                'importance_threshold': 0.4,
                'access_threshold_days': 60
            }
        }
        
        # Ensure archive directory exists
        self.archive_path.mkdir(parents=True, exist_ok=True)
    
    async def process_content_for_archiving(self, content_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process content items to determine what should be archived.
        
        Args:
            content_items: List of content dictionaries
            
        Returns:
            Dictionary with archiving results
        """
        logger.info(f"Processing {len(content_items)} content items for archiving")
        
        result = {
            'status': 'success',
            'items_processed': len(content_items),
            'items_archived': 0,
            'duplicates_found': 0,
            'storage_saved_mb': 0.0,
            'archive_candidates': [],
            'errors': []
        }
        
        try:
            # Identify archive candidates
            candidates = await self._identify_archive_candidates(content_items)
            result['archive_candidates'] = candidates
            
            # Detect duplicates
            if self.duplicate_detection:
                duplicates = await self._detect_duplicates(content_items)
                result['duplicates_found'] = len(duplicates)
            
            # Auto-archive if enabled
            if self.auto_archive:
                archived_items = await self._auto_archive_items(candidates)
                result['items_archived'] = len(archived_items)
                result['storage_saved_mb'] = await self._calculate_storage_saved(archived_items)
            
        except Exception as e:
            logger.error(f"Error processing content for archiving: {e}")
            result['errors'].append(str(e))
            result['status'] = 'error'
        
        logger.info(f"Archiving processing completed: {result}")
        return result
    
    async def archive_item(self, item: Dict[str, Any], reason: str = "manual") -> Dict[str, Any]:
        """
        Archive a specific item.
        
        Args:
            item: Item dictionary to archive
            reason: Reason for archiving
            
        Returns:
            Dictionary with archiving result
        """
        try:
            # Generate content hash
            content_hash = self._generate_content_hash(item)
            
            # Create archive item
            archive_item = ArchiveItem(
                id=item.get('id', ''),
                content_type=item.get('type', 'unknown'),
                title=item.get('title', 'Untitled'),
                content_hash=content_hash,
                file_path=None,
                metadata={
                    'original_id': item.get('id'),
                    'archive_reason': reason,
                    'original_metadata': item.get('metadata', {}),
                    'size_bytes': len(str(item).encode('utf-8'))
                },
                archived_at=datetime.now(),
                original_created_at=self._parse_date(item.get('created_at')),
                tags=item.get('tags', []),
                importance_score=item.get('importance_score', 0.5)
            )
            
            # Save to archive
            archive_path = await self._save_to_archive(archive_item, item)
            archive_item.file_path = str(archive_path)
            
            logger.info(f"Archived item {item.get('id')} to {archive_path}")
            
            return {
                'status': 'success',
                'item_id': item.get('id'),
                'archive_path': str(archive_path),
                'content_hash': content_hash,
                'archived_at': archive_item.archived_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error archiving item {item.get('id')}: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def search_archive(self, query: str, content_type: Optional[str] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Search archived content.
        
        Args:
            query: Search query
            content_type: Filter by content type
            limit: Maximum results to return
            
        Returns:
            List of matching archived items
        """
        try:
            # This is a simplified implementation
            # In a real system, you'd use a proper search index
            
            results = []
            archive_files = list(self.archive_path.rglob("*.json"))
            
            for file_path in archive_files[:limit]:
                try:
                    import json
                    with open(file_path, 'r', encoding='utf-8') as f:
                        archived_item = json.load(f)
                    
                    # Simple text matching
                    searchable_text = (
                        archived_item.get('title', '') + ' ' +
                        archived_item.get('content', '') + ' ' +
                        ' '.join(archived_item.get('tags', []))
                    ).lower()
                    
                    if query.lower() in searchable_text:
                        if not content_type or archived_item.get('content_type') == content_type:
                            results.append({
                                'id': archived_item.get('id'),
                                'title': archived_item.get('title'),
                                'content_type': archived_item.get('content_type'),
                                'archived_at': archived_item.get('archived_at'),
                                'tags': archived_item.get('tags', []),
                                'file_path': str(file_path),
                                'relevance_score': self._calculate_relevance(query, searchable_text)
                            })
                            
                except Exception as e:
                    logger.error(f"Error reading archive file {file_path}: {e}")
                    continue
            
            # Sort by relevance
            results.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Error searching archive: {e}")
            return []
    
    async def get_archive_stats(self) -> ArchiveStats:
        """Get statistics about the archive."""
        try:
            archive_files = list(self.archive_path.rglob("*.json"))
            
            stats = ArchiveStats(
                total_items=len(archive_files),
                items_by_type={},
                storage_size_mb=0.0,
                oldest_item_date=None,
                newest_item_date=None,
                duplicates_found=0,
                compression_ratio=1.0
            )
            
            dates = []
            
            for file_path in archive_files:
                try:
                    import json
                    with open(file_path, 'r', encoding='utf-8') as f:
                        archived_item = json.load(f)
                    
                    # Count by type
                    content_type = archived_item.get('content_type', 'unknown')
                    stats.items_by_type[content_type] = stats.items_by_type.get(content_type, 0) + 1
                    
                    # Calculate size
                    stats.storage_size_mb += file_path.stat().st_size / (1024 * 1024)
                    
                    # Track dates
                    if archived_item.get('archived_at'):
                        date = datetime.fromisoformat(archived_item['archived_at'].replace('Z', '+00:00'))
                        dates.append(date)
                        
                except Exception as e:
                    logger.error(f"Error processing archive file {file_path}: {e}")
                    continue
            
            if dates:
                stats.oldest_item_date = min(dates)
                stats.newest_item_date = max(dates)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting archive stats: {e}")
            return ArchiveStats(0, {}, 0.0, None, None, 0, 1.0)
    
    async def _identify_archive_candidates(self, content_items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify items that are candidates for archiving."""
        candidates = []
        
        for item in content_items:
            content_type = item.get('type', 'unknown')
            policy = self.archive_policies.get(content_type, self.archive_policies['notes'])
            
            should_archive = False
            reasons = []
            
            # Check age
            if item.get('created_at'):
                created_date = self._parse_date(item['created_at'])
                if created_date:
                    age_days = (datetime.now() - created_date).days
                    if age_days > policy['age_threshold_days']:
                        should_archive = True
                        reasons.append(f"older than {policy['age_threshold_days']} days")
            
            # Check importance
            importance = item.get('importance_score', 0.5)
            if importance < policy['importance_threshold']:
                should_archive = True
                reasons.append(f"low importance score ({importance})")
            
            # Check last access (if available)
            if item.get('last_accessed_at'):
                last_access = self._parse_date(item['last_accessed_at'])
                if last_access:
                    days_since_access = (datetime.now() - last_access).days
                    if days_since_access > policy['access_threshold_days']:
                        should_archive = True
                        reasons.append(f"not accessed for {days_since_access} days")
            
            # Special rules for completed tasks
            if content_type == 'task' and item.get('status') == 'completed':
                completed_date = self._parse_date(item.get('completed_at'))
                if completed_date:
                    days_since_completion = (datetime.now() - completed_date).days
                    if days_since_completion > 30:  # Archive completed tasks after 30 days
                        should_archive = True
                        reasons.append("completed task older than 30 days")
            
            if should_archive:
                candidates.append({
                    'item': item,
                    'reasons': reasons,
                    'confidence': self._calculate_archive_confidence(item, reasons)
                })
        
        return candidates
    
    async def _detect_duplicates(self, content_items: List[Dict[str, Any]]) -> List[DuplicateItem]:
        """Detect duplicate content items."""
        duplicates = []
        processed_pairs = set()
        
        for i, item1 in enumerate(content_items):
            for j, item2 in enumerate(content_items[i+1:], i+1):
                pair_key = tuple(sorted([item1.get('id', ''), item2.get('id', '')]))
                if pair_key in processed_pairs:
                    continue
                
                processed_pairs.add(pair_key)
                
                similarity = self._calculate_content_similarity(item1, item2)
                
                if similarity > 0.8:  # High similarity threshold
                    duplicate_type = 'exact' if similarity > 0.95 else 'near_duplicate'
                    
                    duplicates.append(DuplicateItem(
                        item1_id=item1.get('id', ''),
                        item2_id=item2.get('id', ''),
                        similarity_score=similarity,
                        duplicate_type=duplicate_type,
                        recommended_action=self._recommend_duplicate_action(item1, item2, similarity)
                    ))
        
        return duplicates
    
    async def _auto_archive_items(self, candidates: List[Dict[str, Any]]) -> List[str]:
        """Automatically archive candidate items."""
        archived_items = []
        
        for candidate in candidates:
            if candidate['confidence'] > 0.8:  # High confidence threshold
                item = candidate['item']
                
                try:
                    result = await self.archive_item(item, f"auto: {', '.join(candidate['reasons'])}")
                    
                    if result['status'] == 'success':
                        archived_items.append(item.get('id', ''))
                        
                except Exception as e:
                    logger.error(f"Error auto-archiving item {item.get('id')}: {e}")
        
        return archived_items
    
    async def _save_to_archive(self, archive_item: ArchiveItem, original_item: Dict[str, Any]) -> Path:
        """Save an item to the archive."""
        # Create directory structure: archive/year/month/content_type/
        archive_date = archive_item.archived_at
        archive_dir = (
            self.archive_path / 
            str(archive_date.year) / 
            f"{archive_date.month:02d}" / 
            archive_item.content_type
        )
        archive_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename
        filename = f"{archive_item.id}_{archive_item.content_hash[:8]}.json"
        file_path = archive_dir / filename
        
        # Prepare archive data
        archive_data = {
            'id': archive_item.id,
            'content_type': archive_item.content_type,
            'title': archive_item.title,
            'content_hash': archive_item.content_hash,
            'metadata': archive_item.metadata,
            'archived_at': archive_item.archived_at.isoformat(),
            'original_created_at': archive_item.original_created_at.isoformat() if archive_item.original_created_at else None,
            'tags': archive_item.tags,
            'importance_score': archive_item.importance_score,
            'original_data': original_item  # Store complete original data
        }
        
        # Save to file
        import json
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(archive_data, f, indent=2, ensure_ascii=False)
        
        return file_path
    
    def _generate_content_hash(self, item: Dict[str, Any]) -> str:
        """Generate a hash for the item content."""
        content = str(item.get('content', '')) + str(item.get('title', ''))
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse date string to datetime object."""
        if not date_str:
            return None
        
        try:
            return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        except:
            return None
    
    def _calculate_archive_confidence(self, item: Dict[str, Any], reasons: List[str]) -> float:
        """Calculate confidence score for archiving decision."""
        base_confidence = 0.5
        
        # More reasons = higher confidence
        confidence = base_confidence + (len(reasons) * 0.2)
        
        # Boost confidence for completed tasks
        if item.get('type') == 'task' and item.get('status') == 'completed':
            confidence += 0.3
        
        # Boost confidence for very old items
        if 'older than' in ' '.join(reasons):
            confidence += 0.2
        
        return min(confidence, 1.0)
    
    def _calculate_content_similarity(self, item1: Dict[str, Any], item2: Dict[str, Any]) -> float:
        """Calculate similarity between two content items."""
        # Simple similarity based on title and content
        content1 = (item1.get('title', '') + ' ' + item1.get('content', '')).lower()
        content2 = (item2.get('title', '') + ' ' + item2.get('content', '')).lower()
        
        if not content1 or not content2:
            return 0.0
        
        # Exact match
        if content1 == content2:
            return 1.0
        
        # Jaccard similarity on words
        words1 = set(content1.split())
        words2 = set(content2.split())
        
        if not words1 and not words2:
            return 1.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def _recommend_duplicate_action(self, item1: Dict[str, Any], item2: Dict[str, Any], similarity: float) -> str:
        """Recommend action for duplicate items."""
        if similarity > 0.95:
            # Nearly identical - keep newer one
            date1 = self._parse_date(item1.get('created_at'))
            date2 = self._parse_date(item2.get('created_at'))
            
            if date1 and date2:
                return 'archive_older'
            else:
                return 'merge'
        else:
            return 'keep_both'  # Different enough to keep both
    
    def _calculate_relevance(self, query: str, text: str) -> float:
        """Calculate relevance score for search results."""
        query_words = set(query.lower().split())
        text_words = set(text.lower().split())
        
        if not query_words:
            return 0.0
        
        matches = len(query_words & text_words)
        return matches / len(query_words)
    
    async def _calculate_storage_saved(self, archived_items: List[str]) -> float:
        """Calculate storage saved by archiving items."""
        # Simplified calculation - in reality you'd measure actual file sizes
        return len(archived_items) * 0.1  # Assume 0.1 MB per item


# Create global instance
archivist_agent = ArchivistAgent()
