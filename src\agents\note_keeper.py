"""Note Keeper Agent for the Assistant."""

import re
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any, Set, Tuple
from dataclasses import dataclass

from ..core.logging import logger
from ..core.config import settings


@dataclass
class NoteLink:
    """Represents a link between notes."""
    source_note_id: str
    target_note_id: str
    link_type: str  # 'reference', 'related', 'follow_up', 'contradiction'
    confidence: float
    context: str


@dataclass
class NoteSuggestion:
    """Represents a suggestion for note improvement."""
    note_id: str
    suggestion_type: str  # 'add_tags', 'link_notes', 'categorize', 'merge'
    message: str
    confidence: float
    suggested_changes: Dict[str, Any]


class NoteKeeperAgent:
    """
    Agent responsible for intelligent note management and organization.
    
    Key responsibilities:
    - Automatically link related notes based on content similarity
    - Suggest tags and categories for notes
    - Detect duplicate or similar notes
    - Extract key concepts and entities from notes
    - Maintain note relationships and knowledge graph
    """
    
    def __init__(self):
        self.config = settings.agents.note_keeper
        self.auto_link = getattr(self.config, 'auto_link', True)
        
        # Common stop words to ignore in content analysis
        self.stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we',
            'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her',
            'its', 'our', 'their'
        }
    
    async def process_notes(self, notes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process notes for organization and linking.
        
        Args:
            notes: List of note dictionaries
            
        Returns:
            Dictionary with processing results
        """
        logger.info(f"Processing {len(notes)} notes for organization")
        
        result = {
            'status': 'success',
            'notes_processed': len(notes),
            'links_created': 0,
            'tags_suggested': 0,
            'categories_assigned': 0,
            'duplicates_found': 0,
            'errors': []
        }
        
        try:
            # Auto-link related notes
            if self.auto_link:
                links = await self._auto_link_notes(notes)
                result['links_created'] = len(links)
            
            # Generate tag suggestions
            tag_suggestions = await self._suggest_tags(notes)
            result['tags_suggested'] = len(tag_suggestions)
            
            # Auto-categorize notes
            categories = await self._auto_categorize_notes(notes)
            result['categories_assigned'] = len(categories)
            
            # Detect duplicates
            duplicates = await self._detect_duplicate_notes(notes)
            result['duplicates_found'] = len(duplicates)
            
        except Exception as e:
            logger.error(f"Error processing notes: {e}")
            result['errors'].append(str(e))
            result['status'] = 'error'
        
        logger.info(f"Note processing completed: {result}")
        return result
    
    async def analyze_note_content(self, note: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze a single note's content for insights.
        
        Args:
            note: Note dictionary
            
        Returns:
            Dictionary with analysis results
        """
        try:
            content = note.get('content', '')
            title = note.get('title', '')
            
            # Extract key concepts
            concepts = self._extract_key_concepts(content)
            
            # Extract entities (simple implementation)
            entities = self._extract_entities(content)
            
            # Suggest tags
            suggested_tags = self._generate_tags_for_note(note)
            
            # Determine category
            category = self._determine_category(note)
            
            # Calculate importance score
            importance = self._calculate_note_importance(note)
            
            return {
                'status': 'success',
                'note_id': note.get('id'),
                'key_concepts': concepts,
                'entities': entities,
                'suggested_tags': suggested_tags,
                'suggested_category': category,
                'importance_score': importance,
                'word_count': len(content.split()),
                'reading_time_minutes': max(1, len(content.split()) // 200)  # ~200 words per minute
            }
            
        except Exception as e:
            logger.error(f"Error analyzing note content: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def suggest_note_links(self, note_id: str, all_notes: List[Dict[str, Any]]) -> List[NoteLink]:
        """
        Suggest links for a specific note.
        
        Args:
            note_id: ID of the note to find links for
            all_notes: List of all available notes
            
        Returns:
            List of suggested note links
        """
        target_note = next((n for n in all_notes if n.get('id') == note_id), None)
        if not target_note:
            return []
        
        links = []
        
        for note in all_notes:
            if note.get('id') == note_id:
                continue
            
            # Calculate content similarity
            similarity = self._calculate_content_similarity(target_note, note)
            
            if similarity > 0.3:  # 30% similarity threshold
                link_type = self._determine_link_type(target_note, note, similarity)
                
                links.append(NoteLink(
                    source_note_id=note_id,
                    target_note_id=note.get('id'),
                    link_type=link_type,
                    confidence=similarity,
                    context=self._generate_link_context(target_note, note)
                ))
        
        # Sort by confidence
        links.sort(key=lambda x: x.confidence, reverse=True)
        
        return links[:10]  # Return top 10 suggestions
    
    async def _auto_link_notes(self, notes: List[Dict[str, Any]]) -> List[NoteLink]:
        """Automatically create links between related notes."""
        links = []
        
        for i, note1 in enumerate(notes):
            for note2 in notes[i+1:]:
                similarity = self._calculate_content_similarity(note1, note2)
                
                if similarity > 0.4:  # Higher threshold for auto-linking
                    link_type = self._determine_link_type(note1, note2, similarity)
                    
                    links.append(NoteLink(
                        source_note_id=note1.get('id'),
                        target_note_id=note2.get('id'),
                        link_type=link_type,
                        confidence=similarity,
                        context=self._generate_link_context(note1, note2)
                    ))
        
        return links
    
    async def _suggest_tags(self, notes: List[Dict[str, Any]]) -> List[NoteSuggestion]:
        """Generate tag suggestions for notes."""
        suggestions = []
        
        for note in notes:
            current_tags = set(note.get('tags', []))
            suggested_tags = set(self._generate_tags_for_note(note))
            
            new_tags = suggested_tags - current_tags
            
            if new_tags:
                suggestions.append(NoteSuggestion(
                    note_id=note.get('id'),
                    suggestion_type='add_tags',
                    message=f"Suggested tags: {', '.join(new_tags)}",
                    confidence=0.7,
                    suggested_changes={'tags': list(current_tags | new_tags)}
                ))
        
        return suggestions
    
    async def _auto_categorize_notes(self, notes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Automatically categorize notes."""
        categorized = []
        
        for note in notes:
            if not note.get('category') or note.get('category') == 'general':
                category = self._determine_category(note)
                
                if category != 'general':
                    categorized.append({
                        'note_id': note.get('id'),
                        'suggested_category': category,
                        'confidence': 0.8
                    })
        
        return categorized
    
    async def _detect_duplicate_notes(self, notes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect potential duplicate notes."""
        duplicates = []
        processed_pairs = set()
        
        for i, note1 in enumerate(notes):
            for j, note2 in enumerate(notes[i+1:], i+1):
                pair_key = tuple(sorted([note1.get('id'), note2.get('id')]))
                if pair_key in processed_pairs:
                    continue
                
                processed_pairs.add(pair_key)
                
                similarity = self._calculate_content_similarity(note1, note2)
                
                if similarity > 0.8:  # High similarity indicates potential duplicate
                    duplicates.append({
                        'note1_id': note1.get('id'),
                        'note2_id': note2.get('id'),
                        'similarity_score': similarity,
                        'suggested_action': 'merge' if similarity > 0.9 else 'review'
                    })
        
        return duplicates
    
    def _extract_key_concepts(self, content: str) -> List[str]:
        """Extract key concepts from note content."""
        # Simple keyword extraction based on frequency and length
        words = re.findall(r'\b[a-zA-Z]{3,}\b', content.lower())
        
        # Filter out stop words
        filtered_words = [word for word in words if word not in self.stop_words]
        
        # Count frequency
        word_freq = {}
        for word in filtered_words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # Get top concepts (words that appear more than once or are long)
        concepts = []
        for word, freq in word_freq.items():
            if freq > 1 or len(word) > 6:
                concepts.append(word)
        
        # Sort by frequency and return top 10
        concepts.sort(key=lambda x: word_freq[x], reverse=True)
        return concepts[:10]
    
    def _extract_entities(self, content: str) -> Dict[str, List[str]]:
        """Extract entities from content (simple implementation)."""
        entities = {
            'dates': [],
            'emails': [],
            'urls': [],
            'phone_numbers': [],
            'capitalized_terms': []
        }
        
        # Extract dates (simple patterns)
        date_patterns = [
            r'\b\d{1,2}/\d{1,2}/\d{4}\b',
            r'\b\d{4}-\d{2}-\d{2}\b',
            r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \d{1,2},? \d{4}\b'
        ]
        
        for pattern in date_patterns:
            entities['dates'].extend(re.findall(pattern, content, re.IGNORECASE))
        
        # Extract emails
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        entities['emails'] = re.findall(email_pattern, content)
        
        # Extract URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        entities['urls'] = re.findall(url_pattern, content)
        
        # Extract phone numbers (simple pattern)
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        entities['phone_numbers'] = re.findall(phone_pattern, content)
        
        # Extract capitalized terms (potential proper nouns)
        cap_pattern = r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b'
        entities['capitalized_terms'] = re.findall(cap_pattern, content)
        
        return entities
    
    def _generate_tags_for_note(self, note: Dict[str, Any]) -> List[str]:
        """Generate appropriate tags for a note."""
        content = (note.get('content', '') + ' ' + note.get('title', '')).lower()
        tags = []
        
        # Technology tags
        tech_keywords = {
            'python': ['python', 'django', 'flask', 'pandas'],
            'javascript': ['javascript', 'js', 'node', 'react', 'vue'],
            'database': ['sql', 'database', 'mysql', 'postgresql', 'mongodb'],
            'ai': ['ai', 'machine learning', 'ml', 'neural', 'model'],
            'web': ['html', 'css', 'web', 'frontend', 'backend']
        }
        
        for tag, keywords in tech_keywords.items():
            if any(keyword in content for keyword in keywords):
                tags.append(tag)
        
        # Project tags
        project_keywords = {
            'meeting': ['meeting', 'discussion', 'call', 'conference'],
            'idea': ['idea', 'concept', 'brainstorm', 'thought'],
            'todo': ['todo', 'task', 'action', 'need to'],
            'research': ['research', 'study', 'analysis', 'investigation'],
            'documentation': ['documentation', 'docs', 'manual', 'guide']
        }
        
        for tag, keywords in project_keywords.items():
            if any(keyword in content for keyword in keywords):
                tags.append(tag)
        
        # Priority tags
        if any(word in content for word in ['urgent', 'important', 'critical', 'asap']):
            tags.append('high-priority')
        
        return tags
    
    def _determine_category(self, note: Dict[str, Any]) -> str:
        """Determine the most appropriate category for a note."""
        content = (note.get('content', '') + ' ' + note.get('title', '')).lower()
        
        # Category keywords
        categories = {
            'work': ['work', 'project', 'meeting', 'client', 'business', 'office'],
            'personal': ['personal', 'family', 'home', 'hobby', 'vacation'],
            'learning': ['learn', 'study', 'course', 'tutorial', 'education', 'training'],
            'ideas': ['idea', 'concept', 'brainstorm', 'innovation', 'creative'],
            'technical': ['code', 'programming', 'development', 'technical', 'software'],
            'research': ['research', 'analysis', 'investigation', 'findings', 'data']
        }
        
        category_scores = {}
        for category, keywords in categories.items():
            score = sum(1 for keyword in keywords if keyword in content)
            if score > 0:
                category_scores[category] = score
        
        if category_scores:
            return max(category_scores, key=category_scores.get)
        
        return 'general'
    
    def _calculate_note_importance(self, note: Dict[str, Any]) -> float:
        """Calculate importance score for a note."""
        score = 0.5  # Base score
        
        content = note.get('content', '')
        title = note.get('title', '')
        
        # Length factor (longer notes might be more important)
        word_count = len(content.split())
        if word_count > 500:
            score += 0.2
        elif word_count > 200:
            score += 0.1
        
        # Urgency keywords
        urgent_keywords = ['urgent', 'important', 'critical', 'deadline', 'asap']
        if any(keyword in (content + title).lower() for keyword in urgent_keywords):
            score += 0.3
        
        # Recent creation/update
        if note.get('updated_at'):
            try:
                updated = datetime.fromisoformat(note['updated_at'].replace('Z', '+00:00'))
                days_since_update = (datetime.now() - updated).days
                
                if days_since_update < 7:
                    score += 0.1
            except:
                pass
        
        return min(score, 1.0)
    
    def _calculate_content_similarity(self, note1: Dict[str, Any], note2: Dict[str, Any]) -> float:
        """Calculate similarity between two notes."""
        content1 = (note1.get('content', '') + ' ' + note1.get('title', '')).lower()
        content2 = (note2.get('content', '') + ' ' + note2.get('title', '')).lower()
        
        # Extract words
        words1 = set(re.findall(r'\b[a-zA-Z]{3,}\b', content1))
        words2 = set(re.findall(r'\b[a-zA-Z]{3,}\b', content2))
        
        # Filter stop words
        words1 = {word for word in words1 if word not in self.stop_words}
        words2 = {word for word in words2 if word not in self.stop_words}
        
        if not words1 and not words2:
            return 0.0
        
        # Jaccard similarity
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def _determine_link_type(self, note1: Dict[str, Any], note2: Dict[str, Any], similarity: float) -> str:
        """Determine the type of link between two notes."""
        if similarity > 0.7:
            return 'related'
        elif similarity > 0.5:
            return 'reference'
        else:
            return 'related'
    
    def _generate_link_context(self, note1: Dict[str, Any], note2: Dict[str, Any]) -> str:
        """Generate context description for a note link."""
        # Find common concepts
        concepts1 = set(self._extract_key_concepts(note1.get('content', '')))
        concepts2 = set(self._extract_key_concepts(note2.get('content', '')))
        
        common_concepts = concepts1 & concepts2
        
        if common_concepts:
            return f"Related through: {', '.join(list(common_concepts)[:3])}"
        else:
            return "Content similarity detected"


# Create global instance
note_keeper_agent = NoteKeeperAgent()
