{% extends "base.html" %}

{% block content %}
<div class="dashboard">
    <header class="page-header">
        <h1>Knowledge Library Dashboard</h1>
        <p class="page-subtitle">Your personal AI-powered knowledge management system</p>
    </header>

    <!-- Status Cards -->
    <div class="status-grid">
        <div class="status-card">
            <div class="status-icon">👥</div>
            <div class="status-content">
                <h3>Contacts</h3>
                <div class="status-number" id="documentCount">-</div>
                <p class="status-label">Total contacts</p>
            </div>
        </div>
        
        <div class="status-card">
            <div class="status-icon">📝</div>
            <div class="status-content">
                <h3>Notes</h3>
                <div class="status-number" id="noteCount">-</div>
                <p class="status-label">Personal notes</p>
            </div>
        </div>
        
        <div class="status-card">
            <div class="status-icon">✅</div>
            <div class="status-content">
                <h3>Tasks</h3>
                <div class="status-number" id="taskCount">-</div>
                <p class="status-label">Active tasks</p>
            </div>
        </div>
        
        <div class="status-card">
            <div class="status-icon">📁</div>
            <div class="status-content">
                <h3>Projects</h3>
                <div class="status-number" id="projectCount">-</div>
                <p class="status-label">Active projects</p>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <section class="quick-actions">
        <h2>Quick Actions</h2>
        <div class="action-grid">
            <a href="/upload" class="action-card">
                <div class="action-icon">📤</div>
                <h3>Upload Documents</h3>
                <p>Add new documents to your knowledge base</p>
            </a>
            
            <a href="/search" class="action-card">
                <div class="action-icon">🔍</div>
                <h3>Search Knowledge</h3>
                <p>Find information across all your documents</p>
            </a>
            
            <a href="/notes" class="action-card">
                <div class="action-icon">📝</div>
                <h3>Create Note</h3>
                <p>Capture thoughts and organize ideas</p>
            </a>
            
            <a href="/chat" class="action-card">
                <div class="action-icon">💬</div>
                <h3>Chat with AI</h3>
                <p>Ask questions and get intelligent responses</p>
            </a>
        </div>
    </section>

    <!-- Recent Activity -->
    <section class="recent-activity">
        <h2>Recent Activity</h2>
        <div class="activity-list" id="recentActivity">
            <div class="activity-item">
                <div class="activity-icon">📄</div>
                <div class="activity-content">
                    <p class="activity-title">System initialized</p>
                    <p class="activity-time">Just now</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Agent Status -->
    <section class="agent-status">
        <h2>AI Agents</h2>
        <div class="agent-grid" id="agentStatus">
            <!-- Agent status will be populated by JavaScript -->
        </div>
    </section>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Load dashboard data
document.addEventListener('DOMContentLoaded', async function() {
    try {
        // Load system status
        const response = await fetch('/api/status');
        const data = await response.json();

        // Update agent status
        const agentGrid = document.getElementById('agentStatus');
        const agents = data.agents_enabled;

        agentGrid.innerHTML = '';
        for (const [agentName, enabled] of Object.entries(agents)) {
            const agentCard = document.createElement('div');
            agentCard.className = `agent-card ${enabled ? 'enabled' : 'disabled'}`;
            agentCard.innerHTML = `
                <div class="agent-icon">${enabled ? '✅' : '❌'}</div>
                <h3>${agentName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</h3>
                <p class="agent-status">${enabled ? 'Active' : 'Disabled'}</p>
            `;
            agentGrid.appendChild(agentCard);
        }

        // Load real data from APIs
        await loadDashboardStats();

    } catch (error) {
        console.error('Failed to load dashboard data:', error);
    }
});

async function loadDashboardStats() {
    try {
        // Load task stats
        const taskResponse = await fetch('/api/tasks/stats');
        if (taskResponse.ok) {
            const taskData = await taskResponse.json();
            if (taskData.status === 'success') {
                document.getElementById('taskCount').textContent = taskData.stats.total || 0;
            }
        }

        // Load contact stats
        const contactResponse = await fetch('/api/contacts');
        if (contactResponse.ok) {
            const contactData = await contactResponse.json();
            if (contactData.status === 'success') {
                // Update document count with contact count for now
                document.getElementById('documentCount').textContent = contactData.total || 0;
            }
        }

        // Load notes stats
        const notesResponse = await fetch('/api/notes');
        if (notesResponse.ok) {
            const notesData = await notesResponse.json();
            if (notesData.status === 'success') {
                document.getElementById('noteCount').textContent = notesData.notes.length || 0;
            }
        }

        // Load project stats
        const projectResponse = await fetch('/api/projects/stats');
        if (projectResponse.ok) {
            const projectData = await projectResponse.json();
            if (projectData.status === 'success') {
                document.getElementById('projectCount').textContent = projectData.stats.active || 0;
            }
        }

        // Update recent activity with real data
        updateRecentActivity();

    } catch (error) {
        console.error('Error loading dashboard stats:', error);
    }
}

function updateRecentActivity() {
    const activityList = document.getElementById('recentActivity');

    // Add some dynamic activity items
    const activities = [
        {
            icon: '📧',
            title: 'Email system ready',
            time: 'System initialized'
        },
        {
            icon: '👥',
            title: 'Contact management active',
            time: 'Ready to manage contacts'
        },
        {
            icon: '✅',
            title: 'Task system operational',
            time: 'AI task extraction enabled'
        },
        {
            icon: '🤖',
            title: 'AI assistant ready',
            time: 'Chat and analysis available'
        }
    ];

    activityList.innerHTML = activities.map(activity => `
        <div class="activity-item">
            <div class="activity-icon">${activity.icon}</div>
            <div class="activity-content">
                <p class="activity-title">${activity.title}</p>
                <p class="activity-time">${activity.time}</p>
            </div>
        </div>
    `).join('');
}
</script>
{% endblock %}
