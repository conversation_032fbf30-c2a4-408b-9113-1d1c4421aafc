"""Project Manager Agent for the Assistant."""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.logging import logger
from ..core.config import settings


class ProjectStatus(Enum):
    """Project status enumeration."""
    PLANNING = "planning"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class ProjectPriority(Enum):
    """Project priority enumeration."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class ProjectInsight:
    """Represents an insight about a project."""
    project_id: str
    insight_type: str  # 'deadline_risk', 'resource_conflict', 'progress_slow', 'milestone_achieved'
    message: str
    confidence: float
    suggested_action: Optional[str] = None
    urgency: str = 'medium'  # 'low', 'medium', 'high'


@dataclass
class ProjectRecommendation:
    """Represents a recommendation for project optimization."""
    project_id: str
    recommendation_type: str  # 'priority_change', 'deadline_extension', 'resource_reallocation'
    reasoning: str
    confidence: float
    suggested_changes: Dict[str, Any]


class ProjectManagerAgent:
    """
    Agent responsible for intelligent project management and optimization.
    
    Key responsibilities:
    - Monitor project progress and deadlines
    - Identify resource conflicts and bottlenecks
    - Suggest project prioritization based on business value
    - Track milestone achievements and delays
    - Generate project health reports and insights
    """
    
    def __init__(self):
        self.config = settings.agents.project_manager
        self.monitoring_enabled = getattr(self.config, 'monitoring_enabled', True)
        self.auto_insights = getattr(self.config, 'auto_insights', True)
        
    async def process_projects(self, projects: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process all projects for optimization and insights.
        
        Args:
            projects: List of project dictionaries
            
        Returns:
            Dictionary with processing results
        """
        logger.info(f"Processing {len(projects)} projects for optimization")
        
        result = {
            'status': 'success',
            'projects_processed': len(projects),
            'insights_generated': 0,
            'recommendations_made': 0,
            'health_issues_detected': 0,
            'milestones_tracked': 0,
            'errors': []
        }
        
        try:
            # Generate project insights
            if self.auto_insights:
                insights = await self._generate_project_insights(projects)
                result['insights_generated'] = len(insights)
            
            # Create recommendations
            recommendations = await self._generate_recommendations(projects)
            result['recommendations_made'] = len(recommendations)
            
            # Check project health
            health_issues = await self._check_project_health(projects)
            result['health_issues_detected'] = len(health_issues)
            
            # Track milestones
            milestone_updates = await self._track_milestones(projects)
            result['milestones_tracked'] = len(milestone_updates)
            
        except Exception as e:
            logger.error(f"Error processing projects: {e}")
            result['errors'].append(str(e))
            result['status'] = 'error'
        
        logger.info(f"Project processing completed: {result}")
        return result
    
    async def analyze_project_health(self, project_id: str, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze the health of a specific project.
        
        Args:
            project_id: Project ID to analyze
            project_data: Project data dictionary
            
        Returns:
            Dictionary with health analysis
        """
        try:
            health_score = await self._calculate_health_score(project_data)
            risk_factors = await self._identify_risk_factors(project_data)
            progress_analysis = await self._analyze_progress(project_data)
            resource_analysis = await self._analyze_resources(project_data)
            
            return {
                'status': 'success',
                'project_id': project_id,
                'health_score': health_score,
                'health_status': self._get_health_status(health_score),
                'risk_factors': risk_factors,
                'progress_analysis': progress_analysis,
                'resource_analysis': resource_analysis,
                'recommendations': await self._generate_health_recommendations(project_data, health_score, risk_factors)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing project health for {project_id}: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def suggest_project_prioritization(self, projects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Suggest optimal prioritization for projects.
        
        Args:
            projects: List of project dictionaries
            
        Returns:
            List of prioritization suggestions
        """
        try:
            prioritized_projects = []
            
            for project in projects:
                priority_score = await self._calculate_priority_score(project)
                business_value = await self._calculate_business_value(project)
                urgency_score = await self._calculate_urgency_score(project)
                
                prioritized_projects.append({
                    'project_id': project.get('id'),
                    'current_priority': project.get('priority', 2),
                    'suggested_priority': self._score_to_priority(priority_score),
                    'priority_score': priority_score,
                    'business_value': business_value,
                    'urgency_score': urgency_score,
                    'reasoning': self._generate_priority_reasoning(project, priority_score, business_value, urgency_score)
                })
            
            # Sort by priority score
            prioritized_projects.sort(key=lambda x: x['priority_score'], reverse=True)
            
            return prioritized_projects
            
        except Exception as e:
            logger.error(f"Error suggesting project prioritization: {e}")
            return []
    
    async def track_project_milestones(self, project_id: str, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Track milestones for a specific project.
        
        Args:
            project_id: Project ID
            project_data: Project data dictionary
            
        Returns:
            Dictionary with milestone tracking results
        """
        try:
            milestones = project_data.get('milestones', [])
            
            milestone_status = {
                'total_milestones': len(milestones),
                'completed_milestones': 0,
                'overdue_milestones': 0,
                'upcoming_milestones': 0,
                'milestone_details': []
            }
            
            now = datetime.now()
            
            for milestone in milestones:
                milestone_info = {
                    'id': milestone.get('id'),
                    'title': milestone.get('title'),
                    'due_date': milestone.get('due_date'),
                    'status': milestone.get('status', 'pending'),
                    'completion_date': milestone.get('completion_date')
                }
                
                if milestone.get('status') == 'completed':
                    milestone_status['completed_milestones'] += 1
                    milestone_info['days_to_complete'] = self._calculate_completion_time(milestone)
                elif milestone.get('due_date'):
                    due_date = datetime.fromisoformat(milestone['due_date'].replace('Z', '+00:00'))
                    days_until_due = (due_date - now).days
                    
                    if days_until_due < 0:
                        milestone_status['overdue_milestones'] += 1
                        milestone_info['days_overdue'] = abs(days_until_due)
                    elif days_until_due <= 30:
                        milestone_status['upcoming_milestones'] += 1
                        milestone_info['days_until_due'] = days_until_due
                
                milestone_status['milestone_details'].append(milestone_info)
            
            # Calculate completion rate
            completion_rate = (milestone_status['completed_milestones'] / milestone_status['total_milestones']) * 100 if milestone_status['total_milestones'] > 0 else 0
            
            return {
                'status': 'success',
                'project_id': project_id,
                'milestone_status': milestone_status,
                'completion_rate': completion_rate,
                'project_health': self._assess_milestone_health(milestone_status)
            }
            
        except Exception as e:
            logger.error(f"Error tracking milestones for project {project_id}: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def _generate_project_insights(self, projects: List[Dict[str, Any]]) -> List[ProjectInsight]:
        """Generate insights for projects."""
        insights = []
        
        for project in projects:
            try:
                # Check deadline risks
                if project.get('due_date'):
                    due_date = datetime.fromisoformat(project['due_date'].replace('Z', '+00:00'))
                    days_until_due = (due_date - datetime.now()).days
                    
                    if days_until_due < 0:
                        insights.append(ProjectInsight(
                            project_id=project.get('id'),
                            insight_type='deadline_risk',
                            message=f"Project '{project.get('name')}' is {abs(days_until_due)} days overdue",
                            confidence=1.0,
                            suggested_action='review_and_reschedule',
                            urgency='high'
                        ))
                    elif days_until_due <= 7:
                        insights.append(ProjectInsight(
                            project_id=project.get('id'),
                            insight_type='deadline_risk',
                            message=f"Project '{project.get('name')}' is due in {days_until_due} days",
                            confidence=0.9,
                            suggested_action='prioritize_tasks',
                            urgency='high'
                        ))
                
                # Check progress
                progress = project.get('progress_percentage', 0)
                if progress < 20 and project.get('status') == 'active':
                    insights.append(ProjectInsight(
                        project_id=project.get('id'),
                        insight_type='progress_slow',
                        message=f"Project '{project.get('name')}' has low progress ({progress}%)",
                        confidence=0.8,
                        suggested_action='review_blockers',
                        urgency='medium'
                    ))
                
            except Exception as e:
                logger.error(f"Error generating insights for project {project.get('id')}: {e}")
        
        return insights
    
    async def _generate_recommendations(self, projects: List[Dict[str, Any]]) -> List[ProjectRecommendation]:
        """Generate recommendations for projects."""
        recommendations = []
        
        # Analyze all projects for patterns
        active_projects = [p for p in projects if p.get('status') == 'active']
        
        # Check for overloaded periods
        for project in active_projects:
            if project.get('due_date'):
                due_date = datetime.fromisoformat(project['due_date'].replace('Z', '+00:00'))
                days_until_due = (due_date - datetime.now()).days
                
                if days_until_due <= 14 and project.get('priority', 2) < 3:
                    recommendations.append(ProjectRecommendation(
                        project_id=project.get('id'),
                        recommendation_type='priority_change',
                        reasoning='Project deadline is approaching',
                        confidence=0.8,
                        suggested_changes={'priority': 3}
                    ))
        
        return recommendations
    
    async def _check_project_health(self, projects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Check health issues across projects."""
        health_issues = []
        
        for project in projects:
            health_score = await self._calculate_health_score(project)
            
            if health_score < 0.6:  # Health score below 60%
                health_issues.append({
                    'project_id': project.get('id'),
                    'health_score': health_score,
                    'issues': await self._identify_health_issues(project, health_score)
                })
        
        return health_issues
    
    async def _track_milestones(self, projects: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Track milestone updates across projects."""
        milestone_updates = []
        
        for project in projects:
            milestones = project.get('milestones', [])
            
            for milestone in milestones:
                if milestone.get('due_date'):
                    due_date = datetime.fromisoformat(milestone['due_date'].replace('Z', '+00:00'))
                    days_until_due = (due_date - datetime.now()).days
                    
                    if days_until_due == 0:  # Due today
                        milestone_updates.append({
                            'project_id': project.get('id'),
                            'milestone_id': milestone.get('id'),
                            'update_type': 'due_today',
                            'message': f"Milestone '{milestone.get('title')}' is due today"
                        })
        
        return milestone_updates
    
    async def _calculate_health_score(self, project: Dict[str, Any]) -> float:
        """Calculate overall health score for a project."""
        score = 0.5  # Base score
        
        # Progress factor
        progress = project.get('progress_percentage', 0)
        if progress >= 80:
            score += 0.3
        elif progress >= 50:
            score += 0.2
        elif progress >= 20:
            score += 0.1
        
        # Deadline factor
        if project.get('due_date'):
            due_date = datetime.fromisoformat(project['due_date'].replace('Z', '+00:00'))
            days_until_due = (due_date - datetime.now()).days
            
            if days_until_due > 30:
                score += 0.2
            elif days_until_due > 7:
                score += 0.1
            elif days_until_due < 0:
                score -= 0.3  # Overdue penalty
        
        # Status factor
        status = project.get('status', 'active')
        if status == 'completed':
            score = 1.0
        elif status == 'on_hold':
            score -= 0.2
        elif status == 'cancelled':
            score = 0.0
        
        return max(0.0, min(1.0, score))
    
    async def _identify_risk_factors(self, project: Dict[str, Any]) -> List[str]:
        """Identify risk factors for a project."""
        risks = []
        
        # Deadline risk
        if project.get('due_date'):
            due_date = datetime.fromisoformat(project['due_date'].replace('Z', '+00:00'))
            days_until_due = (due_date - datetime.now()).days
            
            if days_until_due < 0:
                risks.append('Project is overdue')
            elif days_until_due <= 7:
                risks.append('Tight deadline approaching')
        
        # Progress risk
        progress = project.get('progress_percentage', 0)
        if progress < 20 and project.get('status') == 'active':
            risks.append('Low progress for active project')
        
        # Resource risk (simplified)
        if not project.get('assigned_team'):
            risks.append('No team assigned')
        
        return risks
    
    def _get_health_status(self, health_score: float) -> str:
        """Convert health score to status."""
        if health_score >= 0.8:
            return 'excellent'
        elif health_score >= 0.6:
            return 'good'
        elif health_score >= 0.4:
            return 'fair'
        else:
            return 'poor'
    
    async def _calculate_priority_score(self, project: Dict[str, Any]) -> float:
        """Calculate priority score for a project."""
        score = 0.5  # Base score
        
        # Deadline urgency
        if project.get('due_date'):
            due_date = datetime.fromisoformat(project['due_date'].replace('Z', '+00:00'))
            days_until_due = (due_date - datetime.now()).days
            
            if days_until_due <= 0:
                score += 0.4
            elif days_until_due <= 7:
                score += 0.3
            elif days_until_due <= 30:
                score += 0.2
        
        # Business value
        business_value = await self._calculate_business_value(project)
        score += business_value * 0.3
        
        # Current priority
        current_priority = project.get('priority', 2)
        score += (current_priority / 4) * 0.2
        
        return min(1.0, score)
    
    async def _calculate_business_value(self, project: Dict[str, Any]) -> float:
        """Calculate business value score for a project."""
        # Simplified business value calculation
        value = 0.5
        
        # Check for business value indicators in description
        description = project.get('description', '').lower()
        
        high_value_keywords = ['revenue', 'customer', 'critical', 'strategic']
        if any(keyword in description for keyword in high_value_keywords):
            value += 0.3
        
        # Project type factor
        project_type = project.get('type', '').lower()
        if project_type in ['strategic', 'customer-facing', 'revenue-generating']:
            value += 0.2
        
        return min(1.0, value)
    
    async def _calculate_urgency_score(self, project: Dict[str, Any]) -> float:
        """Calculate urgency score for a project."""
        urgency = 0.5
        
        if project.get('due_date'):
            due_date = datetime.fromisoformat(project['due_date'].replace('Z', '+00:00'))
            days_until_due = (due_date - datetime.now()).days
            
            if days_until_due <= 0:
                urgency = 1.0
            elif days_until_due <= 7:
                urgency = 0.9
            elif days_until_due <= 30:
                urgency = 0.7
            elif days_until_due <= 90:
                urgency = 0.5
            else:
                urgency = 0.3
        
        return urgency
    
    def _score_to_priority(self, score: float) -> int:
        """Convert priority score to priority level."""
        if score >= 0.8:
            return ProjectPriority.CRITICAL.value
        elif score >= 0.6:
            return ProjectPriority.HIGH.value
        elif score >= 0.4:
            return ProjectPriority.MEDIUM.value
        else:
            return ProjectPriority.LOW.value
    
    def _generate_priority_reasoning(self, project: Dict[str, Any], priority_score: float, business_value: float, urgency_score: float) -> str:
        """Generate reasoning for priority suggestion."""
        reasons = []
        
        if urgency_score > 0.8:
            reasons.append("high urgency due to deadline")
        if business_value > 0.7:
            reasons.append("high business value")
        if project.get('status') == 'active':
            reasons.append("currently active project")
        
        if not reasons:
            reasons.append("based on overall project analysis")
        
        return f"Priority suggested because of {', '.join(reasons)}"


# Create global instance
project_manager_agent = ProjectManagerAgent()
