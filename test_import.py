#!/usr/bin/env python3
"""
Test imports to identify the issue.
"""

import sys
sys.path.insert(0, '.')

print("Testing imports...")

try:
    print("1. Testing basic imports...")
    import os
    import json
    print("   ✓ Basic imports work")
except Exception as e:
    print(f"   ❌ Basic imports failed: {e}")

try:
    print("2. Testing Google API imports...")
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    print("   ✓ Google API imports work")
except Exception as e:
    print(f"   ❌ Google API imports failed: {e}")

try:
    print("3. Testing core config...")
    from src.core.config import settings
    print("   ✓ Core config works")
except Exception as e:
    print(f"   ❌ Core config failed: {e}")

try:
    print("4. Testing Gmail service...")
    from src.integrations.gmail.gmail_service import GmailService
    print("   ✓ Gmail service import works")
except Exception as e:
    print(f"   ❌ Gmail service import failed: {e}")

try:
    print("5. Testing Gmail integration...")
    from src.integrations.gmail.gmail_integration import GmailIntegration
    print("   ✓ Gmail integration class import works")
except Exception as e:
    print(f"   ❌ Gmail integration class import failed: {e}")

try:
    print("6. Testing Gmail integration instance...")
    from src.integrations.gmail.gmail_integration import gmail_integration
    print("   ✓ Gmail integration instance import works")
except Exception as e:
    print(f"   ❌ Gmail integration instance import failed: {e}")

try:
    print("7. Testing email intelligence service...")
    from src.agents.email_intelligence import email_intelligence_service
    print("   ✓ Email intelligence service import works")
except Exception as e:
    print(f"   ❌ Email intelligence service import failed: {e}")

print("\nImport test complete!")
