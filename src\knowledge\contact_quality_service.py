"""Contact data quality and validation service."""

import re
import json
from datetime import datetime
from typing import List, Optional, Dict, Any, Tu<PERSON>
from sqlalchemy.orm import Session

from ..core.logging import logger
from .database import get_db
from .models import Contact, LearningFeedback


class ContactQualityService:
    """Service for validating and cleaning contact data."""
    
    def __init__(self):
        self.logger = logger
        
        # Phone number patterns for validation
        self.phone_patterns = [
            r'^\+?1?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$',  # US format
            r'^\+?([0-9]{1,4})[-.\s]?([0-9]{3,4})[-.\s]?([0-9]{3,4})[-.\s]?([0-9]{3,4})$',  # International
            r'^([0-9]{10})$',  # 10 digits
            r'^([0-9]{11})$',  # 11 digits with country code
        ]
        
        # Common phone number cleaning patterns
        self.phone_cleanup_patterns = [
            (r'[^\d+()-.\s]', ''),  # Remove non-phone characters
            (r'\s+', ' '),  # Normalize spaces
            (r'^1[-.\s]?', ''),  # Remove leading 1 for US numbers
        ]
        
        # Email validation pattern
        self.email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        # Common data quality issues
        self.quality_checks = [
            'invalid_phone_format',
            'invalid_email_format',
            'duplicate_contact',
            'incomplete_name',
            'suspicious_data',
            'formatting_issues'
        ]
    
    async def validate_contact(self, contact: Contact, session: Optional[Session] = None) -> Dict[str, Any]:
        """Validate a single contact and return quality report."""
        if session is None:
            with next(get_db()) as session:
                return await self._validate_contact_impl(contact, session)
        else:
            return await self._validate_contact_impl(contact, session)
    
    async def _validate_contact_impl(self, contact: Contact, session: Session) -> Dict[str, Any]:
        """Implementation of contact validation."""
        issues = []
        suggestions = []
        
        # Validate email
        if contact.email:
            email_issues = self._validate_email(contact.email)
            issues.extend(email_issues)
        
        # Validate phone numbers
        if contact.phone_numbers:
            phone_issues, phone_suggestions = self._validate_phone_numbers(contact.phone_numbers)
            issues.extend(phone_issues)
            suggestions.extend(phone_suggestions)
        
        # Validate name completeness
        name_issues = self._validate_name(contact.first_name, contact.last_name, contact.display_name)
        issues.extend(name_issues)
        
        # Check for duplicates
        duplicate_issues = await self._check_duplicates(contact, session)
        issues.extend(duplicate_issues)
        
        # Check for suspicious data
        suspicious_issues = self._check_suspicious_data(contact)
        issues.extend(suspicious_issues)
        
        return {
            'contact_id': str(contact.id),
            'issues': issues,
            'suggestions': suggestions,
            'quality_score': self._calculate_quality_score(issues),
            'validated_at': datetime.now().isoformat()
        }
    
    def _validate_email(self, email: str) -> List[Dict[str, Any]]:
        """Validate email format."""
        issues = []
        
        if not re.match(self.email_pattern, email):
            issues.append({
                'type': 'invalid_email_format',
                'severity': 'high',
                'field': 'email',
                'value': email,
                'message': f'Invalid email format: {email}',
                'suggestion': 'Please verify and correct the email address'
            })
        
        return issues
    
    def _validate_phone_numbers(self, phone_numbers: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Validate and suggest fixes for phone numbers."""
        issues = []
        suggestions = []
        
        if isinstance(phone_numbers, str):
            # Handle case where phone_numbers is stored as string instead of JSON
            try:
                phone_numbers = json.loads(phone_numbers)
            except:
                phone_numbers = [{'number': phone_numbers, 'type': 'unknown'}]
        
        for i, phone_entry in enumerate(phone_numbers):
            if isinstance(phone_entry, str):
                phone_number = phone_entry
                phone_type = 'unknown'
            else:
                phone_number = phone_entry.get('number', '')
                phone_type = phone_entry.get('type', 'unknown')
            
            # Check if phone number contains non-numeric characters that shouldn't be there
            if re.search(r'[a-zA-Z]', phone_number):
                issues.append({
                    'type': 'invalid_phone_format',
                    'severity': 'high',
                    'field': f'phone_numbers[{i}]',
                    'value': phone_number,
                    'message': f'Phone number contains letters: {phone_number}',
                    'suggestion': 'Remove letters and keep only numbers and formatting characters'
                })
                
                # Suggest cleaned version
                cleaned = self._clean_phone_number(phone_number)
                if cleaned:
                    suggestions.append({
                        'type': 'phone_cleanup',
                        'field': f'phone_numbers[{i}]',
                        'original': phone_number,
                        'suggested': cleaned,
                        'message': f'Suggested cleanup: {phone_number} → {cleaned}'
                    })
            
            # Validate format
            is_valid = False
            for pattern in self.phone_patterns:
                if re.match(pattern, phone_number.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')):
                    is_valid = True
                    break
            
            if not is_valid and phone_number.strip():
                issues.append({
                    'type': 'invalid_phone_format',
                    'severity': 'medium',
                    'field': f'phone_numbers[{i}]',
                    'value': phone_number,
                    'message': f'Phone number format not recognized: {phone_number}',
                    'suggestion': 'Verify phone number format (e.g., ******-123-4567)'
                })
        
        return issues, suggestions
    
    def _clean_phone_number(self, phone_number: str) -> Optional[str]:
        """Clean a phone number by removing invalid characters."""
        cleaned = phone_number
        
        # Apply cleanup patterns
        for pattern, replacement in self.phone_cleanup_patterns:
            cleaned = re.sub(pattern, replacement, cleaned)
        
        # Remove common text artifacts
        text_artifacts = [
            'ext', 'extension', 'x', 'office', 'home', 'mobile', 'cell',
            'work', 'fax', 'phone', 'tel', 'number', '#'
        ]
        
        for artifact in text_artifacts:
            cleaned = re.sub(rf'\b{artifact}\b', '', cleaned, flags=re.IGNORECASE)
        
        # Clean up extra spaces and punctuation
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        cleaned = re.sub(r'^[-.\s]+|[-.\s]+$', '', cleaned)
        
        # Only return if we have a reasonable number of digits
        digits_only = re.sub(r'[^\d]', '', cleaned)
        if 7 <= len(digits_only) <= 15:  # Reasonable phone number length
            return cleaned
        
        return None
    
    def _validate_name(self, first_name: Optional[str], last_name: Optional[str], display_name: Optional[str]) -> List[Dict[str, Any]]:
        """Validate name completeness."""
        issues = []
        
        if not first_name and not last_name and not display_name:
            issues.append({
                'type': 'incomplete_name',
                'severity': 'medium',
                'field': 'name',
                'message': 'Contact has no name information',
                'suggestion': 'Add at least a display name or first/last name'
            })
        elif display_name and not first_name and not last_name:
            # Try to parse display name into first/last
            if ' ' in display_name.strip():
                parts = display_name.strip().split()
                if len(parts) >= 2:
                    issues.append({
                        'type': 'name_parsing_opportunity',
                        'severity': 'low',
                        'field': 'name',
                        'message': 'Display name could be split into first/last name',
                        'suggestion': f'Consider splitting "{display_name}" into first and last name'
                    })
        
        return issues
    
    async def _check_duplicates(self, contact: Contact, session: Session) -> List[Dict[str, Any]]:
        """Check for potential duplicate contacts."""
        issues = []
        
        if contact.email:
            # Check for exact email match
            duplicate = session.query(Contact).filter(
                Contact.email == contact.email,
                Contact.id != contact.id
            ).first()
            
            if duplicate:
                issues.append({
                    'type': 'duplicate_contact',
                    'severity': 'high',
                    'field': 'email',
                    'value': contact.email,
                    'message': f'Duplicate email found in contact {duplicate.id}',
                    'suggestion': 'Consider merging these contacts',
                    'duplicate_id': str(duplicate.id)
                })
        
        # Check for similar names (fuzzy matching)
        if contact.display_name or (contact.first_name and contact.last_name):
            name_to_check = contact.display_name or f"{contact.first_name} {contact.last_name}"
            
            similar_contacts = session.query(Contact).filter(
                Contact.id != contact.id
            ).all()
            
            for similar in similar_contacts:
                similar_name = similar.display_name or f"{similar.first_name or ''} {similar.last_name or ''}".strip()
                
                if similar_name and self._names_are_similar(name_to_check, similar_name):
                    issues.append({
                        'type': 'potential_duplicate',
                        'severity': 'medium',
                        'field': 'name',
                        'message': f'Similar name found: "{similar_name}" (contact {similar.id})',
                        'suggestion': 'Review for potential duplicate',
                        'similar_contact_id': str(similar.id)
                    })
        
        return issues
    
    def _names_are_similar(self, name1: str, name2: str) -> bool:
        """Check if two names are similar (simple implementation)."""
        # Simple similarity check - could be enhanced with fuzzy matching
        name1_clean = re.sub(r'[^\w\s]', '', name1.lower()).strip()
        name2_clean = re.sub(r'[^\w\s]', '', name2.lower()).strip()
        
        # Check if one name is contained in the other
        if name1_clean in name2_clean or name2_clean in name1_clean:
            return True
        
        # Check if they share significant words
        words1 = set(name1_clean.split())
        words2 = set(name2_clean.split())
        
        if len(words1) > 0 and len(words2) > 0:
            common_words = words1.intersection(words2)
            similarity_ratio = len(common_words) / max(len(words1), len(words2))
            return similarity_ratio > 0.6
        
        return False
    
    def _check_suspicious_data(self, contact: Contact) -> List[Dict[str, Any]]:
        """Check for suspicious or malformed data."""
        issues = []
        
        # Check for obviously fake emails
        if contact.email:
            fake_patterns = [
                r'test@test\.com',
                r'example@example\.com',
                r'noemail@',
                r'@noemail',
                r'fake@fake'
            ]
            
            for pattern in fake_patterns:
                if re.search(pattern, contact.email, re.IGNORECASE):
                    issues.append({
                        'type': 'suspicious_data',
                        'severity': 'medium',
                        'field': 'email',
                        'value': contact.email,
                        'message': 'Email appears to be a placeholder or fake',
                        'suggestion': 'Verify this is a real email address'
                    })
                    break
        
        # Check for suspicious names
        if contact.display_name:
            suspicious_name_patterns = [
                r'^test\s*user?$',
                r'^user\s*\d*$',
                r'^contact\s*\d*$',
                r'^unknown$',
                r'^n/?a$'
            ]
            
            for pattern in suspicious_name_patterns:
                if re.search(pattern, contact.display_name, re.IGNORECASE):
                    issues.append({
                        'type': 'suspicious_data',
                        'severity': 'low',
                        'field': 'display_name',
                        'value': contact.display_name,
                        'message': 'Name appears to be a placeholder',
                        'suggestion': 'Consider updating with real name'
                    })
                    break
        
        return issues
    
    def _calculate_quality_score(self, issues: List[Dict[str, Any]]) -> float:
        """Calculate a quality score (0-100) based on issues found."""
        if not issues:
            return 100.0
        
        # Weight issues by severity
        severity_weights = {
            'high': 30,
            'medium': 15,
            'low': 5
        }
        
        total_penalty = 0
        for issue in issues:
            severity = issue.get('severity', 'medium')
            total_penalty += severity_weights.get(severity, 15)
        
        # Cap the penalty at 100
        total_penalty = min(total_penalty, 100)
        
        return max(0.0, 100.0 - total_penalty)
    
    async def fix_contact_issues(
        self,
        contact_id: str,
        fixes: List[Dict[str, Any]],
        session: Optional[Session] = None
    ) -> Dict[str, Any]:
        """Apply fixes to a contact based on suggestions."""
        if session is None:
            with next(get_db()) as session:
                return await self._fix_contact_issues_impl(contact_id, fixes, session)
        else:
            return await self._fix_contact_issues_impl(contact_id, fixes, session)
    
    async def _fix_contact_issues_impl(
        self,
        contact_id: str,
        fixes: List[Dict[str, Any]],
        session: Session
    ) -> Dict[str, Any]:
        """Implementation of contact fixes."""
        try:
            import uuid
            contact = session.query(Contact).filter(Contact.id == uuid.UUID(contact_id)).first()
            
            if not contact:
                return {'success': False, 'message': 'Contact not found'}
            
            applied_fixes = []
            
            for fix in fixes:
                fix_type = fix.get('type')
                field = fix.get('field')
                new_value = fix.get('value')
                
                if fix_type == 'phone_cleanup' and field.startswith('phone_numbers'):
                    # Apply phone number cleanup
                    if contact.phone_numbers:
                        phone_numbers = contact.phone_numbers
                        if isinstance(phone_numbers, str):
                            phone_numbers = json.loads(phone_numbers)
                        
                        # Extract index from field name like 'phone_numbers[0]'
                        index_match = re.search(r'\[(\d+)\]', field)
                        if index_match:
                            index = int(index_match.group(1))
                            if 0 <= index < len(phone_numbers):
                                old_value = phone_numbers[index].get('number') if isinstance(phone_numbers[index], dict) else phone_numbers[index]
                                
                                if isinstance(phone_numbers[index], dict):
                                    phone_numbers[index]['number'] = new_value
                                else:
                                    phone_numbers[index] = new_value

                                # Force SQLAlchemy to detect the change by creating a new list
                                contact.phone_numbers = phone_numbers.copy()
                                # Mark the attribute as modified
                                from sqlalchemy.orm.attributes import flag_modified
                                flag_modified(contact, 'phone_numbers')
                                applied_fixes.append({
                                    'type': fix_type,
                                    'field': field,
                                    'old_value': old_value,
                                    'new_value': new_value
                                })
                
                elif hasattr(contact, field):
                    # Apply direct field updates
                    old_value = getattr(contact, field)
                    setattr(contact, field, new_value)
                    applied_fixes.append({
                        'type': fix_type,
                        'field': field,
                        'old_value': old_value,
                        'new_value': new_value
                    })
            
            if applied_fixes:
                contact.updated_at = datetime.now()

                # Log the fixes for learning
                feedback = LearningFeedback(
                    entity_type='contact',
                    entity_id=contact.id,
                    feedback_type='quality_fix',
                    original_value={'fixes_applied': applied_fixes},
                    corrected_value={'timestamp': datetime.now().isoformat()},
                    user_action='automated_quality_fix',
                    feedback_metadata={'fix_count': len(applied_fixes)}
                )
                session.add(feedback)

                # Commit all changes at once
                session.commit()
                session.refresh(contact)  # Refresh to get updated data
            
            return {
                'success': True,
                'contact_id': contact_id,
                'applied_fixes': applied_fixes,
                'message': f'Applied {len(applied_fixes)} fixes to contact'
            }
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error fixing contact issues: {e}")
            return {'success': False, 'message': str(e)}


# Global contact quality service instance
contact_quality_service = ContactQualityService()
