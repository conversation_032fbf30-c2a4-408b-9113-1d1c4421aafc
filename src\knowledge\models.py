"""Database models for the Personal Assistant."""

from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum
from dataclasses import dataclass

from sqlalchemy import (
    Column, String, Text, Integer, Float, Boolean, DateTime,
    ForeignKey, JSON, create_engine
)
from sqlalchemy.types import TypeDecorator, String as SQLString
import uuid as uuid_module
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.sql import func

Base = declarative_base()


class UUID(TypeDecorator):
    """Platform-independent UUID type.
    Uses PostgreSQL's UUID type, otherwise uses String(36).
    """
    impl = SQLString
    cache_ok = True

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(dialect.UUID())
        else:
            return dialect.type_descriptor(SQLString(36))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return str(value)
        else:
            if not isinstance(value, uuid_module.UUID):
                return str(uuid_module.UUID(value))
            else:
                return str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        else:
            if not isinstance(value, uuid_module.UUID):
                return uuid_module.UUID(value)
            return value


class TaskStatus(str, Enum):
    """Task status enumeration."""
    SUGGESTED = "suggested"  # AI-suggested, awaiting approval
    PENDING = "pending"      # Approved and active
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    DELETED = "deleted"


class TaskPriority(int, Enum):
    """Task priority levels."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


class SourceType(str, Enum):
    """Source types for tasks and other entities."""
    EMAIL = "email"
    CALENDAR = "calendar"
    MANUAL = "manual"
    DOCUMENT = "document"
    CONTACT = "contact"
    MEETING = "meeting"


class Task(Base):
    """Task model for storing actionable items."""
    __tablename__ = "tasks"

    id = Column(UUID(), primary_key=True, default=uuid_module.uuid4)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(50), default=TaskStatus.SUGGESTED.value)
    priority = Column(Integer, default=TaskPriority.MEDIUM.value)
    importance_score = Column(Float)  # AI-calculated importance (0.0-1.0)
    user_priority = Column(Integer)   # User-set priority for learning
    due_date = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    completed_at = Column(DateTime)
    
    # Source information
    source_type = Column(String(50))  # email, manual, document, etc.
    source_id = Column(String(255))   # Reference to source (email ID, etc.)
    
    # Relationships
    project_id = Column(UUID(), ForeignKey('projects.id'))
    project = relationship("Project", back_populates="tasks")
    
    # Metadata and learning
    task_metadata = Column(JSON)  # Flexible metadata storage
    keywords_found = Column(JSON)  # JSON array of keywords that triggered creation
    extraction_confidence = Column(Float)   # Confidence in AI extraction
    user_feedback_score = Column(Float)     # User feedback for learning
    
    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status}')>"


class Project(Base):
    """Project model for organizing tasks."""
    __tablename__ = "projects"

    id = Column(UUID(), primary_key=True, default=uuid_module.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(50), default="active")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    completed_at = Column(DateTime)
    
    # Relationships
    tasks = relationship("Task", back_populates="project")
    
    # Metadata
    project_metadata = Column(JSON)
    
    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', status='{self.status}')>"


class Email(Base):
    """Email model for storing processed emails."""
    __tablename__ = "emails"

    id = Column(UUID(), primary_key=True, default=uuid_module.uuid4)
    message_id = Column(String(255), unique=True, nullable=False)
    thread_id = Column(String(255))
    subject = Column(String(500))
    sender_email = Column(String(255))
    sender_name = Column(String(255))
    recipients = Column(JSON)  # JSON array of recipient emails
    cc_recipients = Column(JSON)  # JSON array of CC recipient emails
    bcc_recipients = Column(JSON)  # JSON array of BCC recipient emails
    body_text = Column(Text)
    body_html = Column(Text)
    received_date = Column(DateTime)
    sent_date = Column(DateTime)
    folder = Column(String(100))
    labels = Column(JSON)  # JSON array of email labels
    importance_score = Column(Float)
    urgency_score = Column(Float)
    category = Column(String(100))
    has_attachments = Column(Boolean, default=False)
    is_read = Column(Boolean, default=False)
    task_created = Column(Boolean, default=False)
    processed_at = Column(DateTime, default=func.now())
    
    # Metadata
    email_metadata = Column(JSON)
    
    def __repr__(self):
        return f"<Email(id={self.id}, subject='{self.subject}', sender='{self.sender_email}')>"


class Contact(Base):
    """Contact model for storing contact information."""
    __tablename__ = "contacts"

    id = Column(UUID(), primary_key=True, default=uuid_module.uuid4)
    email = Column(String(255), unique=True)
    first_name = Column(String(100))
    last_name = Column(String(100))
    display_name = Column(String(255))
    company = Column(String(255))
    job_title = Column(String(255))
    phone_numbers = Column(JSON)  # JSON array of phone objects
    addresses = Column(JSON)      # JSON array of address objects
    notes = Column(Text)
    tags = Column(JSON)  # JSON array of contact tags
    last_contact_date = Column(DateTime)
    contact_frequency = Column(Integer, default=0)
    importance_score = Column(Float)
    source = Column(String(50))  # email, csv, manual, etc.
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Metadata
    contact_metadata = Column(JSON)

    def __repr__(self):
        return f"<Contact(id={self.id}, email='{self.email}', name='{self.display_name}')>"


class CalendarEvent(Base):
    """Calendar event model."""
    __tablename__ = "calendar_events"

    id = Column(UUID(), primary_key=True, default=uuid_module.uuid4)
    event_id = Column(String(255))
    calendar_id = Column(String(255))
    calendar_type = Column(String(50))  # personal, work, etc.
    title = Column(String(500))
    description = Column(Text)
    location = Column(String(500))
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    all_day = Column(Boolean, default=False)
    recurrence_rule = Column(Text)
    attendees = Column(JSON)  # JSON array of attendee objects
    organizer_email = Column(String(255))
    status = Column(String(50))  # confirmed, tentative, cancelled
    visibility = Column(String(50))  # public, private, etc.
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Metadata
    event_metadata = Column(JSON)

    def __repr__(self):
        return f"<CalendarEvent(id={self.id}, title='{self.title}', start='{self.start_time}')>"


class LearningFeedback(Base):
    """Learning feedback model for AI improvement."""
    __tablename__ = "learning_feedback"

    id = Column(UUID(), primary_key=True, default=uuid_module.uuid4)
    entity_type = Column(String(50))  # task, email, contact
    entity_id = Column(UUID())
    feedback_type = Column(String(50))  # priority_change, deletion, rating
    original_value = Column(JSON)
    corrected_value = Column(JSON)
    user_action = Column(String(100))
    timestamp = Column(DateTime, default=func.now())
    applied_to_model = Column(Boolean, default=False)
    
    # Metadata
    feedback_metadata = Column(JSON)

    def __repr__(self):
        return f"<LearningFeedback(id={self.id}, type='{self.feedback_type}', entity='{self.entity_type}')>"


class Relationship(Base):
    """Relationship model for linking entities."""
    __tablename__ = "relationships"

    id = Column(UUID(), primary_key=True, default=uuid_module.uuid4)
    source_type = Column(String(50))
    source_id = Column(UUID())
    target_type = Column(String(50))
    target_id = Column(UUID())
    relationship_type = Column(String(100))
    strength = Column(Float, default=1.0)
    created_at = Column(DateTime, default=func.now())
    
    # Metadata
    relationship_metadata = Column(JSON)

    def __repr__(self):
        return f"<Relationship(id={self.id}, type='{self.relationship_type}', strength={self.strength})>"


# Data Transfer Objects (DTOs) for inter-service communication

@dataclass
class EmailTask:
    """Represents a task extracted from an email."""
    title: str
    description: str
    priority: int
    importance_score: float
    due_date: Optional[datetime]
    source_email_id: str
    keywords_found: List[str]
    metadata: Dict[str, Any]


@dataclass
class EmailContact:
    """Represents a contact extracted from an email."""
    email: str
    name: Optional[str]
    first_interaction: datetime
    interaction_count: int
    context: str  # How they were mentioned/involved
