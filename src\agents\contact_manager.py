"""Contact Manager Agent for the Assistant."""

import re
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from dataclasses import dataclass

from ..core.logging import logger
from ..core.config import settings
from ..knowledge.contact_quality_service import contact_quality_service
from ..integrations.contacts.contacts_service import contacts_service


@dataclass
class ContactInsight:
    """Represents an insight about a contact."""
    contact_id: str
    insight_type: str  # 'communication_pattern', 'importance_change', 'data_quality'
    message: str
    confidence: float
    suggested_action: Optional[str] = None


@dataclass
class ContactMergeCandidate:
    """Represents a potential contact merge."""
    primary_contact_id: str
    duplicate_contact_id: str
    similarity_score: float
    merge_confidence: float
    conflicting_fields: List[str]
    suggested_merge_data: Dict[str, Any]


class ContactManagerAgent:
    """
    Agent responsible for intelligent contact management and optimization.
    
    Key responsibilities:
    - Monitor contact data quality and suggest improvements
    - Detect duplicate contacts and suggest merges
    - Analyze communication patterns and update importance scores
    - Learn from user interactions to improve contact relevance
    - Auto-categorize contacts based on context and behavior
    """
    
    def __init__(self):
        self.config = settings.agents.contact_manager
        self.contact_config = settings.integrations.contacts.management
        self.learning_enabled = getattr(self.config, 'learning_enabled', True)
        
    async def process_contacts(self) -> Dict[str, Any]:
        """
        Process all contacts for optimization and quality improvements.
        
        Returns:
            Dictionary with processing results
        """
        logger.info("Starting contact processing cycle")
        
        result = {
            'status': 'success',
            'contacts_processed': 0,
            'quality_issues_found': 0,
            'duplicates_detected': 0,
            'insights_generated': 0,
            'auto_fixes_applied': 0,
            'errors': []
        }
        
        try:
            # Get all contacts
            contacts_response = await contacts_service.get_contacts(limit=1000)
            contacts = contacts_response.get('contacts', [])
            
            result['contacts_processed'] = len(contacts)
            
            # Quality analysis
            quality_results = await self._analyze_contact_quality(contacts)
            result['quality_issues_found'] = quality_results['issues_found']
            result['auto_fixes_applied'] = quality_results['fixes_applied']
            
            # Duplicate detection
            duplicates = await self._detect_duplicates(contacts)
            result['duplicates_detected'] = len(duplicates)
            
            # Generate insights
            insights = await self._generate_contact_insights(contacts)
            result['insights_generated'] = len(insights)
            
            # Update importance scores
            await self._update_importance_scores(contacts)
            
        except Exception as e:
            logger.error(f"Error during contact processing: {e}")
            result['errors'].append(str(e))
            result['status'] = 'error'
        
        logger.info(f"Contact processing completed: {result}")
        return result
    
    async def analyze_contact_relationships(self, contact_id: str) -> Dict[str, Any]:
        """
        Analyze relationships and communication patterns for a contact.
        
        Args:
            contact_id: Contact ID to analyze
            
        Returns:
            Dictionary with relationship analysis
        """
        try:
            # Get contact details
            contact = await contacts_service.get_contact(contact_id)
            if not contact:
                return {'status': 'error', 'message': 'Contact not found'}
            
            # Analyze communication frequency
            comm_pattern = await self._analyze_communication_pattern(contact)
            
            # Determine contact importance
            importance_score = await self._calculate_importance_score(contact, comm_pattern)
            
            # Find related contacts
            related_contacts = await self._find_related_contacts(contact)
            
            return {
                'status': 'success',
                'contact_id': contact_id,
                'communication_pattern': comm_pattern,
                'importance_score': importance_score,
                'related_contacts': related_contacts,
                'recommendations': await self._generate_contact_recommendations(contact, comm_pattern)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing contact relationships for {contact_id}: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def suggest_contact_merges(self, threshold: float = 0.8) -> List[ContactMergeCandidate]:
        """
        Suggest potential contact merges based on similarity.
        
        Args:
            threshold: Similarity threshold for merge suggestions
            
        Returns:
            List of merge candidates
        """
        try:
            contacts_response = await contacts_service.get_contacts(limit=1000)
            contacts = contacts_response.get('contacts', [])
            
            merge_candidates = []
            processed_pairs = set()
            
            for i, contact1 in enumerate(contacts):
                for j, contact2 in enumerate(contacts[i+1:], i+1):
                    pair_key = tuple(sorted([contact1['id'], contact2['id']]))
                    if pair_key in processed_pairs:
                        continue
                    
                    processed_pairs.add(pair_key)
                    
                    similarity = self._calculate_contact_similarity(contact1, contact2)
                    
                    if similarity >= threshold:
                        merge_candidate = ContactMergeCandidate(
                            primary_contact_id=contact1['id'],
                            duplicate_contact_id=contact2['id'],
                            similarity_score=similarity,
                            merge_confidence=self._calculate_merge_confidence(contact1, contact2),
                            conflicting_fields=self._find_conflicting_fields(contact1, contact2),
                            suggested_merge_data=self._suggest_merge_data(contact1, contact2)
                        )
                        merge_candidates.append(merge_candidate)
            
            # Sort by confidence
            merge_candidates.sort(key=lambda x: x.merge_confidence, reverse=True)
            
            logger.info(f"Found {len(merge_candidates)} potential contact merges")
            return merge_candidates
            
        except Exception as e:
            logger.error(f"Error suggesting contact merges: {e}")
            return []
    
    async def auto_categorize_contact(self, contact_id: str) -> Dict[str, Any]:
        """
        Automatically categorize a contact based on available data.
        
        Args:
            contact_id: Contact ID to categorize
            
        Returns:
            Dictionary with categorization result
        """
        try:
            contact = await contacts_service.get_contact(contact_id)
            if not contact:
                return {'status': 'error', 'message': 'Contact not found'}
            
            # Determine context (work/personal)
            context = self._determine_contact_context(contact)
            
            # Generate appropriate tags
            tags = self._generate_contact_tags(contact, context)
            
            # Calculate importance score
            importance = await self._calculate_importance_score(contact)
            
            # Update contact with new categorization
            updates = {
                'tags': tags,
                'importance_score': importance,
                'context': context
            }
            
            await contacts_service.update_contact(contact_id, updates)
            
            return {
                'status': 'success',
                'contact_id': contact_id,
                'context': context,
                'tags': tags,
                'importance_score': importance,
                'reasoning': self._generate_categorization_reasoning(contact, context, tags)
            }
            
        except Exception as e:
            logger.error(f"Error auto-categorizing contact {contact_id}: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def _analyze_contact_quality(self, contacts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze contact data quality and apply auto-fixes."""
        issues_found = 0
        fixes_applied = 0
        
        for contact in contacts:
            try:
                # Use the contact quality service
                quality_report = await contact_quality_service.validate_contact_dict(contact)
                
                if quality_report['issues']:
                    issues_found += len(quality_report['issues'])
                    
                    # Apply auto-fixes for safe issues
                    auto_fixes = await self._apply_auto_fixes(contact, quality_report['issues'])
                    fixes_applied += len(auto_fixes)
                    
            except Exception as e:
                logger.error(f"Error analyzing quality for contact {contact.get('id')}: {e}")
        
        return {
            'issues_found': issues_found,
            'fixes_applied': fixes_applied
        }
    
    async def _detect_duplicates(self, contacts: List[Dict[str, Any]]) -> List[ContactMergeCandidate]:
        """Detect potential duplicate contacts."""
        duplicates = []
        processed_pairs = set()
        
        for i, contact1 in enumerate(contacts):
            for j, contact2 in enumerate(contacts[i+1:], i+1):
                pair_key = tuple(sorted([contact1['id'], contact2['id']]))
                if pair_key in processed_pairs:
                    continue
                
                processed_pairs.add(pair_key)
                
                similarity = self._calculate_contact_similarity(contact1, contact2)
                
                if similarity >= 0.7:  # 70% similarity threshold for duplicates
                    duplicates.append(ContactMergeCandidate(
                        primary_contact_id=contact1['id'],
                        duplicate_contact_id=contact2['id'],
                        similarity_score=similarity,
                        merge_confidence=self._calculate_merge_confidence(contact1, contact2),
                        conflicting_fields=self._find_conflicting_fields(contact1, contact2),
                        suggested_merge_data=self._suggest_merge_data(contact1, contact2)
                    ))
        
        return duplicates
    
    async def _generate_contact_insights(self, contacts: List[Dict[str, Any]]) -> List[ContactInsight]:
        """Generate insights about contacts."""
        insights = []
        
        for contact in contacts:
            # Check for stale contacts (no recent communication)
            if contact.get('last_contact_date'):
                last_contact = datetime.fromisoformat(contact['last_contact_date'].replace('Z', '+00:00'))
                days_since_contact = (datetime.now() - last_contact).days
                
                if days_since_contact > 90:  # 3 months
                    insights.append(ContactInsight(
                        contact_id=contact['id'],
                        insight_type='communication_pattern',
                        message=f"No communication with {contact.get('display_name', 'contact')} for {days_since_contact} days",
                        confidence=0.8,
                        suggested_action='follow_up'
                    ))
        
        return insights
    
    async def _update_importance_scores(self, contacts: List[Dict[str, Any]]):
        """Update importance scores for contacts."""
        for contact in contacts:
            try:
                new_score = await self._calculate_importance_score(contact)
                
                if abs(new_score - contact.get('importance_score', 0.5)) > 0.1:
                    await contacts_service.update_contact(
                        contact['id'],
                        {'importance_score': new_score}
                    )
                    
            except Exception as e:
                logger.error(f"Error updating importance score for contact {contact.get('id')}: {e}")
    
    def _calculate_contact_similarity(self, contact1: Dict[str, Any], contact2: Dict[str, Any]) -> float:
        """Calculate similarity between two contacts."""
        similarity_score = 0.0
        total_weight = 0.0
        
        # Email similarity (high weight)
        if contact1.get('email') and contact2.get('email'):
            if contact1['email'].lower() == contact2['email'].lower():
                similarity_score += 1.0 * 0.4
            total_weight += 0.4
        
        # Name similarity (high weight)
        name1 = f"{contact1.get('first_name', '')} {contact1.get('last_name', '')}".strip()
        name2 = f"{contact2.get('first_name', '')} {contact2.get('last_name', '')}".strip()
        
        if name1 and name2:
            name_similarity = self._calculate_string_similarity(name1, name2)
            similarity_score += name_similarity * 0.3
            total_weight += 0.3
        
        # Phone similarity (medium weight)
        phones1 = contact1.get('phone_numbers', [])
        phones2 = contact2.get('phone_numbers', [])
        
        if phones1 and phones2:
            phone_match = any(
                self._normalize_phone(p1.get('number', '')) == self._normalize_phone(p2.get('number', ''))
                for p1 in phones1 for p2 in phones2
            )
            if phone_match:
                similarity_score += 1.0 * 0.2
            total_weight += 0.2
        
        # Company similarity (low weight)
        if contact1.get('company') and contact2.get('company'):
            company_similarity = self._calculate_string_similarity(
                contact1['company'], contact2['company']
            )
            similarity_score += company_similarity * 0.1
            total_weight += 0.1
        
        return similarity_score / total_weight if total_weight > 0 else 0.0
    
    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings."""
        str1, str2 = str1.lower().strip(), str2.lower().strip()
        
        if str1 == str2:
            return 1.0
        
        # Simple Jaccard similarity
        words1 = set(str1.split())
        words2 = set(str2.split())
        
        if not words1 and not words2:
            return 1.0
        
        intersection = len(words1 & words2)
        union = len(words1 | words2)
        
        return intersection / union if union > 0 else 0.0
    
    def _normalize_phone(self, phone: str) -> str:
        """Normalize phone number for comparison."""
        return re.sub(r'[^\d]', '', phone)
    
    def _determine_contact_context(self, contact: Dict[str, Any]) -> str:
        """Determine if contact is work or personal."""
        email = contact.get('email', '').lower()
        company = contact.get('company', '')
        
        # Check against work domains
        work_domains = self.contact_config.work_email_domains
        if any(domain in email for domain in work_domains):
            return 'work'
        
        # Check against personal indicators
        personal_indicators = self.contact_config.personal_indicators
        if any(indicator in email for indicator in personal_indicators):
            return 'personal'
        
        # If has company, likely work
        if company:
            return 'work'
        
        return 'personal'  # Default to personal
    
    def _generate_contact_tags(self, contact: Dict[str, Any], context: str) -> List[str]:
        """Generate appropriate tags for a contact."""
        tags = [context]
        
        # Add role-based tags
        job_title = contact.get('job_title', '').lower()
        if 'manager' in job_title or 'director' in job_title or 'ceo' in job_title:
            tags.append('leadership')
        elif 'engineer' in job_title or 'developer' in job_title:
            tags.append('technical')
        elif 'sales' in job_title or 'marketing' in job_title:
            tags.append('business')
        
        # Add frequency tags
        frequency = contact.get('contact_frequency', 0)
        if frequency > 10:
            tags.append('frequent')
        elif frequency > 5:
            tags.append('regular')
        else:
            tags.append('occasional')
        
        return tags
    
    async def _calculate_importance_score(self, contact: Dict[str, Any], comm_pattern: Optional[Dict] = None) -> float:
        """Calculate importance score for a contact."""
        score = 0.5  # Base score
        
        # Communication frequency
        frequency = contact.get('contact_frequency', 0)
        if frequency > 20:
            score += 0.3
        elif frequency > 10:
            score += 0.2
        elif frequency > 5:
            score += 0.1
        
        # Recent communication
        if contact.get('last_contact_date'):
            try:
                last_contact = datetime.fromisoformat(contact['last_contact_date'].replace('Z', '+00:00'))
                days_since = (datetime.now() - last_contact).days
                
                if days_since < 7:
                    score += 0.2
                elif days_since < 30:
                    score += 0.1
            except:
                pass
        
        # Work context gets higher importance
        if self._determine_contact_context(contact) == 'work':
            score += 0.1
        
        return min(score, 1.0)
    
    def _generate_categorization_reasoning(self, contact: Dict[str, Any], context: str, tags: List[str]) -> str:
        """Generate reasoning for contact categorization."""
        reasons = []
        
        if context == 'work':
            if contact.get('company'):
                reasons.append(f"works at {contact['company']}")
            if any(domain in contact.get('email', '') for domain in self.contact_config.work_email_domains):
                reasons.append("email domain indicates work context")
        
        if 'frequent' in tags:
            reasons.append("high communication frequency")
        
        if not reasons:
            reasons.append("based on available contact data")
        
        return f"Categorized as {context} because {', '.join(reasons)}"
    
    async def _apply_auto_fixes(self, contact: Dict[str, Any], issues: List[Dict]) -> List[str]:
        """Apply automatic fixes for contact quality issues."""
        fixes = []
        
        for issue in issues:
            if issue['type'] == 'formatting_issues' and issue['field'] == 'phone_numbers':
                # Auto-fix phone number formatting
                # This would be implemented based on specific formatting rules
                fixes.append(f"Fixed phone number formatting for {contact.get('display_name')}")
        
        return fixes


# Create global instance
contact_manager_agent = ContactManagerAgent()
