<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ title }} - Assistant{% endblock %}</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', path='/css/main.css') }}">
    {% block extra_css %}{% endblock %}
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', path='/images/favicon.ico') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="/" class="nav-logo">
                    <span class="logo-icon">🧠</span>
                    <span class="logo-text">Assistant</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="/" class="nav-link {% if request.url.path == '/' %}active{% endif %}">
                    <span class="nav-icon">📊</span>
                    Dashboard
                </a>
                <a href="/search" class="nav-link {% if request.url.path == '/search' %}active{% endif %}">
                    <span class="nav-icon">🔍</span>
                    Search
                </a>
                <a href="/upload" class="nav-link {% if request.url.path == '/upload' %}active{% endif %}">
                    <span class="nav-icon">📤</span>
                    Upload
                </a>
                <a href="/notes" class="nav-link {% if request.url.path == '/notes' %}active{% endif %}">
                    <span class="nav-icon">📝</span>
                    Notes
                </a>
                <a href="/tasks" class="nav-link {% if request.url.path == '/tasks' %}active{% endif %}">
                    <span class="nav-icon">✅</span>
                    Tasks
                </a>
                <a href="/projects" class="nav-link {% if request.url.path == '/projects' %}active{% endif %}">
                    <span class="nav-icon">📁</span>
                    Projects
                </a>
                <a href="/email" class="nav-link {% if request.url.path == '/email' %}active{% endif %}">
                    <span class="nav-icon">📧</span>
                    Email
                </a>
                <a href="/calendar" class="nav-link {% if request.url.path == '/calendar' %}active{% endif %}">
                    <span class="nav-icon">📅</span>
                    Calendar
                </a>
                <a href="/contacts" class="nav-link {% if request.url.path == '/contacts' %}active{% endif %}">
                    <span class="nav-icon">👥</span>
                    Contacts
                </a>
                <a href="/chat" class="nav-link {% if request.url.path == '/chat' %}active{% endif %}">
                    <span class="nav-icon">💬</span>
                    Chat
                </a>
            </div>
            
            <div class="nav-actions">
                <a href="/logs" class="nav-link {% if request.url.path == '/logs' %}active{% endif %}" title="System Logs">
                    <span class="nav-icon">📋</span>
                </a>
                <a href="/settings" class="nav-link {% if request.url.path == '/settings' %}active{% endif %}" title="Settings">
                    <span class="nav-icon">⚙️</span>
                </a>
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <span class="theme-icon">🌙</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Assistant - Local Knowledge Library</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', path='/js/logger.js') }}"></script>
    <script src="{{ url_for('static', path='/js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
