"""Google Contacts API client for contact management."""

import pickle
from pathlib import Path
from typing import Dict, List, Optional, Any

# Optional Google API imports
try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    # Create dummy classes for when Google APIs aren't available
    class Request: pass
    class Credentials: pass
    class InstalledAppFlow: pass
    class HttpError(Exception): pass
    def build(*args, **kwargs): return None

from ...core.logging import logger
from ...core.config import settings


class GoogleContactsClient:
    """
    Google Contacts API client for managing contacts.
    """
    
    # People API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/contacts.readonly',
        'https://www.googleapis.com/auth/contacts',
        'https://www.googleapis.com/auth/userinfo.profile'
    ]
    
    def __init__(self):
        """Initialize Google Contacts client."""
        if not GOOGLE_AVAILABLE:
            logger.warning("Google API libraries not available. Google Contacts integration disabled.")
            self.service = None
            self.credentials = None
            return

        self.config = settings.integrations.contacts.providers.google
        self.credentials_file = self.config.credentials_file
        self.token_file = self.config.token_file
        self.contexts = self.config.contexts

        self.service = None
        self.credentials = None
        
    async def authenticate(self) -> bool:
        """
        Authenticate with Google People API using OAuth2.

        Returns:
            True if authentication successful, False otherwise
        """
        if not GOOGLE_AVAILABLE:
            logger.warning("Google API libraries not available")
            return False

        try:
            # Load existing credentials if available
            if Path(self.token_file).exists():
                with open(self.token_file, 'rb') as token:
                    self.credentials = pickle.load(token)
            
            # If there are no valid credentials, get new ones
            if not self.credentials or not self.credentials.valid:
                if self.credentials and self.credentials.expired and self.credentials.refresh_token:
                    logger.info("Refreshing Google Contacts credentials")
                    self.credentials.refresh(Request())
                else:
                    logger.info("Starting OAuth flow for Google Contacts")
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_file, self.SCOPES
                    )
                    self.credentials = flow.run_local_server(port=0)
                
                # Save credentials for next run
                Path(self.token_file).parent.mkdir(parents=True, exist_ok=True)
                with open(self.token_file, 'wb') as token:
                    pickle.dump(self.credentials, token)
            
            # Build the People service
            self.service = build('people', 'v1', credentials=self.credentials)
            
            # Test the connection
            profile = self.service.people().get(
                resourceName='people/me',
                personFields='names,emailAddresses'
            ).execute()
            
            name = "Unknown"
            if 'names' in profile and profile['names']:
                name = profile['names'][0].get('displayName', 'Unknown')
            
            logger.info(f"Successfully authenticated Google Contacts for: {name}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to authenticate Google Contacts: {e}")
            return False
    
    async def get_contacts(
        self,
        max_results: int = 1000,
        page_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get contacts from Google Contacts.
        
        Args:
            max_results: Maximum number of contacts to retrieve
            page_token: Token for pagination
            
        Returns:
            Dictionary containing contacts and pagination info
        """
        if not GOOGLE_AVAILABLE:
            return {"contacts": [], "next_page_token": None, "error": "Google API not available"}

        if not self.service:
            if not await self.authenticate():
                return {'contacts': [], 'next_page_token': None}
        
        try:
            logger.info(f"Fetching up to {max_results} contacts from Google Contacts")
            
            # Get contacts
            request_params = {
                'resourceName': 'people/me',
                'pageSize': min(max_results, 1000),  # API limit is 1000
                'personFields': 'names,emailAddresses,phoneNumbers,organizations,addresses,birthdays,photos,metadata'
            }
            
            if page_token:
                request_params['pageToken'] = page_token
            
            result = self.service.people().connections().list(**request_params).execute()
            
            contacts = result.get('connections', [])
            next_page_token = result.get('nextPageToken')
            
            logger.info(f"Retrieved {len(contacts)} contacts from Google Contacts")
            
            # Process contacts to extract useful information
            processed_contacts = []
            for contact in contacts:
                processed_contact = self._process_contact(contact)
                if processed_contact:
                    processed_contacts.append(processed_contact)
            
            return {
                'contacts': processed_contacts,
                'next_page_token': next_page_token,
                'total_retrieved': len(processed_contacts)
            }
            
        except Exception as e:
            logger.error(f"Error getting contacts: {e}")
            return {'contacts': [], 'next_page_token': None}
    
    async def get_all_contacts(self) -> List[Dict[str, Any]]:
        """
        Get all contacts from Google Contacts (handles pagination).
        
        Returns:
            List of all contacts
        """
        all_contacts = []
        page_token = None
        
        while True:
            result = await self.get_contacts(max_results=1000, page_token=page_token)
            contacts = result.get('contacts', [])
            all_contacts.extend(contacts)
            
            page_token = result.get('next_page_token')
            if not page_token:
                break
        
        logger.info(f"Retrieved total of {len(all_contacts)} contacts")
        return all_contacts
    
    async def create_contact(
        self,
        name: str,
        email: Optional[str] = None,
        phone: Optional[str] = None,
        organization: Optional[str] = None,
        notes: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Create a new contact in Google Contacts.
        
        Args:
            name: Contact name
            email: Email address
            phone: Phone number
            organization: Organization/company
            notes: Additional notes
            
        Returns:
            Created contact data or None if failed
        """
        if not self.service:
            if not await self.authenticate():
                return None
        
        try:
            contact = {
                'names': [{'givenName': name}]
            }
            
            if email:
                contact['emailAddresses'] = [{'value': email}]
            
            if phone:
                contact['phoneNumbers'] = [{'value': phone}]
            
            if organization:
                contact['organizations'] = [{'name': organization}]
            
            if notes:
                contact['biographies'] = [{'value': notes}]
            
            created_contact = self.service.people().createContact(body=contact).execute()
            
            logger.info(f"Created contact: {name}")
            
            return self._process_contact(created_contact)
            
        except Exception as e:
            logger.error(f"Error creating contact {name}: {e}")
            return None
    
    async def search_contacts(self, query: str) -> List[Dict[str, Any]]:
        """
        Search contacts by name or email.
        
        Args:
            query: Search query
            
        Returns:
            List of matching contacts
        """
        if not self.service:
            if not await self.authenticate():
                return []
        
        try:
            logger.info(f"Searching contacts for: {query}")
            
            result = self.service.people().searchContacts(
                query=query,
                readMask='names,emailAddresses,phoneNumbers,organizations'
            ).execute()
            
            contacts = result.get('results', [])
            
            # Process search results
            processed_contacts = []
            for result_item in contacts:
                person = result_item.get('person', {})
                processed_contact = self._process_contact(person)
                if processed_contact:
                    processed_contacts.append(processed_contact)
            
            logger.info(f"Found {len(processed_contacts)} contacts matching '{query}'")
            
            return processed_contacts
            
        except Exception as e:
            logger.error(f"Error searching contacts for '{query}': {e}")
            return []
    
    def _process_contact(self, contact: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Process raw contact data from Google API into standardized format.
        
        Args:
            contact: Raw contact data from Google API
            
        Returns:
            Processed contact data or None if invalid
        """
        try:
            # Extract name
            name = "Unknown"
            if 'names' in contact and contact['names']:
                name_data = contact['names'][0]
                if 'displayName' in name_data:
                    name = name_data['displayName']
                elif 'givenName' in name_data:
                    given_name = name_data.get('givenName', '')
                    family_name = name_data.get('familyName', '')
                    name = f"{given_name} {family_name}".strip()
            
            # Skip contacts without a proper name
            if name == "Unknown" or not name:
                return None
            
            # Extract emails
            emails = []
            if 'emailAddresses' in contact:
                for email_data in contact['emailAddresses']:
                    if 'value' in email_data:
                        emails.append(email_data['value'])
            
            # Extract phone numbers
            phones = []
            if 'phoneNumbers' in contact:
                for phone_data in contact['phoneNumbers']:
                    if 'value' in phone_data:
                        phones.append(phone_data['value'])
            
            # Extract organization
            organization = None
            if 'organizations' in contact and contact['organizations']:
                org_data = contact['organizations'][0]
                organization = org_data.get('name')
            
            # Extract resource name (ID)
            resource_name = contact.get('resourceName', '')
            
            processed = {
                'id': resource_name,
                'name': name,
                'emails': emails,
                'phones': phones,
                'organization': organization,
                'source': 'google_contacts',
                'context': 'personal'  # Default context
            }
            
            return processed
            
        except Exception as e:
            logger.error(f"Error processing contact: {e}")
            return None
