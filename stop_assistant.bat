@echo off
REM Personal Assistant - Stop Application
REM This script stops the Personal Assistant web application

echo ========================================
echo   Personal Assistant - Stopping...
echo ========================================
echo.

REM Kill any running Python processes that might be the assistant
echo Stopping Personal Assistant processes...

REM Find and kill processes running the web app
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| findstr "web_app"') do (
    echo Stopping process %%i...
    taskkill /pid %%i /f >nul 2>&1
)

REM More aggressive approach - kill any Python process running on port 8000
for /f "tokens=5" %%i in ('netstat -ano ^| findstr ":8000"') do (
    echo Stopping process using port 8000: %%i
    taskkill /pid %%i /f >nul 2>&1
)

REM Alternative method using PowerShell for more precision
powershell -Command "Get-Process python -ErrorAction SilentlyContinue | Where-Object {$_.CommandLine -like '*web_app*'} | Stop-Process -Force" >nul 2>&1

echo.
echo Personal Assistant has been stopped
echo ========================================
pause
