"""Google Calendar API client for calendar integration."""

import pickle
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from ...core.logging import logger
from ...core.config import settings


class GoogleCalendarClient:
    """
    Google Calendar API client for managing calendar events and availability.
    """
    
    # Calendar API scopes
    SCOPES = [
        'https://www.googleapis.com/auth/calendar.readonly',
        'https://www.googleapis.com/auth/calendar.events'
    ]
    
    def __init__(self):
        """Initialize Google Calendar client."""
        self.config = settings.integrations.calendar.providers.google
        self.credentials_file = self.config.credentials_file
        self.token_file = self.config.token_file
        self.calendars = self.config.calendars
        
        self.service = None
        self.credentials = None
        
    async def authenticate(self) -> bool:
        """
        Authenticate with Google Calendar API using OAuth2.
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            # Load existing credentials if available
            if Path(self.token_file).exists():
                with open(self.token_file, 'rb') as token:
                    self.credentials = pickle.load(token)
            
            # If there are no valid credentials, get new ones
            if not self.credentials or not self.credentials.valid:
                if self.credentials and self.credentials.expired and self.credentials.refresh_token:
                    logger.info("Refreshing Google Calendar credentials")
                    self.credentials.refresh(Request())
                else:
                    logger.info("Starting OAuth flow for Google Calendar")
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_file, self.SCOPES
                    )
                    self.credentials = flow.run_local_server(port=0)
                
                # Save credentials for next run
                Path(self.token_file).parent.mkdir(parents=True, exist_ok=True)
                with open(self.token_file, 'wb') as token:
                    pickle.dump(self.credentials, token)
            
            # Build the Calendar service
            self.service = build('calendar', 'v3', credentials=self.credentials)
            
            # Test the connection
            calendar_list = self.service.calendarList().list().execute()
            logger.info(f"Successfully authenticated Google Calendar: {len(calendar_list.get('items', []))} calendars found")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to authenticate Google Calendar: {e}")
            return False
    
    async def get_events(
        self,
        calendar_id: str = 'primary',
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        max_results: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get events from a specific calendar.
        
        Args:
            calendar_id: Calendar ID ('primary' for main calendar)
            start_time: Start time for event search
            end_time: End time for event search
            max_results: Maximum number of events to retrieve
            
        Returns:
            List of calendar events
        """
        if not self.service:
            if not await self.authenticate():
                return []
        
        try:
            # Default to next 30 days if no time range specified
            if start_time is None:
                start_time = datetime.now()
            if end_time is None:
                end_time = start_time + timedelta(days=30)
            
            # Format times for API
            start_time_str = start_time.isoformat() + 'Z'
            end_time_str = end_time.isoformat() + 'Z'
            
            logger.info(f"Fetching events from calendar {calendar_id} from {start_time_str} to {end_time_str}")
            
            # Get events
            events_result = self.service.events().list(
                calendarId=calendar_id,
                timeMin=start_time_str,
                timeMax=end_time_str,
                maxResults=max_results,
                singleEvents=True,
                orderBy='startTime'
            ).execute()
            
            events = events_result.get('items', [])
            logger.info(f"Found {len(events)} events in calendar {calendar_id}")
            
            return events
            
        except Exception as e:
            logger.error(f"Error getting events from calendar {calendar_id}: {e}")
            return []
    
    async def get_all_calendars(self) -> List[Dict[str, Any]]:
        """
        Get list of all accessible calendars.
        
        Returns:
            List of calendar information
        """
        if not self.service:
            if not await self.authenticate():
                return []
        
        try:
            calendar_list = self.service.calendarList().list().execute()
            calendars = calendar_list.get('items', [])
            
            logger.info(f"Found {len(calendars)} accessible calendars")
            
            return calendars
            
        except Exception as e:
            logger.error(f"Error getting calendar list: {e}")
            return []
    
    async def create_event(
        self,
        calendar_id: str,
        summary: str,
        start_time: datetime,
        end_time: datetime,
        description: Optional[str] = None,
        location: Optional[str] = None,
        attendees: Optional[List[str]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Create a new calendar event.
        
        Args:
            calendar_id: Calendar ID to create event in
            summary: Event title
            start_time: Event start time
            end_time: Event end time
            description: Event description
            location: Event location
            attendees: List of attendee email addresses
            
        Returns:
            Created event data or None if failed
        """
        if not self.service:
            if not await self.authenticate():
                return None
        
        try:
            event = {
                'summary': summary,
                'start': {
                    'dateTime': start_time.isoformat(),
                    'timeZone': 'America/New_York',  # TODO: Make configurable
                },
                'end': {
                    'dateTime': end_time.isoformat(),
                    'timeZone': 'America/New_York',
                },
            }
            
            if description:
                event['description'] = description
            
            if location:
                event['location'] = location
            
            if attendees:
                event['attendees'] = [{'email': email} for email in attendees]
            
            created_event = self.service.events().insert(
                calendarId=calendar_id,
                body=event
            ).execute()
            
            logger.info(f"Created event '{summary}' in calendar {calendar_id}")
            
            return created_event
            
        except Exception as e:
            logger.error(f"Error creating event in calendar {calendar_id}: {e}")
            return None
    
    async def get_availability(
        self,
        start_time: datetime,
        end_time: datetime,
        calendar_ids: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Get free/busy information for calendars.
        
        Args:
            start_time: Start time for availability check
            end_time: End time for availability check
            calendar_ids: List of calendar IDs to check (defaults to primary)
            
        Returns:
            List of busy time periods
        """
        if not self.service:
            if not await self.authenticate():
                return []
        
        try:
            if calendar_ids is None:
                calendar_ids = ['primary']
            
            # Format times for API
            start_time_str = start_time.isoformat() + 'Z'
            end_time_str = end_time.isoformat() + 'Z'
            
            body = {
                'timeMin': start_time_str,
                'timeMax': end_time_str,
                'items': [{'id': cal_id} for cal_id in calendar_ids]
            }
            
            freebusy_result = self.service.freebusy().query(body=body).execute()
            
            busy_times = []
            for calendar_id in calendar_ids:
                calendar_busy = freebusy_result.get('calendars', {}).get(calendar_id, {}).get('busy', [])
                busy_times.extend(calendar_busy)
            
            logger.info(f"Found {len(busy_times)} busy periods across {len(calendar_ids)} calendars")
            
            return busy_times
            
        except Exception as e:
            logger.error(f"Error getting availability: {e}")
            return []
