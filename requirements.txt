# Personal Assistant - Required Dependencies
# Core web framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
jinja2>=3.1.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0

# HTTP client
httpx>=0.25.0
requests>=2.31.0

# Data processing
pandas>=2.1.0
numpy>=1.24.0

# Email processing
email-validator>=2.1.0
python-multipart>=0.0.6

# Configuration and utilities
pydantic>=2.4.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0
pyyaml>=6.0.1

# Date/time handling
python-dateutil>=2.8.2

# Logging and monitoring
structlog>=23.2.0
loguru>=0.7.0

# Google API dependencies (optional)
google-auth>=2.23.0
google-auth-oauthlib>=1.1.0
google-auth-httplib2>=0.1.1
google-api-python-client>=2.100.0

# Optional AI/ML dependencies (install if needed)
# ollama>=0.1.0
# openai>=1.0.0
# anthropic>=0.7.0

# Development tools (optional)
# pytest>=7.4.0
# black>=23.9.0
# flake8>=6.1.0
