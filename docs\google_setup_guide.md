# Google Services Integration Setup

This guide walks you through setting up Google integrations for Gmail, Google Calendar, and Google Contacts.

## Prerequisites

1. Google account(s) - personal and/or work
2. Google Cloud Console access
3. Assistant project set up and running

## Step 1: Google Cloud Console Setup

### 1.1 Create a New Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "New Project" or select an existing project
3. Name it something like "Assistant Integration"
4. Note the Project ID

### 1.2 Enable Required APIs
Enable these APIs in the Google Cloud Console:

1. **Gmail API** - for email access
2. **Google Calendar API** - for calendar access  
3. **People API** - for contacts access

To enable APIs:
1. Go to "APIs & Services" > "Library"
2. Search for each API and click "Enable"

### 1.3 Create OAuth 2.0 Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Choose "Desktop application"
4. Name it "Assistant Desktop Client"
5. Download the JSON file

## Step 2: Configure Assistant

### 2.1 Save Credentials Files
1. Rename the downloaded JSON file to match your configuration:
   - Personal Gmail: `config/gmail_personal_credentials.json`
   - Work Gmail: `config/gmail_work_credentials.json`
   - Calendar: `config/google_calendar_credentials.json`
   - Contacts: `config/google_contacts_credentials.json`

### 2.2 Update Configuration
Edit `config/settings.yaml`:

```yaml
integrations:
  email:
    providers:
      gmail:
        enabled: true
        accounts:
          personal:
            credentials_file: "./config/gmail_personal_credentials.json"
            context: "personal"
          work:
            credentials_file: "./config/gmail_work_credentials.json"
            context: "work"

  calendar:
    providers:
      google:
        enabled: true
        calendars:
          personal: "primary"
          work: "<EMAIL>"
          car: "<EMAIL>"

  contacts:
    providers:
      google:
        enabled: true
```

### 2.3 Find Calendar IDs
To find your calendar IDs:
1. Go to [Google Calendar](https://calendar.google.com/)
2. Click the three dots next to each calendar
3. Select "Settings and sharing"
4. Copy the "Calendar ID" (usually an email address)

## Step 3: Initial Authentication

### 3.1 Run Authentication Setup
```bash
# Start the Assistant
python -m src.api.web_app

# Or use CLI for initial auth
python -m src.api.cli init
```

### 3.2 Complete OAuth Flow
1. The system will open a browser window
2. Sign in to your Google account
3. Grant permissions for:
   - Read and manage Gmail
   - Read and manage Calendar
   - Read and manage Contacts
4. The system will save tokens automatically

### 3.3 Verify Setup
```bash
# Check system status
python -m src.api.cli status
```

You should see all Google integrations as "enabled" and "authenticated".

## Step 4: Proton Mail Setup (IMAP Bridge)

### 4.1 Install Proton Bridge
1. Download from [Proton Bridge](https://proton.me/mail/bridge)
2. Install and set up with your Proton account
3. Note the IMAP settings (usually localhost:1143)

### 4.2 Configure Proton in Assistant
Edit `config/settings.yaml`:

```yaml
integrations:
  email:
    providers:
      proton:
        enabled: true
        host: "127.0.0.1"
        port: 1143
        username: "<EMAIL>"
        password_file: "./config/proton_password.txt"
        context: "personal"  # or "work"
```

### 4.3 Save Proton Password
Create `config/proton_password.txt` with your Proton Bridge password.

## Step 5: Test Integration

### 5.1 Test Email Processing
```bash
# Trigger email sync
curl -X POST http://localhost:8000/api/email/sync

# Check for new tasks created from emails
curl http://localhost:8000/api/tasks
```

### 5.2 Test Calendar Sync
```bash
# Trigger calendar sync
curl -X POST http://localhost:8000/api/calendar/sync

# Check availability
curl "http://localhost:8000/api/calendar/availability?start_date=2024-01-01&end_date=2024-01-02"
```

### 5.3 Test Contact Management
```bash
# Check contacts
curl http://localhost:8000/api/contacts
```

## Step 6: Appfolio CSV Integration

### 6.1 Set Up Email Monitoring
The system will automatically detect CSV files from Appfolio emails based on the pattern configured in `settings.yaml`.

### 6.2 Manual CSV Import
You can also manually import CSV files:

```bash
# Via web interface
# Go to http://localhost:8000/contacts and use the import feature

# Via API
curl -X POST -F "file=@contacts.csv" http://localhost:8000/api/contacts/import
```

## Troubleshooting

### Common Issues

**"Credentials not found"**
- Ensure JSON files are in the correct location
- Check file permissions

**"Authentication failed"**
- Re-run the OAuth flow
- Check if APIs are enabled in Google Cloud Console

**"Rate limit exceeded"**
- Google APIs have rate limits
- The system will automatically retry with backoff

**"Calendar not found"**
- Verify calendar IDs in Google Calendar settings
- Ensure the calendar is shared with the authenticated account

### Logs
Check logs for detailed error information:
```bash
tail -f data/logs/assistant.log
```

### Reset Authentication
If you need to re-authenticate:
```bash
# Remove token files
rm data/tokens/*.json

# Re-run authentication
python -m src.api.cli init
```

## Security Notes

1. **Credential Files**: Keep credential files secure and never commit to version control
2. **Token Storage**: Tokens are stored locally in `data/tokens/`
3. **Permissions**: Only grant minimum required permissions
4. **Regular Rotation**: Consider rotating credentials periodically

## Next Steps

Once integration is working:
1. Monitor the learning system as it processes your emails
2. Provide feedback by editing task priorities and metadata
3. Review and adjust the configuration based on your workflow
4. Set up automated backups of your data directory

The system will start learning your patterns and preferences automatically!
