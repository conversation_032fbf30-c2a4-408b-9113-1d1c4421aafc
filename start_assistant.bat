@echo off
REM Personal Assistant - Start Application
REM This script starts the Personal Assistant web application

echo ========================================
echo    Personal Assistant - Starting...
echo ========================================
echo.

REM Change to the application directory
cd /d "%~dp0"

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

REM Install/update dependencies
echo Installing dependencies...
echo This may take a few minutes on first run...
pip install --upgrade pip
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
) else (
    echo Dependencies installed successfully
)

REM Create data directories if they don't exist
if not exist "data" mkdir data
if not exist "data\database" mkdir data\database
if not exist "data\archive" mkdir data\archive
if not exist "data\models" mkdir data\models
if not exist "logs" mkdir logs

REM Check if Ollama is running (optional)
echo Checking Ollama service...
curl -s http://localhost:11434/api/version >nul 2>&1
if errorlevel 1 (
    echo WARNING: Ollama service not detected at localhost:11434
    echo Some AI features may not work without Ollama running
    echo You can start Ollama separately if needed
    echo.
)

REM Start the application
echo Starting Personal Assistant...
echo.
echo The application will be available at:
echo   http://localhost:8000
echo.
echo Press Ctrl+C to stop the application
echo ========================================
echo.

REM Start the web application
python -m src.api.web_app

REM If we get here, the application has stopped
echo.
echo ========================================
echo Personal Assistant has stopped
echo ========================================
pause
