// Assistant - Main JavaScript

class Assistant {
    constructor() {
        this.theme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.setupTheme();
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            const icon = themeToggle.querySelector('.theme-icon');
            icon.textContent = this.theme === 'dark' ? '☀️' : '🌙';
        }
    }

    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', this.theme);
        this.setupTheme();
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            window.location.href = '/search';
        }
        
        // Ctrl/Cmd + U for upload
        if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
            e.preventDefault();
            window.location.href = '/upload';
        }
        
        // Ctrl/Cmd + N for new note
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            window.location.href = '/notes';
        }
        
        // Ctrl/Cmd + T for tasks
        if ((e.ctrlKey || e.metaKey) && e.key === 't') {
            e.preventDefault();
            window.location.href = '/tasks';
        }
    }

    async loadInitialData() {
        try {
            const response = await fetch('/api/status');
            if (response.ok) {
                const data = await response.json();
                this.updateStatus(data);
            }
        } catch (error) {
            console.error('Failed to load initial data:', error);
        }
    }

    updateStatus(data) {
        // Update status indicators if they exist
        const elements = {
            documentCount: document.getElementById('documentCount'),
            noteCount: document.getElementById('noteCount'),
            taskCount: document.getElementById('taskCount'),
            projectCount: document.getElementById('projectCount')
        };

        // For now, show placeholder data
        if (elements.documentCount) elements.documentCount.textContent = '0';
        if (elements.noteCount) elements.noteCount.textContent = '0';
        if (elements.taskCount) elements.taskCount.textContent = '0';
        if (elements.projectCount) elements.projectCount.textContent = '0';
    }

    // Utility methods
    async apiCall(endpoint, options = {}) {
        const startTime = Date.now();
        const method = options.method || 'GET';

        try {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                },
            };

            const response = await fetch(endpoint, { ...defaultOptions, ...options });
            const duration = Date.now() - startTime;

            // Log API call
            if (window.frontendLogger) {
                window.frontendLogger.logApiCall(method, endpoint, response.status, duration);
            }

            if (!response.ok) {
                const error = `API call failed: ${response.statusText}`;
                if (window.frontendLogger) {
                    window.frontendLogger.log('ERROR', error, {
                        endpoint: endpoint,
                        status: response.status,
                        method: method
                    });
                }
                throw new Error(error);
            }

            return response.json();

        } catch (error) {
            const duration = Date.now() - startTime;

            // Log API error
            if (window.frontendLogger) {
                window.frontendLogger.logApiCall(method, endpoint, 0, duration, error.message);
            }

            throw error;
        }
    }

    showNotification(message, type = 'info') {
        // Log notification
        if (window.frontendLogger) {
            const level = type === 'error' ? 'ERROR' : type === 'warning' ? 'WARNING' : 'INFO';
            window.frontendLogger.log(level, `Notification: ${message}`, { type: type });
        }

        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            backgroundColor: type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff',
            zIndex: '1000',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
}

// Initialize the application
const app = new Assistant();

// Export for use in other scripts
window.Assistant = Assistant;
window.app = app;
