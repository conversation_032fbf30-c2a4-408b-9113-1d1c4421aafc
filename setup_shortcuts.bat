@echo off
REM Personal Assistant - Setup Desktop Shortcuts
REM This script creates desktop shortcuts for easy access

echo ========================================
echo   Personal Assistant - Setup Shortcuts
echo ========================================
echo.

REM Try VBScript method first (most reliable)
echo Creating desktop shortcuts using VBScript...
cscript //nologo "%~dp0create_shortcuts.vbs"

if errorlevel 1 (
    echo WARNING: VBScript method failed, trying alternative method
    goto :create_basic_shortcuts
) else (
    echo.
    echo Desktop shortcuts created successfully!
    goto :end
)

:create_basic_shortcuts
echo Creating basic shortcuts...

REM Create basic shortcuts using VBScript
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%temp%\create_shortcut.vbs"
echo sLinkFile = "%USERPROFILE%\Desktop\Start Personal Assistant.lnk" >> "%temp%\create_shortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%temp%\create_shortcut.vbs"
echo oLink.TargetPath = "%~dp0start_assistant.bat" >> "%temp%\create_shortcut.vbs"
echo oLink.WorkingDirectory = "%~dp0" >> "%temp%\create_shortcut.vbs"
echo oLink.Description = "Start Personal Assistant" >> "%temp%\create_shortcut.vbs"
echo oLink.Save >> "%temp%\create_shortcut.vbs"

cscript //nologo "%temp%\create_shortcut.vbs"
if errorlevel 1 (
    echo ERROR: Failed to create start shortcut
) else (
    echo ✅ Created: Start Personal Assistant.lnk
)

REM Create stop shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%temp%\create_shortcut2.vbs"
echo sLinkFile = "%USERPROFILE%\Desktop\Stop Personal Assistant.lnk" >> "%temp%\create_shortcut2.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%temp%\create_shortcut2.vbs"
echo oLink.TargetPath = "%~dp0stop_assistant.bat" >> "%temp%\create_shortcut2.vbs"
echo oLink.WorkingDirectory = "%~dp0" >> "%temp%\create_shortcut2.vbs"
echo oLink.Description = "Stop Personal Assistant" >> "%temp%\create_shortcut2.vbs"
echo oLink.Save >> "%temp%\create_shortcut2.vbs"

cscript //nologo "%temp%\create_shortcut2.vbs"
if errorlevel 1 (
    echo ERROR: Failed to create stop shortcut
) else (
    echo ✅ Created: Stop Personal Assistant.lnk
)

REM Create web shortcut
echo [InternetShortcut] > "%USERPROFILE%\Desktop\Open Personal Assistant.url"
echo URL=http://localhost:8000 >> "%USERPROFILE%\Desktop\Open Personal Assistant.url"
echo IconFile=shell32.dll >> "%USERPROFILE%\Desktop\Open Personal Assistant.url"
echo IconIndex=14 >> "%USERPROFILE%\Desktop\Open Personal Assistant.url"

if exist "%USERPROFILE%\Desktop\Open Personal Assistant.url" (
    echo ✅ Created: Open Personal Assistant.url
) else (
    echo ERROR: Failed to create web shortcut
)

REM Cleanup temp files
del "%temp%\create_shortcut.vbs" >nul 2>&1
del "%temp%\create_shortcut2.vbs" >nul 2>&1

:end
echo.
echo ========================================
echo   Setup Complete!
echo ========================================
echo.
echo Desktop shortcuts have been created:
echo • Start Personal Assistant - Starts the application
echo • Stop Personal Assistant - Stops the application  
echo • Open Personal Assistant - Opens in web browser
echo.
echo Usage:
echo 1. Double-click "Start Personal Assistant" to launch
echo 2. Wait for the application to start
echo 3. Double-click "Open Personal Assistant" to use the app
echo 4. Double-click "Stop Personal Assistant" when done
echo.
pause
