"""Logging configuration for the Personal Assistant."""

import sys
from pathlib import Path
from typing import Optional

from loguru import logger

from .config import settings


def setup_logging(
    level: Optional[str] = None,
    log_file: Optional[str] = None,
) -> None:
    """Set up logging configuration."""
    # Remove default handler
    logger.remove()
    
    # Use settings if not provided
    if level is None:
        level = settings.logging.level
    if log_file is None:
        log_file = settings.logging.file
    
    # Ensure log directory exists
    Path(log_file).parent.mkdir(parents=True, exist_ok=True)
    
    # Console handler with colors
    logger.add(
        sys.stderr,
        level=level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True,
    )
    
    # File handler with rotation
    logger.add(
        log_file,
        level=level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=f"{settings.logging.max_size_mb} MB",
        retention=settings.logging.backup_count,
        compression="zip",
    )
    
    logger.info("Logging initialized")


# Initialize logging when module is imported
setup_logging()
