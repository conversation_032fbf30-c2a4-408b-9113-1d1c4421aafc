[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "assistant"
version = "0.1.0"
description = "A constellation of AI agents for personal knowledge management"
authors = [{name = "Your Name", email = "<EMAIL>"}]
license = {text = "Private"}
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    # Core dependencies
    "pydantic>=2.0.0",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",
    
    # AI/ML dependencies
    "ollama>=0.1.0",
    "sentence-transformers>=2.2.0",
    "transformers>=4.30.0",
    
    # Vector databases
    "chromadb>=0.4.0",
    
    # Document processing
    "unstructured>=0.10.0",
    "pypdf2>=3.0.0",
    "python-docx>=0.8.11",
    "pytesseract>=0.3.10",
    "pillow>=10.0.0",
    "opencv-python>=4.8.0",

    # Web scraping and URL processing
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "selenium>=4.15.0",
    "readability-lxml>=0.8.1",

    # Email integration
    "google-api-python-client>=2.100.0",
    "google-auth-httplib2>=0.1.1",
    "google-auth-oauthlib>=1.1.0",
    "exchangelib>=5.0.0",
    "imapclient>=2.3.1",
    "email-validator>=2.0.0",

    # Calendar integration
    "icalendar>=5.0.0",
    "recurring-ical-events>=2.0.0",

    # Contact management
    "vobject>=0.9.6.1",

    # Machine learning for adaptation
    "scikit-learn>=1.3.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    
    # Database
    "sqlalchemy>=2.0.0",
    "alembic>=1.11.0",
    
    # API and web
    "fastapi>=0.100.0",
    "uvicorn>=0.23.0",
    "click>=8.1.0",
    
    # Utilities
    "rich>=13.0.0",
    "loguru>=0.7.0",
    "httpx>=0.24.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.4.0",
]

[project.scripts]
assistant = "src.api.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --cov=src --cov-report=term-missing"
