{% extends "base.html" %}

{% block content %}
<div class="logs-page">
    <header class="page-header">
        <h1>System Logs</h1>
        <p class="page-subtitle">Monitor system activity and debug issues</p>
    </header>

    <!-- Log Controls -->
    <div class="log-controls">
        <div class="control-group">
            <label for="logLevel">Log Level:</label>
            <select id="logLevel">
                <option value="ALL">All Levels</option>
                <option value="DEBUG">Debug</option>
                <option value="INFO" selected>Info</option>
                <option value="WARNING">Warning</option>
                <option value="ERROR">Error</option>
            </select>
        </div>

        <div class="control-group">
            <label for="logLines">Lines:</label>
            <select id="logLines">
                <option value="50">50</option>
                <option value="100" selected>100</option>
                <option value="200">200</option>
                <option value="500">500</option>
                <option value="1000">1000</option>
            </select>
        </div>

        <div class="control-group">
            <button id="refreshLogs" class="btn btn-primary">
                <span class="btn-icon">🔄</span>
                Refresh
            </button>
        </div>

        <div class="control-group">
            <button id="downloadLogs" class="btn btn-secondary">
                <span class="btn-icon">📥</span>
                Download
            </button>
        </div>

        <div class="control-group">
            <button id="clearLogs" class="btn btn-danger">
                <span class="btn-icon">🗑️</span>
                Clear Logs
            </button>
        </div>

        <div class="control-group">
            <label class="checkbox-label">
                <input type="checkbox" id="autoRefresh">
                Auto-refresh (10s)
            </label>
        </div>
    </div>

    <!-- Log Stats -->
    <div class="log-stats">
        <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
                <div class="stat-number" id="totalLines">-</div>
                <div class="stat-label">Total Lines</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">📄</div>
            <div class="stat-content">
                <div class="stat-number" id="displayedLines">-</div>
                <div class="stat-label">Displayed</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">❌</div>
            <div class="stat-content">
                <div class="stat-number" id="errorCount">-</div>
                <div class="stat-label">Errors</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">⚠️</div>
            <div class="stat-content">
                <div class="stat-number" id="warningCount">-</div>
                <div class="stat-label">Warnings</div>
            </div>
        </div>
    </div>

    <!-- Log Search -->
    <div class="log-search">
        <input type="text" id="logSearch" placeholder="Search logs..." class="search-input">
        <button id="clearSearch" class="btn btn-secondary">Clear</button>
    </div>

    <!-- Log Display -->
    <div class="log-display">
        <div class="log-header">
            <span class="log-col-time">Timestamp</span>
            <span class="log-col-level">Level</span>
            <span class="log-col-location">Location</span>
            <span class="log-col-message">Message</span>
        </div>
        
        <div class="log-content" id="logContent">
            <div class="loading-message">Loading logs...</div>
        </div>
    </div>

    <!-- Frontend Logs Section -->
    <div class="frontend-logs-section">
        <h2>Frontend Logs</h2>
        <div class="frontend-controls">
            <button id="exportFrontendLogs" class="btn btn-secondary">
                <span class="btn-icon">📤</span>
                Export Frontend Logs
            </button>
            <button id="clearFrontendLogs" class="btn btn-secondary">
                <span class="btn-icon">🗑️</span>
                Clear Buffer
            </button>
            <div class="frontend-stats">
                <span>Buffer: <span id="frontendBufferSize">0</span> entries</span>
            </div>
        </div>
        
        <div class="frontend-log-display" id="frontendLogDisplay">
            <!-- Frontend logs will be populated here -->
        </div>
    </div>
</div>

<style>
.logs-page {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.log-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: center;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.control-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-group label {
    font-weight: 500;
    color: var(--text-secondary);
}

.control-group select {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--input-bg);
    color: var(--text-primary);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.log-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.stat-icon {
    font-size: 24px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: uppercase;
}

.log-search {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.search-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--input-bg);
    color: var(--text-primary);
}

.log-display {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 30px;
}

.log-header {
    display: grid;
    grid-template-columns: 180px 80px 200px 1fr;
    gap: 15px;
    padding: 12px 15px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 12px;
    text-transform: uppercase;
}

.log-content {
    max-height: 600px;
    overflow-y: auto;
}

.log-entry {
    display: grid;
    grid-template-columns: 180px 80px 200px 1fr;
    gap: 15px;
    padding: 8px 15px;
    border-bottom: 1px solid var(--border-color);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.log-entry:hover {
    background: var(--bg-secondary);
}

.log-entry.level-ERROR {
    background: rgba(239, 68, 68, 0.1);
    border-left: 3px solid #ef4444;
}

.log-entry.level-WARNING {
    background: rgba(245, 158, 11, 0.1);
    border-left: 3px solid #f59e0b;
}

.log-entry.level-DEBUG {
    background: rgba(107, 114, 128, 0.1);
    border-left: 3px solid #6b7280;
}

.log-timestamp {
    color: var(--text-secondary);
}

.log-level {
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    text-align: center;
}

.log-level.ERROR {
    background: #ef4444;
    color: white;
}

.log-level.WARNING {
    background: #f59e0b;
    color: white;
}

.log-level.INFO {
    background: #3b82f6;
    color: white;
}

.log-level.DEBUG {
    background: #6b7280;
    color: white;
}

.log-location {
    color: var(--text-secondary);
    font-size: 11px;
}

.log-message {
    color: var(--text-primary);
    word-break: break-word;
}

.loading-message {
    padding: 40px;
    text-align: center;
    color: var(--text-secondary);
}

.frontend-logs-section {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 2px solid var(--border-color);
}

.frontend-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.frontend-stats {
    color: var(--text-secondary);
    font-size: 14px;
}

.frontend-log-display {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
}

.frontend-log-entry {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.frontend-log-entry:last-child {
    border-bottom: none;
}

.frontend-log-meta {
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.frontend-log-message {
    color: var(--text-primary);
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', path='/js/logs.js') }}"></script>
{% endblock %}
