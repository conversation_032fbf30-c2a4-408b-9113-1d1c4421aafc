"""Task Manager Agent for the Assistant."""

import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass

from ..core.logging import logger
from ..core.config import settings
from ..knowledge.task_service import task_service
from ..knowledge.models import Task, TaskStatus, TaskPriority


@dataclass
class TaskReminder:
    """Represents a task reminder."""
    task_id: str
    reminder_time: datetime
    reminder_type: str  # 'due_soon', 'overdue', 'follow_up'
    message: str


@dataclass
class TaskSuggestion:
    """Represents a suggested task optimization."""
    task_id: str
    suggestion_type: str  # 'priority_change', 'due_date_change', 'merge', 'split'
    reasoning: str
    confidence: float
    suggested_changes: Dict[str, Any]


class TaskManagerAgent:
    """
    Agent responsible for intelligent task management and optimization.
    
    Key responsibilities:
    - Monitor task deadlines and send reminders
    - Suggest task prioritization based on patterns
    - Identify related tasks for grouping
    - Learn from user behavior to improve suggestions
    - Auto-prioritize tasks based on context and urgency
    """
    
    def __init__(self):
        self.config = settings.agents.task_manager
        self.learning_enabled = getattr(self.config, 'learning_enabled', True)
        self.reminder_system = getattr(self.config, 'reminder_system', True)
        self.auto_prioritize = getattr(self.config, 'auto_prioritize', True)
        
    async def process_tasks(self) -> Dict[str, Any]:
        """
        Process all tasks for optimization and reminders.
        
        Returns:
            Dictionary with processing results
        """
        logger.info("Starting task processing cycle")
        
        result = {
            'status': 'success',
            'tasks_processed': 0,
            'reminders_sent': 0,
            'suggestions_made': 0,
            'optimizations_applied': 0,
            'errors': []
        }
        
        try:
            # Get all active tasks
            tasks = await task_service.get_tasks(
                status=[TaskStatus.PENDING.value, TaskStatus.IN_PROGRESS.value],
                limit=1000
            )
            
            result['tasks_processed'] = len(tasks)
            
            # Process reminders
            if self.reminder_system:
                reminders = await self._check_reminders(tasks)
                result['reminders_sent'] = len(reminders)
                
                for reminder in reminders:
                    await self._send_reminder(reminder)
            
            # Generate task suggestions
            suggestions = await self._generate_suggestions(tasks)
            result['suggestions_made'] = len(suggestions)
            
            # Auto-apply safe optimizations
            if self.auto_prioritize:
                optimizations = await self._apply_auto_optimizations(tasks)
                result['optimizations_applied'] = len(optimizations)
            
        except Exception as e:
            logger.error(f"Error during task processing: {e}")
            result['errors'].append(str(e))
            result['status'] = 'error'
        
        logger.info(f"Task processing completed: {result}")
        return result
    
    async def prioritize_task(self, task_id: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Intelligently prioritize a task based on context and patterns.
        
        Args:
            task_id: Task ID to prioritize
            context: Additional context for prioritization
            
        Returns:
            Dictionary with prioritization result
        """
        try:
            task = await task_service.get_task(task_id)
            if not task:
                return {'status': 'error', 'message': 'Task not found'}
            
            # Calculate priority score
            priority_score = await self._calculate_priority_score(task, context)
            
            # Determine new priority level
            new_priority = self._score_to_priority(priority_score)
            
            # Update task if priority changed
            if new_priority != task.priority:
                await task_service.update_task(
                    task_id, 
                    priority=new_priority,
                    metadata={
                        **task.task_metadata,
                        'auto_prioritized': True,
                        'priority_score': priority_score,
                        'prioritized_at': datetime.now().isoformat()
                    }
                )
                
                logger.info(f"Auto-prioritized task {task_id}: {task.priority} -> {new_priority}")
                
                return {
                    'status': 'success',
                    'task_id': task_id,
                    'old_priority': task.priority,
                    'new_priority': new_priority,
                    'priority_score': priority_score,
                    'reasoning': self._generate_priority_reasoning(task, priority_score)
                }
            else:
                return {
                    'status': 'no_change',
                    'task_id': task_id,
                    'current_priority': task.priority,
                    'priority_score': priority_score
                }
                
        except Exception as e:
            logger.error(f"Error prioritizing task {task_id}: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def suggest_task_grouping(self, tasks: Optional[List[Task]] = None) -> List[Dict[str, Any]]:
        """
        Suggest groupings for related tasks.
        
        Args:
            tasks: List of tasks to analyze (if None, gets all active tasks)
            
        Returns:
            List of grouping suggestions
        """
        if tasks is None:
            tasks = await task_service.get_tasks(
                status=[TaskStatus.PENDING.value, TaskStatus.IN_PROGRESS.value],
                limit=1000
            )
        
        suggestions = []
        
        try:
            # Group tasks by similarity
            task_groups = await self._find_similar_tasks(tasks)
            
            for group in task_groups:
                if len(group['tasks']) > 1:
                    suggestion = {
                        'type': 'task_grouping',
                        'tasks': [task.id for task in group['tasks']],
                        'similarity_score': group['similarity_score'],
                        'suggested_project_name': group['suggested_name'],
                        'reasoning': group['reasoning'],
                        'confidence': group['confidence']
                    }
                    suggestions.append(suggestion)
            
        except Exception as e:
            logger.error(f"Error generating task grouping suggestions: {e}")
        
        return suggestions
    
    async def _check_reminders(self, tasks: List[Task]) -> List[TaskReminder]:
        """Check which tasks need reminders."""
        reminders = []
        now = datetime.now()
        
        for task in tasks:
            if not task.due_date:
                continue
                
            time_until_due = task.due_date - now
            
            # Overdue tasks
            if time_until_due.total_seconds() < 0:
                reminders.append(TaskReminder(
                    task_id=str(task.id),
                    reminder_time=now,
                    reminder_type='overdue',
                    message=f"Task '{task.title}' is overdue by {abs(time_until_due.days)} days"
                ))
            
            # Due soon (within 24 hours)
            elif time_until_due.total_seconds() < 86400:  # 24 hours
                reminders.append(TaskReminder(
                    task_id=str(task.id),
                    reminder_time=now,
                    reminder_type='due_soon',
                    message=f"Task '{task.title}' is due in {time_until_due.seconds // 3600} hours"
                ))
        
        return reminders
    
    async def _send_reminder(self, reminder: TaskReminder):
        """Send a task reminder (placeholder for notification system)."""
        logger.info(f"Task reminder: {reminder.message}")
        # TODO: Integrate with notification system
    
    async def _generate_suggestions(self, tasks: List[Task]) -> List[TaskSuggestion]:
        """Generate optimization suggestions for tasks."""
        suggestions = []
        
        # Find tasks that might need priority adjustment
        for task in tasks:
            if task.due_date and task.priority == TaskPriority.MEDIUM.value:
                days_until_due = (task.due_date - datetime.now()).days
                
                if days_until_due <= 1:
                    suggestions.append(TaskSuggestion(
                        task_id=str(task.id),
                        suggestion_type='priority_change',
                        reasoning='Task is due very soon',
                        confidence=0.9,
                        suggested_changes={'priority': TaskPriority.HIGH.value}
                    ))
        
        return suggestions
    
    async def _apply_auto_optimizations(self, tasks: List[Task]) -> List[str]:
        """Apply safe automatic optimizations."""
        optimizations = []
        
        for task in tasks:
            # Auto-prioritize overdue tasks
            if task.due_date and task.due_date < datetime.now() and task.priority < TaskPriority.HIGH.value:
                await task_service.update_task(
                    str(task.id),
                    priority=TaskPriority.HIGH.value
                )
                optimizations.append(f"Increased priority for overdue task: {task.title}")
        
        return optimizations
    
    async def _calculate_priority_score(self, task: Task, context: Optional[Dict[str, Any]]) -> float:
        """Calculate priority score for a task."""
        score = 0.5  # Base score
        
        # Due date factor
        if task.due_date:
            days_until_due = (task.due_date - datetime.now()).days
            if days_until_due <= 0:
                score += 0.4  # Overdue
            elif days_until_due <= 1:
                score += 0.3  # Due today/tomorrow
            elif days_until_due <= 7:
                score += 0.2  # Due this week
        
        # Source factor (email tasks might be more urgent)
        if task.source_type == 'email':
            score += 0.1
        
        # Keywords factor
        if task.keywords_found:
            urgent_keywords = ['urgent', 'asap', 'critical', 'deadline']
            if any(keyword in task.keywords_found for keyword in urgent_keywords):
                score += 0.2
        
        return min(score, 1.0)
    
    def _score_to_priority(self, score: float) -> int:
        """Convert priority score to priority level."""
        if score >= 0.8:
            return TaskPriority.CRITICAL.value
        elif score >= 0.6:
            return TaskPriority.HIGH.value
        elif score >= 0.4:
            return TaskPriority.MEDIUM.value
        else:
            return TaskPriority.LOW.value
    
    def _generate_priority_reasoning(self, task: Task, score: float) -> str:
        """Generate human-readable reasoning for priority change."""
        reasons = []
        
        if task.due_date:
            days_until_due = (task.due_date - datetime.now()).days
            if days_until_due <= 0:
                reasons.append("task is overdue")
            elif days_until_due <= 1:
                reasons.append("task is due very soon")
        
        if task.source_type == 'email':
            reasons.append("task was extracted from email")
        
        if not reasons:
            reasons.append("based on content analysis")
        
        return f"Priority adjusted because {', '.join(reasons)}"
    
    async def _find_similar_tasks(self, tasks: List[Task]) -> List[Dict[str, Any]]:
        """Find groups of similar tasks."""
        # Simple similarity based on title keywords
        groups = []
        processed_tasks = set()
        
        for task in tasks:
            if task.id in processed_tasks:
                continue
                
            similar_tasks = [task]
            task_keywords = set(task.title.lower().split())
            
            for other_task in tasks:
                if other_task.id == task.id or other_task.id in processed_tasks:
                    continue
                    
                other_keywords = set(other_task.title.lower().split())
                similarity = len(task_keywords & other_keywords) / len(task_keywords | other_keywords)
                
                if similarity > 0.3:  # 30% similarity threshold
                    similar_tasks.append(other_task)
                    processed_tasks.add(other_task.id)
            
            if len(similar_tasks) > 1:
                groups.append({
                    'tasks': similar_tasks,
                    'similarity_score': 0.5,  # Simplified
                    'suggested_name': f"Project: {task.title[:30]}...",
                    'reasoning': f"Tasks share common keywords",
                    'confidence': 0.7
                })
                
            processed_tasks.add(task.id)
        
        return groups


# Create global instance
task_manager_agent = TaskManagerAgent()
