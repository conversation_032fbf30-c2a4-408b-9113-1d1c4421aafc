"""
Specialized AI agents for different domains.

Each agent is responsible for specific types of tasks:
- Archivist: Document processing and categorization
- Research: Information retrieval and synthesis
- TaskManager: TODO and scheduling management
- Note<PERSON>eeper: Note organization and linking
- ProjectManager: Project tracking and milestones
- EmailProcessor: Email analysis and processing
- CalendarManager: Calendar and event management
- ContactManager: Contact quality and relationship management
"""

from .email_processor import email_processor_agent
from .calendar_manager import calendar_manager_agent
from .task_manager import task_manager_agent
from .contact_manager import contact_manager_agent
from .note_keeper import note_keeper_agent
from .project_manager import project_manager_agent
from .research import research_agent
from .archivist import archivist_agent

__all__ = [
    'email_processor_agent',
    'calendar_manager_agent',
    'task_manager_agent',
    'contact_manager_agent',
    'note_keeper_agent',
    'project_manager_agent',
    'research_agent',
    'archivist_agent'
]
