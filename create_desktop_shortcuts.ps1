# Create Desktop Shortcuts for Personal Assistant
# This script creates desktop shortcuts for starting and stopping the Personal Assistant

param(
    [switch]$AllUsers,  # Create shortcuts for all users (requires admin)
    [switch]$Force      # Overwrite existing shortcuts
)

# Color functions
function Write-Success($text) { Write-Host "[OK] $text" -ForegroundColor Green }
function Write-Warning($text) { Write-Host "[WARN] $text" -ForegroundColor Yellow }
function Write-Error($text) { Write-Host "[ERROR] $text" -ForegroundColor Red }
function Write-Info($text) { Write-Host "[INFO] $text" -ForegroundColor Blue }

function Create-DesktopShortcuts {
    Write-Host "`n========================================" -ForegroundColor Cyan
    Write-Host "   Creating Desktop Shortcuts" -ForegroundColor Yellow
    Write-Host "========================================`n" -ForegroundColor Cyan
    
    # Get the current script directory (where the assistant is located)
    $assistantPath = if ($MyInvocation.MyCommand.Path) {
        Split-Path -Parent $MyInvocation.MyCommand.Path
    } else {
        (Get-Location).Path
    }
    $assistantPath = $assistantPath.ToString()
    Write-Info "Assistant location: $assistantPath"
    
    # Determine desktop path
    if ($AllUsers) {
        $desktopPath = [Environment]::GetFolderPath("CommonDesktopDirectory")
        Write-Info "Creating shortcuts for all users"
    } else {
        $desktopPath = [Environment]::GetFolderPath("Desktop")
        Write-Info "Creating shortcuts for current user"
    }
    Write-Info "Desktop path: $desktopPath"
    
    # Create WScript Shell object for shortcuts
    $shell = New-Object -ComObject WScript.Shell
    
    # Shortcut 1: Start Personal Assistant
    $startShortcutPath = Join-Path $desktopPath "Start Personal Assistant.lnk"
    
    if ((Test-Path $startShortcutPath) -and -not $Force) {
        Write-Warning "Start shortcut already exists. Use -Force to overwrite."
    } else {
        try {
            $startShortcut = $shell.CreateShortcut($startShortcutPath)
            $startShortcut.TargetPath = "powershell.exe"
            $startShortcut.Arguments = "-ExecutionPolicy Bypass -File `"$assistantPath\start_assistant.ps1`""
            $startShortcut.WorkingDirectory = $assistantPath
            $startShortcut.Description = "Start the Personal Assistant application"
            $startShortcut.IconLocation = "shell32.dll,25"  # Green arrow icon
            $startShortcut.WindowStyle = 1  # Normal window
            $startShortcut.Save()
            Write-Success "Created: Start Personal Assistant.lnk"
        }
        catch {
            Write-Error "Failed to create start shortcut: $($_.Exception.Message)"
        }
    }
    
    # Shortcut 2: Stop Personal Assistant
    $stopShortcutPath = Join-Path $desktopPath "Stop Personal Assistant.lnk"
    
    if ((Test-Path $stopShortcutPath) -and -not $Force) {
        Write-Warning "Stop shortcut already exists. Use -Force to overwrite."
    } else {
        try {
            $stopShortcut = $shell.CreateShortcut($stopShortcutPath)
            $stopShortcut.TargetPath = "powershell.exe"
            $stopShortcut.Arguments = "-ExecutionPolicy Bypass -File `"$assistantPath\stop_assistant.ps1`""
            $stopShortcut.WorkingDirectory = $assistantPath
            $stopShortcut.Description = "Stop the Personal Assistant application"
            $stopShortcut.IconLocation = "shell32.dll,28"  # Red X icon
            $stopShortcut.WindowStyle = 1  # Normal window
            $stopShortcut.Save()
            Write-Success "Created: Stop Personal Assistant.lnk"
        }
        catch {
            Write-Error "Failed to create stop shortcut: $($_.Exception.Message)"
        }
    }
    
    # Shortcut 3: Open Personal Assistant (web browser)
    $openShortcutPath = Join-Path $desktopPath "Open Personal Assistant.lnk"
    
    if ((Test-Path $openShortcutPath) -and -not $Force) {
        Write-Warning "Open shortcut already exists. Use -Force to overwrite."
    } else {
        try {
            $openShortcut = $shell.CreateShortcut($openShortcutPath)
            $openShortcut.TargetPath = "http://localhost:8000"
            $openShortcut.Description = "Open Personal Assistant in web browser"
            $openShortcut.IconLocation = "shell32.dll,14"  # Globe icon
            $openShortcut.Save()
            Write-Success "Created: Open Personal Assistant.lnk"
        }
        catch {
            Write-Error "Failed to create open shortcut: $($_.Exception.Message)"
        }
    }
    
    # Shortcut 4: Personal Assistant Folder
    $folderShortcutPath = Join-Path $desktopPath "Personal Assistant Folder.lnk"
    
    if ((Test-Path $folderShortcutPath) -and -not $Force) {
        Write-Warning "Folder shortcut already exists. Use -Force to overwrite."
    } else {
        try {
            $folderShortcut = $shell.CreateShortcut($folderShortcutPath)
            $folderShortcut.TargetPath = $assistantPath
            $folderShortcut.Description = "Open Personal Assistant folder"
            $folderShortcut.IconLocation = "shell32.dll,4"  # Folder icon
            $folderShortcut.Save()
            Write-Success "Created: Personal Assistant Folder.lnk"
        }
        catch {
            Write-Error "Failed to create folder shortcut: $($_.Exception.Message)"
        }
    }
    
    # Create a batch file alternative for systems that don't allow PowerShell
    $batchShortcutPath = Join-Path $desktopPath "Start Personal Assistant (Batch).lnk"
    
    if ((Test-Path $batchShortcutPath) -and -not $Force) {
        Write-Warning "Batch shortcut already exists. Use -Force to overwrite."
    } else {
        try {
            $batchShortcut = $shell.CreateShortcut($batchShortcutPath)
            $batchShortcut.TargetPath = Join-Path $assistantPath "start_assistant.bat"
            $batchShortcut.WorkingDirectory = $assistantPath
            $batchShortcut.Description = "Start Personal Assistant (Batch version)"
            $batchShortcut.IconLocation = "shell32.dll,25"  # Green arrow icon
            $batchShortcut.WindowStyle = 1  # Normal window
            $batchShortcut.Save()
            Write-Success "Created: Start Personal Assistant (Batch).lnk"
        }
        catch {
            Write-Error "Failed to create batch shortcut: $($_.Exception.Message)"
        }
    }
    
    Write-Host "`n========================================" -ForegroundColor Cyan
    Write-Host "   Shortcuts Created Successfully!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    
    Write-Host "`nAvailable shortcuts on desktop:" -ForegroundColor Yellow
    Write-Host "• Start Personal Assistant - Starts the application" -ForegroundColor White
    Write-Host "• Stop Personal Assistant - Stops the application" -ForegroundColor White
    Write-Host "• Open Personal Assistant - Opens in web browser" -ForegroundColor White
    Write-Host "• Personal Assistant Folder - Opens the app folder" -ForegroundColor White
    Write-Host "• Start Personal Assistant (Batch) - Alternative starter" -ForegroundColor White
    
    Write-Host "`nUsage:" -ForegroundColor Yellow
    Write-Host "1. Double-click 'Start Personal Assistant' to launch" -ForegroundColor White
    Write-Host "2. Wait for the application to start (check console)" -ForegroundColor White
    Write-Host "3. Double-click 'Open Personal Assistant' to use the app" -ForegroundColor White
    Write-Host "4. Double-click 'Stop Personal Assistant' when done" -ForegroundColor White
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Main execution
try {
    if ($AllUsers -and -not (Test-Administrator)) {
        Write-Error "Creating shortcuts for all users requires administrator privileges"
        Write-Host "Please run this script as administrator or remove the -AllUsers parameter" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    Create-DesktopShortcuts
    
    Write-Host "`nDesktop shortcuts have been created successfully!" -ForegroundColor Green
    Write-Host "You can now easily start and stop the Personal Assistant from your desktop." -ForegroundColor Cyan
}
catch {
    Write-Error "An unexpected error occurred: $($_.Exception.Message)"
    Write-Host $_.ScriptStackTrace -ForegroundColor Red
}
finally {
    Read-Host "Press Enter to exit"
}
