# Simple Desktop Shortcuts Creator for Personal Assistant
# This script creates basic desktop shortcuts

Write-Host "Creating Personal Assistant Desktop Shortcuts..." -ForegroundColor Green

# Get current directory
$currentDir = (Get-Location).Path
Write-Host "Application directory: $currentDir" -ForegroundColor Blue

# Get desktop path
$desktop = [Environment]::GetFolderPath("Desktop")
Write-Host "Desktop path: $desktop" -ForegroundColor Blue

# Create WScript Shell object
$shell = New-Object -ComObject WScript.Shell

try {
    # Create Start shortcut
    $startShortcut = $shell.CreateShortcut("$desktop\Start Personal Assistant.lnk")
    $startShortcut.TargetPath = "powershell.exe"
    $startShortcut.Arguments = "-ExecutionPolicy Bypass -File `"$currentDir\start_assistant.ps1`""
    $startShortcut.WorkingDirectory = $currentDir
    $startShortcut.Description = "Start Personal Assistant"
    $startShortcut.Save()
    Write-Host "[OK] Created: Start Personal Assistant.lnk" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Failed to create start shortcut: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    # Create Stop shortcut
    $stopShortcut = $shell.CreateShortcut("$desktop\Stop Personal Assistant.lnk")
    $stopShortcut.TargetPath = "powershell.exe"
    $stopShortcut.Arguments = "-ExecutionPolicy Bypass -File `"$currentDir\stop_assistant.ps1`""
    $stopShortcut.WorkingDirectory = $currentDir
    $stopShortcut.Description = "Stop Personal Assistant"
    $stopShortcut.Save()
    Write-Host "[OK] Created: Stop Personal Assistant.lnk" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Failed to create stop shortcut: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    # Create Batch Start shortcut (alternative)
    $batchShortcut = $shell.CreateShortcut("$desktop\Start Personal Assistant (Batch).lnk")
    $batchShortcut.TargetPath = "$currentDir\start_assistant.bat"
    $batchShortcut.WorkingDirectory = $currentDir
    $batchShortcut.Description = "Start Personal Assistant (Batch version)"
    $batchShortcut.Save()
    Write-Host "[OK] Created: Start Personal Assistant (Batch).lnk" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Failed to create batch shortcut: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    # Create web shortcut
    $webShortcut = $shell.CreateShortcut("$desktop\Open Personal Assistant.lnk")
    $webShortcut.TargetPath = "http://localhost:8000"
    $webShortcut.Description = "Open Personal Assistant in browser"
    $webShortcut.Save()
    Write-Host "[OK] Created: Open Personal Assistant.lnk" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Failed to create web shortcut: $($_.Exception.Message)" -ForegroundColor Red
}

try {
    # Create folder shortcut
    $folderShortcut = $shell.CreateShortcut("$desktop\Personal Assistant Folder.lnk")
    $folderShortcut.TargetPath = $currentDir
    $folderShortcut.Description = "Open Personal Assistant folder"
    $folderShortcut.Save()
    Write-Host "[OK] Created: Personal Assistant Folder.lnk" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Failed to create folder shortcut: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nShortcut creation completed!" -ForegroundColor Yellow
Write-Host "`nUsage:" -ForegroundColor Cyan
Write-Host "1. Double-click 'Start Personal Assistant' to launch the app" -ForegroundColor White
Write-Host "2. Wait for startup to complete" -ForegroundColor White
Write-Host "3. Double-click 'Open Personal Assistant' to use the web interface" -ForegroundColor White
Write-Host "4. Double-click 'Stop Personal Assistant' when finished" -ForegroundColor White

Read-Host "`nPress Enter to exit"
