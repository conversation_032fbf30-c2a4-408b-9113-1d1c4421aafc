{% extends "base.html" %}

{% block content %}
<div class="search-page">
    <header class="page-header">
        <h1>Knowledge Search</h1>
        <p class="page-subtitle">Search across all your documents, notes, and data</p>
    </header>

    <!-- Search Interface -->
    <section class="search-interface">
        <div class="search-form">
            <div class="search-input-group">
                <input type="text" id="searchQuery" placeholder="Search your knowledge base..." autocomplete="off">
                <button class="search-btn" id="searchBtn">
                    <span class="search-icon">🔍</span>
                    Search
                </button>
            </div>
            
            <div class="search-filters">
                <select id="searchType">
                    <option value="all">All Content</option>
                    <option value="documents">Documents</option>
                    <option value="notes">Notes</option>
                    <option value="emails">Emails</option>
                    <option value="contacts">Contacts</option>
                </select>
                
                <select id="sortBy">
                    <option value="relevance">Relevance</option>
                    <option value="date">Date</option>
                    <option value="title">Title</option>
                </select>
                
                <button class="filter-btn" id="advancedFilters">
                    <span>⚙️</span> Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="advanced-filters" id="advancedFiltersPanel" style="display: none;">
            <div class="filter-group">
                <label>Date Range:</label>
                <input type="date" id="dateFrom">
                <span>to</span>
                <input type="date" id="dateTo">
            </div>
            
            <div class="filter-group">
                <label>File Type:</label>
                <select id="fileType">
                    <option value="">All Types</option>
                    <option value="pdf">PDF</option>
                    <option value="doc">Word</option>
                    <option value="txt">Text</option>
                    <option value="md">Markdown</option>
                </select>
            </div>
            
            <div class="filter-actions">
                <button class="btn btn-primary" id="applyFilters">Apply Filters</button>
                <button class="btn btn-secondary" id="clearFilters">Clear</button>
            </div>
        </div>
    </section>

    <!-- Search Results -->
    <section class="search-results" id="searchResultsSection" style="display: none;">
        <div class="results-header">
            <h2>Search Results</h2>
            <div class="results-meta" id="resultsMeta">
                <span id="resultsCount">0</span> results found
            </div>
        </div>
        
        <div class="results-list" id="resultsList">
            <!-- Results will be populated here -->
        </div>
        
        <div class="results-pagination" id="resultsPagination" style="display: none;">
            <button class="btn btn-secondary" id="prevPage">← Previous</button>
            <span class="page-info" id="pageInfo">Page 1 of 1</span>
            <button class="btn btn-secondary" id="nextPage">Next →</button>
        </div>
    </section>

    <!-- Recent Searches -->
    <section class="recent-searches">
        <h2>Recent Searches</h2>
        <div class="search-history" id="searchHistory">
            <div class="history-item">
                <span class="history-query">No recent searches</span>
            </div>
        </div>
    </section>

    <!-- Quick Search Suggestions -->
    <section class="search-suggestions">
        <h2>Quick Search</h2>
        <div class="suggestion-grid">
            <button class="suggestion-card" data-query="tasks due today">
                <div class="suggestion-icon">✅</div>
                <span>Tasks Due Today</span>
            </button>
            
            <button class="suggestion-card" data-query="recent emails">
                <div class="suggestion-icon">📧</div>
                <span>Recent Emails</span>
            </button>
            
            <button class="suggestion-card" data-query="meeting notes">
                <div class="suggestion-icon">📝</div>
                <span>Meeting Notes</span>
            </button>
            
            <button class="suggestion-card" data-query="project documents">
                <div class="suggestion-icon">📁</div>
                <span>Project Documents</span>
            </button>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<style>
.search-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.search-form {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
}

.search-input-group {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.search-input-group input {
    flex: 1;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-lg);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.search-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
}

.search-btn:hover {
    background-color: #0056b3;
}

.search-filters {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.search-filters select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.advanced-filters {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.filter-group label {
    min-width: 100px;
    font-weight: 500;
}

.filter-group input, .filter-group select {
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.filter-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.results-meta {
    color: var(--text-secondary);
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.result-item {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    transition: all 0.2s ease;
}

.result-item:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 2px 8px var(--shadow);
}

.result-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-sm);
}

.result-snippet {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
}

.result-meta {
    display: flex;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.suggestion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.suggestion-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.suggestion-card:hover {
    border-color: var(--accent-primary);
    background-color: var(--bg-primary);
}

.suggestion-icon {
    font-size: var(--font-size-xl);
}

.search-history {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
}

.history-item {
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
}

.history-item:hover {
    background-color: var(--bg-primary);
}

.history-item:last-child {
    border-bottom: none;
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

.results-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.page-info {
    color: var(--text-secondary);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class SearchManager {
    constructor() {
        this.currentPage = 1;
        this.resultsPerPage = 10;
        this.searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSearchHistory();
        
        // Focus search input
        document.getElementById('searchQuery').focus();
    }

    setupEventListeners() {
        // Search functionality
        document.getElementById('searchBtn').addEventListener('click', () => this.performSearch());
        document.getElementById('searchQuery').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.performSearch();
        });
        
        // Filters
        document.getElementById('advancedFilters').addEventListener('click', () => this.toggleAdvancedFilters());
        document.getElementById('applyFilters').addEventListener('click', () => this.performSearch());
        document.getElementById('clearFilters').addEventListener('click', () => this.clearFilters());
        
        // Quick suggestions
        document.querySelectorAll('.suggestion-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const query = e.currentTarget.dataset.query;
                document.getElementById('searchQuery').value = query;
                this.performSearch();
            });
        });
        
        // Pagination
        document.getElementById('prevPage')?.addEventListener('click', () => this.previousPage());
        document.getElementById('nextPage')?.addEventListener('click', () => this.nextPage());
    }

    async performSearch() {
        const query = document.getElementById('searchQuery').value.trim();
        if (!query) return;

        this.addToHistory(query);
        this.showLoading();

        try {
            // This would call your actual search API
            const response = await fetch('/api/search', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    query: query,
                    type: document.getElementById('searchType').value,
                    sort: document.getElementById('sortBy').value,
                    page: this.currentPage,
                    limit: this.resultsPerPage
                })
            });

            const data = await response.json();
            this.displayResults(data);
        } catch (error) {
            console.error('Search error:', error);
            this.showError('Search failed. Please try again.');
        }
    }

    displayResults(data) {
        const resultsSection = document.getElementById('searchResultsSection');
        const resultsList = document.getElementById('resultsList');
        const resultsCount = document.getElementById('resultsCount');

        resultsSection.style.display = 'block';
        
        if (data.status === 'success' && data.results) {
            resultsCount.textContent = data.total || data.results.length;
            
            if (data.results.length === 0) {
                resultsList.innerHTML = '<div class="no-results">No results found for your search.</div>';
                return;
            }

            const resultsHtml = data.results.map(result => `
                <div class="result-item">
                    <div class="result-title">${result.title || 'Untitled'}</div>
                    <div class="result-snippet">${result.snippet || result.content?.substring(0, 200) + '...' || 'No preview available'}</div>
                    <div class="result-meta">
                        <span>Type: ${result.type || 'Unknown'}</span>
                        <span>Date: ${result.date ? new Date(result.date).toLocaleDateString() : 'Unknown'}</span>
                        ${result.score ? `<span>Relevance: ${Math.round(result.score * 100)}%</span>` : ''}
                    </div>
                </div>
            `).join('');

            resultsList.innerHTML = resultsHtml;
        } else {
            resultsList.innerHTML = '<div class="error-message">Search service unavailable. Please try again later.</div>';
        }
    }

    showLoading() {
        const resultsList = document.getElementById('resultsList');
        resultsList.innerHTML = '<div class="loading-placeholder">Searching...</div>';
        document.getElementById('searchResultsSection').style.display = 'block';
    }

    showError(message) {
        const resultsList = document.getElementById('resultsList');
        resultsList.innerHTML = `<div class="error-message">${message}</div>`;
    }

    toggleAdvancedFilters() {
        const panel = document.getElementById('advancedFiltersPanel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }

    clearFilters() {
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';
        document.getElementById('fileType').value = '';
        document.getElementById('searchType').value = 'all';
        document.getElementById('sortBy').value = 'relevance';
    }

    addToHistory(query) {
        this.searchHistory = this.searchHistory.filter(item => item !== query);
        this.searchHistory.unshift(query);
        this.searchHistory = this.searchHistory.slice(0, 10); // Keep only last 10
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
        this.loadSearchHistory();
    }

    loadSearchHistory() {
        const historyContainer = document.getElementById('searchHistory');
        
        if (this.searchHistory.length === 0) {
            historyContainer.innerHTML = '<div class="history-item"><span class="history-query">No recent searches</span></div>';
            return;
        }

        const historyHtml = this.searchHistory.map(query => `
            <div class="history-item" onclick="searchManager.searchFromHistory('${query}')">
                <span class="history-query">${query}</span>
            </div>
        `).join('');

        historyContainer.innerHTML = historyHtml;
    }

    searchFromHistory(query) {
        document.getElementById('searchQuery').value = query;
        this.performSearch();
    }

    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.performSearch();
        }
    }

    nextPage() {
        this.currentPage++;
        this.performSearch();
    }
}

// Initialize search manager
let searchManager;
document.addEventListener('DOMContentLoaded', () => {
    searchManager = new SearchManager();
});
</script>
{% endblock %}
