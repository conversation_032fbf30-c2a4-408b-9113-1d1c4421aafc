{% extends "base.html" %}

{% block content %}
<div class="contacts-management">
    <header class="page-header">
        <h1>Contact Quality Management</h1>
        <p class="page-subtitle">AI-powered contact data validation and cleanup</p>
    </header>

    <!-- Contact Quality Status -->
    <section class="quality-status">
        <h2>Quality Overview</h2>
        <div class="status-grid">
            <div class="status-card" id="totalContactsCard">
                <div class="status-icon">👥</div>
                <div class="status-content">
                    <h3>Total Contacts</h3>
                    <div class="status-number" id="totalContacts">-</div>
                    <p class="status-label">Contacts in database</p>
                </div>
            </div>
            
            <div class="status-card" id="qualityScoreCard">
                <div class="status-icon">⭐</div>
                <div class="status-content">
                    <h3>Average Quality</h3>
                    <div class="status-number" id="averageQuality">-</div>
                    <p class="status-label">Quality score (0-100)</p>
                </div>
            </div>
            
            <div class="status-card" id="issuesCard">
                <div class="status-icon">⚠️</div>
                <div class="status-content">
                    <h3>Issues Found</h3>
                    <div class="status-number" id="issuesCount">-</div>
                    <p class="status-label">Contacts with issues</p>
                </div>
            </div>
            
            <div class="status-card" id="lastScanCard">
                <div class="status-icon">🔍</div>
                <div class="status-content">
                    <h3>Last Scan</h3>
                    <div class="status-number" id="lastScanTime">-</div>
                    <p class="status-label">Quality scan time</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Actions -->
    <section class="quick-actions">
        <h2>Quality Actions</h2>
        <div class="action-grid">
            <button class="action-card" id="scanQualityBtn">
                <div class="action-icon">🔍</div>
                <h3>Scan Quality</h3>
                <p>Analyze all contacts for quality issues</p>
            </button>
            
            <button class="action-card" id="autoFixBtn">
                <div class="action-icon">🔧</div>
                <h3>Auto-Fix Issues</h3>
                <p>Automatically fix common data problems</p>
            </button>
            
            <button class="action-card" id="addContactBtn">
                <div class="action-icon">➕</div>
                <h3>Add Contact</h3>
                <p>Create a new contact entry</p>
            </button>
            
            <button class="action-card" id="viewContactsBtn">
                <div class="action-icon">📋</div>
                <h3>View All</h3>
                <p>Browse all contacts and their quality</p>
            </button>
        </div>
    </section>

    <!-- Add Contact Form -->
    <section class="add-contact" id="addContactSection" style="display: none;">
        <h2>Add New Contact</h2>
        <div class="contact-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName">First Name</label>
                    <input type="text" id="firstName" placeholder="John">
                </div>
                <div class="form-group">
                    <label for="lastName">Last Name</label>
                    <input type="text" id="lastName" placeholder="Doe">
                </div>
            </div>
            
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" placeholder="<EMAIL>">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="company">Company</label>
                    <input type="text" id="company" placeholder="Acme Corp">
                </div>
                <div class="form-group">
                    <label for="jobTitle">Job Title</label>
                    <input type="text" id="jobTitle" placeholder="Software Engineer">
                </div>
            </div>
            
            <div class="form-group">
                <label for="phoneNumber">Phone Number</label>
                <input type="tel" id="phoneNumber" placeholder="(*************">
            </div>
            
            <div class="form-actions">
                <button class="btn btn-primary" id="saveContactBtn">Save Contact</button>
                <button class="btn btn-secondary" id="cancelContactBtn">Cancel</button>
            </div>
        </div>
    </section>

    <!-- Contacts List -->
    <section class="contacts-list" id="contactsListSection" style="display: none;">
        <h2>All Contacts</h2>
        <div class="list-controls">
            <button class="btn btn-secondary" id="refreshContactsBtn">🔄 Refresh</button>
            <button class="btn btn-secondary" id="hideContactsBtn">✕ Hide</button>
        </div>
        <div class="contacts-container" id="contactsContainer">
            <div class="loading-placeholder">Loading contacts...</div>
        </div>
    </section>

    <!-- Quality Scan Results -->
    <section class="scan-results" id="scanResultsSection" style="display: none;">
        <h2>Quality Scan Results</h2>
        <div class="scan-controls">
            <button class="btn btn-secondary" id="hideScanBtn">✕ Hide</button>
        </div>
        <div class="scan-summary" id="scanSummary">
            <!-- Scan summary will be populated -->
        </div>
        <div class="issues-list" id="issuesList">
            <!-- Issues will be populated -->
        </div>
    </section>

    <!-- Recent Activity -->
    <section class="recent-activity">
        <h2>Recent Activity</h2>
        <div class="activity-log" id="activityLog">
            <div class="activity-item">
                <div class="activity-icon">👥</div>
                <div class="activity-content">
                    <p class="activity-title">Contact quality system ready</p>
                    <p class="activity-time">Ready to scan and improve your contact data</p>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Loading Modal -->
<div class="modal" id="loadingModal" style="display: none;">
    <div class="modal-content">
        <div class="loading-spinner"></div>
        <h3>Processing...</h3>
        <p id="loadingMessage">Please wait while we process your request.</p>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.contacts-management {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxl);
}

.quality-status .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.contact-form {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    max-width: 600px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.form-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.contacts-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
}

.contact-item {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.contact-item:hover {
    background-color: var(--bg-primary);
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.contact-name {
    font-weight: 600;
    color: var(--text-primary);
}

.contact-email {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.quality-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.quality-excellent {
    background-color: #28a745;
    color: white;
}

.quality-good {
    background-color: #ffc107;
    color: #000;
}

.quality-poor {
    background-color: #fd7e14;
    color: white;
}

.quality-critical {
    background-color: #dc3545;
    color: white;
}

.contact-issues {
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.issue-item {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.issue-high {
    color: #dc3545;
}

.issue-medium {
    color: #fd7e14;
}

.issue-low {
    color: #ffc107;
}

.list-controls, .scan-controls {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.scan-summary {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--accent-primary);
}

.stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Contact Quality Manager
class ContactQualityManager {
    constructor() {
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadContactStats();
    }

    setupEventListeners() {
        // Main action buttons
        document.getElementById('scanQualityBtn').addEventListener('click', () => this.scanQuality());
        document.getElementById('autoFixBtn').addEventListener('click', () => this.autoFixIssues());
        document.getElementById('addContactBtn').addEventListener('click', () => this.showAddContact());
        document.getElementById('viewContactsBtn').addEventListener('click', () => this.showContacts());
        
        // Form actions
        document.getElementById('saveContactBtn').addEventListener('click', () => this.saveContact());
        document.getElementById('cancelContactBtn').addEventListener('click', () => this.hideAddContact());
        
        // List controls
        document.getElementById('refreshContactsBtn')?.addEventListener('click', () => this.refreshContacts());
        document.getElementById('hideContactsBtn')?.addEventListener('click', () => this.hideContacts());
        document.getElementById('hideScanBtn')?.addEventListener('click', () => this.hideScanResults());
    }

    async loadContactStats() {
        try {
            const response = await fetch('/api/contacts');
            const data = await response.json();
            
            if (data.status === 'success') {
                document.getElementById('totalContacts').textContent = data.total;
                // Other stats would be calculated from the contact data
                document.getElementById('averageQuality').textContent = '85';
                document.getElementById('issuesCount').textContent = '3';
                document.getElementById('lastScanTime').textContent = 'Never';
            }
        } catch (error) {
            console.error('Error loading contact stats:', error);
        }
    }

    async scanQuality() {
        this.showLoading('Scanning contact quality...');
        
        try {
            const response = await fetch('/api/contacts/quality/scan');
            const data = await response.json();
            
            this.hideLoading();
            
            if (data.status === 'success') {
                this.showScanResults(data);
                this.addActivity('🔍', `Quality scan completed: ${data.total_contacts} contacts analyzed`);
                app.showNotification('Quality scan completed!', 'success');
            } else {
                this.showError(`Scan failed: ${data.message}`);
            }
        } catch (error) {
            this.hideLoading();
            this.showError(`Scan error: ${error.message}`);
        }
    }

    async autoFixIssues() {
        this.showLoading('Auto-fixing contact issues...');
        
        try {
            const response = await fetch('/api/contacts/quality/auto-fix', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    max_contacts: 10,
                    fix_types: ['phone_cleanup', 'email_validation'],
                    min_confidence: 0.8
                })
            });
            const data = await response.json();
            
            this.hideLoading();
            
            if (data.status === 'success') {
                this.addActivity('🔧', `Auto-fix completed: ${data.fixes_applied} issues fixed`);
                app.showNotification(`Fixed ${data.fixes_applied} issues!`, 'success');
                await this.loadContactStats();
            } else {
                this.showError(`Auto-fix failed: ${data.message}`);
            }
        } catch (error) {
            this.hideLoading();
            this.showError(`Auto-fix error: ${error.message}`);
        }
    }

    showAddContact() {
        document.getElementById('addContactSection').style.display = 'block';
        document.getElementById('contactsListSection').style.display = 'none';
        document.getElementById('scanResultsSection').style.display = 'none';
    }

    hideAddContact() {
        document.getElementById('addContactSection').style.display = 'none';
        this.clearForm();
    }

    async saveContact() {
        const contactData = {
            first_name: document.getElementById('firstName').value,
            last_name: document.getElementById('lastName').value,
            email: document.getElementById('email').value,
            company: document.getElementById('company').value,
            job_title: document.getElementById('jobTitle').value,
            phone_numbers: [{
                number: document.getElementById('phoneNumber').value,
                type: 'work'
            }]
        };

        if (!contactData.email) {
            this.showError('Email address is required');
            return;
        }

        this.showLoading('Saving contact...');

        try {
            const response = await fetch('/api/contacts', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(contactData)
            });
            const data = await response.json();

            this.hideLoading();

            if (data.status === 'success') {
                this.addActivity('➕', `Contact created: ${contactData.email}`);
                app.showNotification('Contact saved successfully!', 'success');
                this.hideAddContact();
                await this.loadContactStats();
            } else {
                this.showError(`Save failed: ${data.message}`);
            }
        } catch (error) {
            this.hideLoading();
            this.showError(`Save error: ${error.message}`);
        }
    }

    clearForm() {
        document.getElementById('firstName').value = '';
        document.getElementById('lastName').value = '';
        document.getElementById('email').value = '';
        document.getElementById('company').value = '';
        document.getElementById('jobTitle').value = '';
        document.getElementById('phoneNumber').value = '';
    }

    async showContacts() {
        document.getElementById('contactsListSection').style.display = 'block';
        document.getElementById('addContactSection').style.display = 'none';
        document.getElementById('scanResultsSection').style.display = 'none';
        
        await this.loadContacts();
    }

    async loadContacts() {
        const container = document.getElementById('contactsContainer');
        container.innerHTML = '<div class="loading-placeholder">Loading contacts...</div>';
        
        try {
            const response = await fetch('/api/contacts');
            const data = await response.json();
            
            if (data.status === 'success') {
                this.renderContacts(container, data.contacts);
            } else {
                container.innerHTML = `<div class="error-message">Error: ${data.message}</div>`;
            }
        } catch (error) {
            container.innerHTML = `<div class="error-message">Error loading contacts: ${error.message}</div>`;
        }
    }

    renderContacts(container, contacts) {
        if (!contacts || contacts.length === 0) {
            container.innerHTML = '<div class="no-contacts">No contacts found</div>';
            return;
        }

        const contactsHtml = contacts.map(contact => {
            const displayName = contact.display_name || `${contact.first_name || ''} ${contact.last_name || ''}`.trim() || 'Unknown';
            const qualityScore = Math.floor(Math.random() * 40) + 60; // Placeholder
            const qualityClass = qualityScore >= 90 ? 'excellent' : qualityScore >= 70 ? 'good' : qualityScore >= 50 ? 'poor' : 'critical';
            
            return `
                <div class="contact-item">
                    <div class="contact-header">
                        <div>
                            <div class="contact-name">${displayName}</div>
                            <div class="contact-email">${contact.email || 'No email'}</div>
                        </div>
                        <span class="quality-badge quality-${qualityClass}">Quality: ${qualityScore}%</span>
                    </div>
                    <div class="contact-details">
                        ${contact.company ? `<div><strong>Company:</strong> ${contact.company}</div>` : ''}
                        ${contact.job_title ? `<div><strong>Title:</strong> ${contact.job_title}</div>` : ''}
                        ${contact.phone_numbers && contact.phone_numbers.length > 0 ? 
                            `<div><strong>Phone:</strong> ${contact.phone_numbers[0].number || contact.phone_numbers[0]}</div>` : ''}
                    </div>
                    <div class="contact-meta">
                        <small>Created: ${new Date(contact.created_at).toLocaleDateString()}</small>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = contactsHtml;
    }

    showScanResults(scanData) {
        document.getElementById('scanResultsSection').style.display = 'block';
        
        const summary = document.getElementById('scanSummary');
        summary.innerHTML = `
            <h3>Scan Summary</h3>
            <div class="summary-stats">
                <div class="stat-item">
                    <div class="stat-number">${scanData.total_contacts || 0}</div>
                    <div class="stat-label">Contacts Scanned</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${scanData.issues_found || 0}</div>
                    <div class="stat-label">Issues Found</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">${Math.round((scanData.average_quality || 0) * 100)}%</div>
                    <div class="stat-label">Average Quality</div>
                </div>
            </div>
        `;
    }

    // Control methods
    async refreshContacts() { await this.loadContacts(); }
    hideContacts() { document.getElementById('contactsListSection').style.display = 'none'; }
    hideScanResults() { document.getElementById('scanResultsSection').style.display = 'none'; }

    addActivity(icon, message) {
        const activityLog = document.getElementById('activityLog');
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        
        activityItem.innerHTML = `
            <div class="activity-icon">${icon}</div>
            <div class="activity-content">
                <p class="activity-title">${message}</p>
                <p class="activity-time">${new Date().toLocaleString()}</p>
            </div>
        `;
        
        activityLog.insertBefore(activityItem, activityLog.firstChild);
        
        // Keep only last 10 items
        while (activityLog.children.length > 10) {
            activityLog.removeChild(activityLog.lastChild);
        }
    }

    showLoading(message) {
        document.getElementById('loadingMessage').textContent = message;
        document.getElementById('loadingModal').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingModal').style.display = 'none';
    }

    showError(message) {
        app.showNotification(message, 'error');
    }
}

// Initialize contact quality manager when page loads
document.addEventListener('DOMContentLoaded', () => {
    new ContactQualityManager();
});
</script>
{% endblock %}
