#!/usr/bin/env python3
"""
Test the GmailService directly to debug authentication issues.
"""

import sys
import asyncio
sys.path.insert(0, '.')

async def test_gmail_service():
    """Test the Gmail service authentication."""
    print("🧪 Testing GmailService Authentication")
    print("=" * 40)
    
    try:
        from src.integrations.gmail.gmail_service import gmail_service
        print("✅ GmailService imported successfully")
        
        print("📁 Checking file paths...")
        print(f"   Credentials: {gmail_service.credentials_file}")
        print(f"   Token: {gmail_service.token_file}")
        
        import os
        creds_exist = os.path.exists(gmail_service.credentials_file)
        token_exist = os.path.exists(gmail_service.token_file)
        
        print(f"   Credentials exist: {'✅' if creds_exist else '❌'}")
        print(f"   Token exists: {'✅' if token_exist else '❌'}")
        
        if not creds_exist:
            print(f"❌ Credentials file not found: {gmail_service.credentials_file}")
            return False
            
        if not token_exist:
            print(f"❌ Token file not found: {gmail_service.token_file}")
            return False
        
        print("\n🔐 Attempting authentication...")
        success = await gmail_service.authenticate()
        
        if success:
            print(f"✅ Authentication successful!")
            print(f"📧 User email: {gmail_service.user_email}")
            print(f"🔗 Service connected: {gmail_service.service is not None}")
            
            # Test a simple API call
            print("\n🧪 Testing API call...")
            try:
                profile = gmail_service.service.users().getProfile(userId='me').execute()
                print(f"✅ API test successful: {profile.get('emailAddress')}")
                return True
            except Exception as e:
                print(f"❌ API test failed: {e}")
                return False
        else:
            print("❌ Authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Gmail service: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_gmail_integration():
    """Test the Gmail integration."""
    print("\n🧪 Testing GmailIntegration")
    print("=" * 40)
    
    try:
        from src.integrations.gmail.gmail_integration import gmail_integration
        print("✅ GmailIntegration imported successfully")
        
        print("🔐 Attempting integration initialization...")
        success = await gmail_integration.initialize()
        
        if success:
            print("✅ Gmail integration initialized successfully!")
            status = gmail_integration.get_status()
            print(f"📊 Status: {status}")
            return True
        else:
            print("❌ Gmail integration initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Gmail integration: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    print("🔧 Gmail Service Debug Test")
    print("=" * 50)
    
    # Test the service first
    service_success = await test_gmail_service()
    
    # Test the integration
    integration_success = await test_gmail_integration()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    print(f"Gmail Service:     {'✅ Working' if service_success else '❌ Failed'}")
    print(f"Gmail Integration: {'✅ Working' if integration_success else '❌ Failed'}")
    
    if service_success and integration_success:
        print("\n🎉 All tests passed! Gmail should work in the web interface.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
