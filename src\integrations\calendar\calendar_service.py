"""Calendar service for managing calendar integrations."""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from ...core.logging import logger
from ...core.config import settings
from .google_calendar import GoogleCalendarClient


class CalendarService:
    """
    Main calendar service that coordinates calendar operations across providers.
    """
    
    def __init__(self):
        self.config = settings.integrations.calendar
        self.google_client = None
        
    async def initialize(self) -> bool:
        """Initialize calendar service and authenticate with providers."""
        logger.info("Initializing calendar service...")
        
        success = True
        
        # Initialize Google Calendar client
        if self.config.providers.google.enabled:
            try:
                self.google_client = GoogleCalendarClient()
                if await self.google_client.authenticate():
                    logger.info("Successfully initialized Google Calendar")
                else:
                    logger.error("Failed to authenticate Google Calendar")
                    success = False
            except Exception as e:
                logger.error(f"Error initializing Google Calendar: {e}")
                success = False
        
        return success
    
    async def sync_calendars(self) -> Dict[str, Any]:
        """
        Sync events from all configured calendars.
        
        Returns:
            Dictionary with sync results
        """
        logger.info("Starting calendar sync...")
        
        results = {
            'status': 'success',
            'calendars_synced': 0,
            'total_events': 0,
            'errors': []
        }
        
        try:
            if self.google_client:
                # Get events from Google Calendar
                google_result = await self._sync_google_calendar()
                results['calendars_synced'] += google_result.get('calendars_synced', 0)
                results['total_events'] += google_result.get('events_synced', 0)
                
                if google_result.get('errors'):
                    results['errors'].extend(google_result['errors'])
            
        except Exception as e:
            error_msg = f"Error during calendar sync: {e}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
            results['status'] = 'error'
        
        logger.info(f"Calendar sync completed: {results['calendars_synced']} calendars, {results['total_events']} events")
        
        return results
    
    async def get_availability(
        self,
        start_date: datetime,
        end_date: datetime,
        duration_minutes: int = 60
    ) -> List[Dict[str, Any]]:
        """
        Get availability across all calendars.
        
        Args:
            start_date: Start of availability window
            end_date: End of availability window
            duration_minutes: Minimum duration needed
            
        Returns:
            List of available time slots
        """
        logger.info(f"Getting availability from {start_date} to {end_date}")
        
        if not self.google_client:
            logger.warning("Google Calendar not initialized")
            return []
        
        try:
            # Get busy times from Google Calendar
            busy_times = await self.google_client.get_availability(
                start_date, end_date, ['primary']
            )
            
            # Calculate free slots
            free_slots = self._calculate_free_slots(
                start_date, end_date, busy_times, duration_minutes
            )
            
            logger.info(f"Found {len(free_slots)} available time slots")
            
            return free_slots
            
        except Exception as e:
            logger.error(f"Error getting availability: {e}")
            return []
    
    async def create_event(
        self,
        title: str,
        start_time: datetime,
        end_time: datetime,
        description: Optional[str] = None,
        location: Optional[str] = None,
        attendees: Optional[List[str]] = None,
        calendar_id: str = 'primary'
    ) -> Optional[Dict[str, Any]]:
        """
        Create a new calendar event.
        
        Args:
            title: Event title
            start_time: Event start time
            end_time: Event end time
            description: Event description
            location: Event location
            attendees: List of attendee emails
            calendar_id: Calendar to create event in
            
        Returns:
            Created event data or None if failed
        """
        logger.info(f"Creating event: {title}")
        
        if not self.google_client:
            logger.error("Google Calendar not initialized")
            return None
        
        try:
            event = await self.google_client.create_event(
                calendar_id=calendar_id,
                summary=title,
                start_time=start_time,
                end_time=end_time,
                description=description,
                location=location,
                attendees=attendees
            )
            
            if event:
                logger.info(f"Successfully created event: {title}")
            
            return event
            
        except Exception as e:
            logger.error(f"Error creating event: {e}")
            return None
    
    async def get_events(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        calendar_id: str = 'primary'
    ) -> List[Dict[str, Any]]:
        """
        Get events from a specific calendar.
        
        Args:
            start_date: Start date for events
            end_date: End date for events
            calendar_id: Calendar ID to get events from
            
        Returns:
            List of events
        """
        if not self.google_client:
            logger.error("Google Calendar not initialized")
            return []
        
        try:
            events = await self.google_client.get_events(
                calendar_id=calendar_id,
                start_time=start_date,
                end_time=end_date
            )
            
            logger.info(f"Retrieved {len(events)} events from calendar {calendar_id}")
            
            return events
            
        except Exception as e:
            logger.error(f"Error getting events: {e}")
            return []
    
    async def get_calendars(self) -> List[Dict[str, Any]]:
        """
        Get list of all accessible calendars.
        
        Returns:
            List of calendar information
        """
        if not self.google_client:
            logger.error("Google Calendar not initialized")
            return []
        
        try:
            calendars = await self.google_client.get_all_calendars()
            
            logger.info(f"Retrieved {len(calendars)} calendars")
            
            return calendars
            
        except Exception as e:
            logger.error(f"Error getting calendars: {e}")
            return []
    
    async def _sync_google_calendar(self) -> Dict[str, Any]:
        """Sync events from Google Calendar."""
        result = {
            'calendars_synced': 0,
            'events_synced': 0,
            'errors': []
        }
        
        try:
            # Get configured calendars
            calendars_config = self.config.providers.google.calendars
            
            for calendar_name, calendar_id in calendars_config.items():
                if calendar_id:  # Skip empty calendar IDs
                    try:
                        events = await self.google_client.get_events(
                            calendar_id=calendar_id,
                            start_time=datetime.now() - timedelta(days=7),
                            end_time=datetime.now() + timedelta(days=30)
                        )
                        
                        result['calendars_synced'] += 1
                        result['events_synced'] += len(events)
                        
                        logger.info(f"Synced {len(events)} events from {calendar_name} calendar")
                        
                    except Exception as e:
                        error_msg = f"Error syncing {calendar_name} calendar: {e}"
                        logger.error(error_msg)
                        result['errors'].append(error_msg)
            
        except Exception as e:
            error_msg = f"Error in Google Calendar sync: {e}"
            logger.error(error_msg)
            result['errors'].append(error_msg)
        
        return result
    
    def _calculate_free_slots(
        self,
        start_date: datetime,
        end_date: datetime,
        busy_times: List[Dict[str, Any]],
        duration_minutes: int
    ) -> List[Dict[str, Any]]:
        """Calculate free time slots from busy periods."""
        free_slots = []
        
        try:
            # Convert busy times to datetime objects
            busy_periods = []
            for busy in busy_times:
                start_str = busy.get('start')
                end_str = busy.get('end')
                
                if start_str and end_str:
                    # Parse ISO format timestamps
                    start_time = datetime.fromisoformat(start_str.replace('Z', '+00:00'))
                    end_time = datetime.fromisoformat(end_str.replace('Z', '+00:00'))
                    busy_periods.append((start_time, end_time))
            
            # Sort busy periods by start time
            busy_periods.sort(key=lambda x: x[0])
            
            # Find gaps between busy periods
            current_time = start_date
            duration_delta = timedelta(minutes=duration_minutes)
            
            for busy_start, busy_end in busy_periods:
                # If there's a gap before this busy period
                if current_time + duration_delta <= busy_start:
                    free_slots.append({
                        'start': current_time.isoformat(),
                        'end': busy_start.isoformat(),
                        'duration_minutes': int((busy_start - current_time).total_seconds() / 60)
                    })
                
                # Move current time to after this busy period
                current_time = max(current_time, busy_end)
            
            # Check for free time after the last busy period
            if current_time + duration_delta <= end_date:
                free_slots.append({
                    'start': current_time.isoformat(),
                    'end': end_date.isoformat(),
                    'duration_minutes': int((end_date - current_time).total_seconds() / 60)
                })
            
        except Exception as e:
            logger.error(f"Error calculating free slots: {e}")
        
        return free_slots


# Global calendar service instance
calendar_service = CalendarService()
