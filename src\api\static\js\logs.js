/**
 * Logs Page JavaScript
 * Handles log viewing, filtering, and management
 */

class LogsManager {
    constructor() {
        this.autoRefreshInterval = null;
        this.currentLogs = [];
        this.filteredLogs = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadLogs();
        this.updateFrontendLogStats();
        
        // Log page view
        if (window.frontendLogger) {
            window.frontendLogger.logPageView('/logs');
        }
    }

    initializeElements() {
        this.elements = {
            logLevel: document.getElementById('logLevel'),
            logLines: document.getElementById('logLines'),
            refreshLogs: document.getElementById('refreshLogs'),
            downloadLogs: document.getElementById('downloadLogs'),
            clearLogs: document.getElementById('clearLogs'),
            autoRefresh: document.getElementById('autoRefresh'),
            logSearch: document.getElementById('logSearch'),
            clearSearch: document.getElementById('clearSearch'),
            logContent: document.getElementById('logContent'),
            totalLines: document.getElementById('totalLines'),
            displayedLines: document.getElementById('displayedLines'),
            errorCount: document.getElementById('errorCount'),
            warningCount: document.getElementById('warningCount'),
            exportFrontendLogs: document.getElementById('exportFrontendLogs'),
            clearFrontendLogs: document.getElementById('clearFrontendLogs'),
            frontendBufferSize: document.getElementById('frontendBufferSize'),
            frontendLogDisplay: document.getElementById('frontendLogDisplay')
        };
    }

    setupEventListeners() {
        // Backend log controls
        this.elements.refreshLogs.addEventListener('click', () => this.loadLogs());
        this.elements.downloadLogs.addEventListener('click', () => this.downloadLogs());
        this.elements.clearLogs.addEventListener('click', () => this.clearLogs());
        
        this.elements.logLevel.addEventListener('change', () => this.loadLogs());
        this.elements.logLines.addEventListener('change', () => this.loadLogs());
        
        this.elements.autoRefresh.addEventListener('change', (e) => {
            if (e.target.checked) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });

        // Search functionality
        this.elements.logSearch.addEventListener('input', (e) => this.filterLogs(e.target.value));
        this.elements.clearSearch.addEventListener('click', () => {
            this.elements.logSearch.value = '';
            this.filterLogs('');
        });

        // Frontend log controls
        this.elements.exportFrontendLogs.addEventListener('click', () => this.exportFrontendLogs());
        this.elements.clearFrontendLogs.addEventListener('click', () => this.clearFrontendLogs());
    }

    async loadLogs() {
        try {
            this.showLoading();
            
            const level = this.elements.logLevel.value;
            const lines = this.elements.logLines.value;
            
            const response = await fetch(`/api/logs?lines=${lines}&level=${level}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.currentLogs = data.logs;
                this.updateLogStats(data);
                this.filterLogs(this.elements.logSearch.value);
                
                if (window.frontendLogger) {
                    window.frontendLogger.logUserAction('load_logs', { 
                        level: level, 
                        lines: lines,
                        total_logs: data.logs.length 
                    });
                }
            } else {
                this.showError(`Failed to load logs: ${data.message}`);
            }
            
        } catch (error) {
            this.showError(`Error loading logs: ${error.message}`);
            if (window.frontendLogger) {
                window.frontendLogger.log('ERROR', 'Failed to load logs', { error: error.message });
            }
        }
    }

    filterLogs(searchTerm) {
        if (!searchTerm) {
            this.filteredLogs = [...this.currentLogs];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredLogs = this.currentLogs.filter(log => 
                log.message.toLowerCase().includes(term) ||
                log.location.toLowerCase().includes(term) ||
                log.level.toLowerCase().includes(term)
            );
        }
        
        this.displayLogs();
        this.updateDisplayStats();
    }

    displayLogs() {
        const container = this.elements.logContent;
        
        if (this.filteredLogs.length === 0) {
            container.innerHTML = '<div class="loading-message">No logs found</div>';
            return;
        }

        const logsHtml = this.filteredLogs.map(log => `
            <div class="log-entry level-${log.level}">
                <div class="log-timestamp">${this.formatTimestamp(log.timestamp)}</div>
                <div class="log-level ${log.level}">${log.level}</div>
                <div class="log-location">${this.escapeHtml(log.location)}</div>
                <div class="log-message">${this.escapeHtml(log.message)}</div>
            </div>
        `).join('');

        container.innerHTML = logsHtml;
        
        // Scroll to bottom
        container.scrollTop = container.scrollHeight;
    }

    updateLogStats(data) {
        this.elements.totalLines.textContent = data.total_lines || 0;
        
        // Count errors and warnings
        const errorCount = this.currentLogs.filter(log => log.level === 'ERROR').length;
        const warningCount = this.currentLogs.filter(log => log.level === 'WARNING').length;
        
        this.elements.errorCount.textContent = errorCount;
        this.elements.warningCount.textContent = warningCount;
    }

    updateDisplayStats() {
        this.elements.displayedLines.textContent = this.filteredLogs.length;
    }

    async downloadLogs() {
        try {
            if (window.frontendLogger) {
                window.frontendLogger.logUserAction('download_logs');
            }
            
            const response = await fetch('/api/logs/download');
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `assistant_logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.log`;
                a.click();
                window.URL.revokeObjectURL(url);
                
                app.showNotification('Logs downloaded successfully', 'success');
            } else {
                throw new Error(`Download failed: ${response.statusText}`);
            }
            
        } catch (error) {
            this.showError(`Error downloading logs: ${error.message}`);
        }
    }

    async clearLogs() {
        if (!confirm('Are you sure you want to clear the log file? This will create a backup.')) {
            return;
        }

        try {
            if (window.frontendLogger) {
                window.frontendLogger.logUserAction('clear_logs');
            }
            
            const response = await fetch('/api/logs/clear', { method: 'POST' });
            const data = await response.json();
            
            if (data.status === 'success') {
                app.showNotification('Logs cleared successfully', 'success');
                this.loadLogs();
            } else {
                throw new Error(data.message);
            }
            
        } catch (error) {
            this.showError(`Error clearing logs: ${error.message}`);
        }
    }

    startAutoRefresh() {
        this.autoRefreshInterval = setInterval(() => {
            this.loadLogs();
        }, 10000); // 10 seconds
        
        if (window.frontendLogger) {
            window.frontendLogger.logUserAction('enable_auto_refresh');
        }
    }

    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
        
        if (window.frontendLogger) {
            window.frontendLogger.logUserAction('disable_auto_refresh');
        }
    }

    updateFrontendLogStats() {
        if (window.frontendLogger) {
            const bufferSize = window.frontendLogger.getLogBuffer().length;
            this.elements.frontendBufferSize.textContent = bufferSize;
            
            // Display recent frontend logs
            this.displayFrontendLogs();
        }
        
        // Update every 5 seconds
        setTimeout(() => this.updateFrontendLogStats(), 5000);
    }

    displayFrontendLogs() {
        if (!window.frontendLogger) return;
        
        const logs = window.frontendLogger.getLogBuffer().slice(-20); // Last 20 logs
        const container = this.elements.frontendLogDisplay;
        
        if (logs.length === 0) {
            container.innerHTML = '<div class="loading-message">No frontend logs in buffer</div>';
            return;
        }

        const logsHtml = logs.map(log => `
            <div class="frontend-log-entry">
                <div class="frontend-log-meta">
                    ${log.timestamp} | ${log.level} | ${log.url}
                </div>
                <div class="frontend-log-message">${this.escapeHtml(log.message)}</div>
                ${log.context && Object.keys(log.context).length > 0 ? 
                    `<div class="frontend-log-context">${this.escapeHtml(JSON.stringify(log.context, null, 2))}</div>` : 
                    ''}
            </div>
        `).join('');

        container.innerHTML = logsHtml;
    }

    exportFrontendLogs() {
        if (window.frontendLogger) {
            window.frontendLogger.exportLogs();
            window.frontendLogger.logUserAction('export_frontend_logs');
        }
    }

    clearFrontendLogs() {
        if (window.frontendLogger) {
            window.frontendLogger.clearBuffer();
            this.updateFrontendLogStats();
            app.showNotification('Frontend log buffer cleared', 'success');
        }
    }

    formatTimestamp(timestamp) {
        if (!timestamp) return '';
        try {
            return new Date(timestamp).toLocaleString();
        } catch {
            return timestamp;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoading() {
        this.elements.logContent.innerHTML = '<div class="loading-message">Loading logs...</div>';
    }

    showError(message) {
        this.elements.logContent.innerHTML = `<div class="loading-message" style="color: var(--error-color);">${message}</div>`;
        app.showNotification(message, 'error');
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.logsManager = new LogsManager();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.logsManager) {
        window.logsManager.stopAutoRefresh();
    }
});
