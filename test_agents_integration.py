#!/usr/bin/env python3
"""
Agent Integration Test
Tests all the new agent implementations to ensure they work correctly.
"""

import asyncio
import sys
import os
from datetime import datetime, timed<PERSON>ta

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_task_manager_agent():
    """Test the task manager agent."""
    print("🔧 Testing Task Manager Agent...")
    
    try:
        from src.agents.task_manager import task_manager_agent
        
        # Test processing tasks (with empty list for now)
        result = await task_manager_agent.process_tasks()
        print(f"   ✅ Process tasks: {result['status']}")
        
        # Test priority calculation
        mock_task_id = "test-task-123"
        context = {"urgency": "high", "source": "email"}
        
        # This will fail since task doesn't exist, but tests the method
        priority_result = await task_manager_agent.prioritize_task(mock_task_id, context)
        print(f"   ✅ Prioritize task: {priority_result['status']}")
        
        # Test task grouping suggestions
        suggestions = await task_manager_agent.suggest_task_grouping([])
        print(f"   ✅ Task grouping suggestions: {len(suggestions)} suggestions")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Task Manager Agent failed: {e}")
        return False


async def test_contact_manager_agent():
    """Test the contact manager agent."""
    print("👥 Testing Contact Manager Agent...")
    
    try:
        from src.agents.contact_manager import contact_manager_agent
        
        # Test processing contacts
        result = await contact_manager_agent.process_contacts()
        print(f"   ✅ Process contacts: {result['status']}")
        
        # Test merge suggestions
        suggestions = await contact_manager_agent.suggest_contact_merges(0.8)
        print(f"   ✅ Merge suggestions: {len(suggestions)} suggestions")
        
        # Test auto-categorization (will fail for non-existent contact)
        categorization = await contact_manager_agent.auto_categorize_contact("test-contact-123")
        print(f"   ✅ Auto-categorization: {categorization['status']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Contact Manager Agent failed: {e}")
        return False


async def test_note_keeper_agent():
    """Test the note keeper agent."""
    print("📝 Testing Note Keeper Agent...")
    
    try:
        from src.agents.note_keeper import note_keeper_agent
        
        # Create mock notes for testing
        mock_notes = [
            {
                "id": "note1",
                "title": "Python Programming Notes",
                "content": "Python is a powerful programming language used for web development, data science, and AI.",
                "tags": ["programming"],
                "created_at": datetime.now().isoformat()
            },
            {
                "id": "note2", 
                "title": "Machine Learning Concepts",
                "content": "Machine learning involves training models on data to make predictions and decisions.",
                "tags": ["ai"],
                "created_at": datetime.now().isoformat()
            }
        ]
        
        # Test processing notes
        result = await note_keeper_agent.process_notes(mock_notes)
        print(f"   ✅ Process notes: {result['status']}, {result['notes_processed']} notes processed")
        
        # Test analyzing note content
        analysis = await note_keeper_agent.analyze_note_content(mock_notes[0])
        print(f"   ✅ Analyze note content: {analysis['status']}")
        
        # Test suggesting note links
        links = await note_keeper_agent.suggest_note_links("note1", mock_notes)
        print(f"   ✅ Note link suggestions: {len(links)} links suggested")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Note Keeper Agent failed: {e}")
        return False


async def test_project_manager_agent():
    """Test the project manager agent."""
    print("📁 Testing Project Manager Agent...")
    
    try:
        from src.agents.project_manager import project_manager_agent
        
        # Create mock projects for testing
        mock_projects = [
            {
                "id": "project1",
                "name": "Website Redesign",
                "description": "Redesign the company website with modern UI",
                "status": "active",
                "priority": 2,
                "progress_percentage": 45,
                "due_date": (datetime.now() + timedelta(days=30)).isoformat(),
                "milestones": [
                    {
                        "id": "milestone1",
                        "title": "Design Mockups",
                        "due_date": (datetime.now() + timedelta(days=10)).isoformat(),
                        "status": "completed"
                    }
                ]
            }
        ]
        
        # Test processing projects
        result = await project_manager_agent.process_projects(mock_projects)
        print(f"   ✅ Process projects: {result['status']}, {result['projects_processed']} projects processed")
        
        # Test project health analysis
        health = await project_manager_agent.analyze_project_health("project1", mock_projects[0])
        print(f"   ✅ Project health analysis: {health['status']}")
        
        # Test prioritization suggestions
        prioritization = await project_manager_agent.suggest_project_prioritization(mock_projects)
        print(f"   ✅ Project prioritization: {len(prioritization)} suggestions")
        
        # Test milestone tracking
        milestones = await project_manager_agent.track_project_milestones("project1", mock_projects[0])
        print(f"   ✅ Milestone tracking: {milestones['status']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Project Manager Agent failed: {e}")
        return False


async def test_research_agent():
    """Test the research agent."""
    print("🔬 Testing Research Agent...")
    
    try:
        from src.agents.research import research_agent
        
        # Create mock content for testing
        mock_content = [
            {
                "id": "content1",
                "title": "AI Research Paper",
                "content": "This research explores machine learning applications in healthcare and demonstrates significant improvements in diagnosis accuracy.",
                "source": "research_paper",
                "created_at": datetime.now().isoformat()
            },
            {
                "id": "content2",
                "title": "Technology Trends",
                "content": "Cloud computing and artificial intelligence are transforming business operations across industries.",
                "source": "article",
                "created_at": datetime.now().isoformat()
            }
        ]
        
        # Test content analysis for research
        result = await research_agent.analyze_content_for_research(mock_content)
        print(f"   ✅ Analyze content for research: {result['status']}, {result['topics_identified']} topics identified")
        
        # Test extracting insights
        insights = await research_agent.extract_research_insights(
            mock_content[0]["content"], 
            mock_content[0]["source"]
        )
        print(f"   ✅ Extract research insights: {len(insights)} insights extracted")
        
        # Test research direction suggestions
        suggestions = await research_agent.suggest_research_directions(
            ["technology", "ai"], 
            mock_content
        )
        print(f"   ✅ Research direction suggestions: {len(suggestions)} suggestions")
        
        # Test research summary generation
        summary = await research_agent.generate_research_summary("technology", mock_content)
        print(f"   ✅ Research summary generation: {summary['status']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Research Agent failed: {e}")
        return False


async def test_archivist_agent():
    """Test the archivist agent."""
    print("📚 Testing Archivist Agent...")
    
    try:
        from src.agents.archivist import archivist_agent
        
        # Create mock content for testing
        mock_content = [
            {
                "id": "old_note1",
                "title": "Old Meeting Notes",
                "content": "Meeting notes from 2 years ago about project planning.",
                "type": "note",
                "created_at": (datetime.now() - timedelta(days=800)).isoformat(),
                "importance_score": 0.2,
                "last_accessed_at": (datetime.now() - timedelta(days=400)).isoformat()
            },
            {
                "id": "recent_note1",
                "title": "Recent Project Update",
                "content": "Latest updates on the current project status and next steps.",
                "type": "note", 
                "created_at": datetime.now().isoformat(),
                "importance_score": 0.8
            }
        ]
        
        # Test processing content for archiving
        result = await archivist_agent.process_content_for_archiving(mock_content)
        print(f"   ✅ Process content for archiving: {result['status']}, {len(result['archive_candidates'])} candidates")
        
        # Test archiving a specific item
        archive_result = await archivist_agent.archive_item(mock_content[0], "test archiving")
        print(f"   ✅ Archive item: {archive_result['status']}")
        
        # Test searching archive
        search_results = await archivist_agent.search_archive("meeting", limit=10)
        print(f"   ✅ Search archive: {len(search_results)} results found")
        
        # Test getting archive stats
        stats = await archivist_agent.get_archive_stats()
        print(f"   ✅ Archive stats: {stats.total_items} total items")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Archivist Agent failed: {e}")
        return False


async def main():
    """Run all agent tests."""
    print("🚀 Starting Agent Integration Tests")
    print("=" * 50)
    
    tests = [
        test_task_manager_agent,
        test_contact_manager_agent,
        test_note_keeper_agent,
        test_project_manager_agent,
        test_research_agent,
        test_archivist_agent
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if await test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"🎯 Agent Integration Test Results")
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 All agent tests passed! Agents are ready for use.")
    else:
        print(f"⚠️  {total - passed} agent tests failed. Check the logs above.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
