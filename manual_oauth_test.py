#!/usr/bin/env python3
"""
Manual OAuth test to authenticate with Google APIs.
"""

import pickle
import os
import sys
import webbrowser
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    GOOGLE_AVAILABLE = True
    print("✅ Google API libraries are available")
except ImportError as e:
    print(f"❌ Google API libraries not available: {e}")
    print("Please install: pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")
    sys.exit(1)

def run_oauth_flow(service_name, credentials_file, token_file, scopes):
    """Run OAuth flow for a service."""
    print(f"\n=== {service_name} OAuth Flow ===")

    if not os.path.exists(credentials_file):
        print(f"❌ Credentials file not found: {credentials_file}")
        print(f"Expected location: {os.path.abspath(credentials_file)}")
        return False

    try:
        print(f"✅ Credentials file found: {credentials_file}")
        print(f"Starting OAuth flow for {service_name}...")
        print("🌐 This will open a browser window for authentication.")
        print("📝 Please complete the authentication in your browser.")

        flow = InstalledAppFlow.from_client_secrets_file(
            credentials_file, scopes
        )

        # Run the OAuth flow with more explicit settings
        print("🚀 Starting local server for OAuth callback...")
        credentials = flow.run_local_server(
            port=0,
            prompt='select_account',
            open_browser=True
        )

        # Save the credentials
        os.makedirs(os.path.dirname(token_file), exist_ok=True)
        with open(token_file, 'wb') as token:
            pickle.dump(credentials, token)

        print(f"✅ {service_name} authentication successful!")
        print(f"💾 Token saved to: {token_file}")

        # Test the API connection
        print(f"🧪 Testing {service_name} API connection...")

        if service_name == "Gmail":
            service = build('gmail', 'v1', credentials=credentials)
            profile = service.users().getProfile(userId='me').execute()
            email = profile.get('emailAddress', 'Unknown')
            print(f"📧 Successfully connected to Gmail: {email}")

        elif service_name == "Calendar":
            service = build('calendar', 'v3', credentials=credentials)
            calendar_list = service.calendarList().list().execute()
            count = len(calendar_list.get('items', []))
            print(f"📅 Successfully connected to Calendar: {count} calendars found")

        elif service_name == "Contacts":
            service = build('people', 'v1', credentials=credentials)
            profile = service.people().get(
                resourceName='people/me',
                personFields='names,emailAddresses'
            ).execute()

            name = "Unknown"
            if 'names' in profile and profile['names']:
                name = profile['names'][0].get('displayName', 'Unknown')

            print(f"👤 Successfully connected to Contacts: {name}")

        return True

    except Exception as e:
        print(f"❌ OAuth flow failed for {service_name}: {e}")
        print(f"💡 Common issues:")
        print(f"   - Make sure the Google Cloud project has the required APIs enabled")
        print(f"   - Check that the credentials file is valid JSON")
        print(f"   - Ensure you have internet connectivity")
        return False

def main():
    """Main function."""
    print("🔧 Google API Manual OAuth Authentication")
    print("=" * 45)
    print("This script will authenticate your Google integrations.")
    print("Make sure you have:")
    print("  ✅ Valid credentials files in config/ directory")
    print("  ✅ Google Cloud APIs enabled (Gmail, Calendar, People)")
    print("  ✅ Internet connection")
    print()

    if not GOOGLE_AVAILABLE:
        return

    # Check credentials files first
    credentials_exist = all([
        os.path.exists("config/gmail_personal_credentials.json"),
        os.path.exists("config/google_calendar_credentials.json"),
        os.path.exists("config/google_contacts_credentials.json")
    ])

    if not credentials_exist:
        print("❌ Missing credentials files!")
        print("Please ensure these files exist:")
        print("  - config/gmail_personal_credentials.json")
        print("  - config/google_calendar_credentials.json")
        print("  - config/google_contacts_credentials.json")
        return

    print("✅ All credentials files found!")

    # Gmail OAuth (most important)
    print("\n🎯 Starting with Gmail authentication...")
    gmail_success = run_oauth_flow(
        "Gmail",
        "config/gmail_personal_credentials.json",
        "data/tokens/gmail_personal_token.json",
        [
            'https://www.googleapis.com/auth/gmail.readonly',
            'https://www.googleapis.com/auth/gmail.send',
            'https://www.googleapis.com/auth/gmail.modify',
            'https://www.googleapis.com/auth/gmail.labels'
        ]
    )

    if gmail_success:
        print("\n🎉 Gmail OAuth completed successfully!")
        print("✅ You can now use the 'Connect Gmail' button in the web interface.")

        # Automatically continue with other services
        print("\n🔄 Continuing with Calendar and Contacts authentication...")

        # Calendar OAuth
        calendar_success = run_oauth_flow(
            "Calendar",
            "config/google_calendar_credentials.json",
            "data/tokens/google_calendar_token.json",
            [
                'https://www.googleapis.com/auth/calendar.readonly',
                'https://www.googleapis.com/auth/calendar.events'
            ]
        )

        # Contacts OAuth
        contacts_success = run_oauth_flow(
            "Contacts",
            "config/google_contacts_credentials.json",
            "data/tokens/google_contacts_token.json",
            [
                'https://www.googleapis.com/auth/contacts.readonly',
                'https://www.googleapis.com/auth/contacts',
                'https://www.googleapis.com/auth/userinfo.profile'
            ]
        )

        # Final summary
        print("\n" + "=" * 45)
        print("🏁 AUTHENTICATION SUMMARY")
        print("=" * 45)
        print(f"Gmail:    {'✅ Success' if gmail_success else '❌ Failed'}")
        print(f"Calendar: {'✅ Success' if calendar_success else '❌ Failed'}")
        print(f"Contacts: {'✅ Success' if contacts_success else '❌ Failed'}")

        if gmail_success and calendar_success and contacts_success:
            print("\n🎉 ALL GOOGLE SERVICES AUTHENTICATED SUCCESSFULLY!")
            print("🚀 You can now use all features in the web interface:")
            print("   • Connect Gmail ✅")
            print("   • Sync Recent Emails ✅")
            print("   • Priority Inbox ✅")
            print("   • Unread Emails ✅")
            print("   • Calendar Integration ✅")
            print("   • Contacts Integration ✅")
        else:
            print("\n⚠️  Some services failed. Gmail is working, which is the most important.")

    else:
        print("\n❌ Gmail OAuth failed.")
        print("🔧 Troubleshooting steps:")
        print("1. Check that config/gmail_personal_credentials.json exists and is valid")
        print("2. Ensure Gmail API is enabled in Google Cloud Console")
        print("3. Verify your Google account has access to the project")
        print("4. Try running the script again")

if __name__ == "__main__":
    main()
