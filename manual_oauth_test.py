#!/usr/bin/env python3
"""
Manual OAuth test to authenticate with Google APIs.
"""

import pickle
import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, '.')

try:
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    from google_auth_oauthlib.flow import InstalledAppFlow
    from googleapiclient.discovery import build
    GOOGLE_AVAILABLE = True
except ImportError:
    print("❌ Google API libraries not available")
    sys.exit(1)

def run_oauth_flow(service_name, credentials_file, token_file, scopes):
    """Run OAuth flow for a service."""
    print(f"\n=== {service_name} OAuth Flow ===")
    
    if not os.path.exists(credentials_file):
        print(f"❌ Credentials file not found: {credentials_file}")
        return False
    
    try:
        print(f"Starting OAuth flow for {service_name}...")
        print("This will open a browser window for authentication.")
        
        flow = InstalledAppFlow.from_client_secrets_file(
            credentials_file, scopes
        )
        
        # Run the OAuth flow
        credentials = flow.run_local_server(port=0)
        
        # Save the credentials
        os.makedirs(os.path.dirname(token_file), exist_ok=True)
        with open(token_file, 'wb') as token:
            pickle.dump(credentials, token)
        
        print(f"✅ {service_name} authentication successful!")
        print(f"Token saved to: {token_file}")
        
        # Test the API
        if service_name == "Gmail":
            service = build('gmail', 'v1', credentials=credentials)
            profile = service.users().getProfile(userId='me').execute()
            email = profile.get('emailAddress', 'Unknown')
            print(f"📧 Connected to Gmail: {email}")
            
        elif service_name == "Calendar":
            service = build('calendar', 'v3', credentials=credentials)
            calendar_list = service.calendarList().list().execute()
            count = len(calendar_list.get('items', []))
            print(f"📅 Connected to Calendar: {count} calendars found")
            
        elif service_name == "Contacts":
            service = build('people', 'v1', credentials=credentials)
            profile = service.people().get(
                resourceName='people/me',
                personFields='names,emailAddresses'
            ).execute()
            
            name = "Unknown"
            if 'names' in profile and profile['names']:
                name = profile['names'][0].get('displayName', 'Unknown')
            
            print(f"👤 Connected to Contacts: {name}")
        
        return True
        
    except Exception as e:
        print(f"❌ OAuth flow failed for {service_name}: {e}")
        return False

def main():
    """Main function."""
    print("Google API Manual OAuth Test")
    print("=" * 30)
    
    if not GOOGLE_AVAILABLE:
        return
    
    # Gmail OAuth
    gmail_success = run_oauth_flow(
        "Gmail",
        "config/gmail_personal_credentials.json",
        "data/tokens/gmail_personal_token.json",
        [
            'https://www.googleapis.com/auth/gmail.readonly',
            'https://www.googleapis.com/auth/gmail.send',
            'https://www.googleapis.com/auth/gmail.modify',
            'https://www.googleapis.com/auth/gmail.labels'
        ]
    )
    
    if gmail_success:
        print("\n🎉 Gmail OAuth completed successfully!")
        print("You should now be able to connect Gmail in the web interface.")
    else:
        print("\n❌ Gmail OAuth failed.")
        print("Please check your credentials file and try again.")
    
    # Ask if user wants to continue with other services
    if gmail_success:
        response = input("\nDo you want to authenticate Calendar and Contacts too? (y/n): ")
        if response.lower() == 'y':
            # Calendar OAuth
            calendar_success = run_oauth_flow(
                "Calendar",
                "config/google_calendar_credentials.json",
                "data/tokens/google_calendar_token.json",
                [
                    'https://www.googleapis.com/auth/calendar.readonly',
                    'https://www.googleapis.com/auth/calendar.events'
                ]
            )
            
            # Contacts OAuth
            contacts_success = run_oauth_flow(
                "Contacts",
                "config/google_contacts_credentials.json",
                "data/tokens/google_contacts_token.json",
                [
                    'https://www.googleapis.com/auth/contacts.readonly',
                    'https://www.googleapis.com/auth/contacts',
                    'https://www.googleapis.com/auth/userinfo.profile'
                ]
            )
            
            if calendar_success and contacts_success:
                print("\n🎉 All Google services authenticated successfully!")
            else:
                print("\n⚠️  Some services failed to authenticate.")

if __name__ == "__main__":
    main()
