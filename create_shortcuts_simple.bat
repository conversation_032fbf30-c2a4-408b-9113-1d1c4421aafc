@echo off
REM Simple Desktop Shortcuts Creator
REM This creates shortcuts using the most basic method possible

echo Creating Personal Assistant Desktop Shortcuts...
echo.

REM Get current directory
set "CURRENT_DIR=%~dp0"
echo Application directory: %CURRENT_DIR%

REM Create shortcuts using PowerShell one-liners (more reliable)
echo Creating Start shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Start Personal Assistant.lnk'); $Shortcut.TargetPath = '%CURRENT_DIR%start_assistant.bat'; $Shortcut.WorkingDirectory = '%CURRENT_DIR%'; $Shortcut.Save()"

if exist "%USERPROFILE%\Desktop\Start Personal Assistant.lnk" (
    echo ✓ Created: Start Personal Assistant.lnk
) else (
    echo ✗ Failed to create Start shortcut
)

echo Creating Stop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\Stop Personal Assistant.lnk'); $Shortcut.TargetPath = '%CURRENT_DIR%stop_assistant.bat'; $Shortcut.WorkingDirectory = '%CURRENT_DIR%'; $Shortcut.Save()"

if exist "%USERPROFILE%\Desktop\Stop Personal Assistant.lnk" (
    echo ✓ Created: Stop Personal Assistant.lnk
) else (
    echo ✗ Failed to create Stop shortcut
)

echo Creating Web shortcut...
echo [InternetShortcut] > "%USERPROFILE%\Desktop\Open Personal Assistant.url"
echo URL=http://localhost:8000 >> "%USERPROFILE%\Desktop\Open Personal Assistant.url"

if exist "%USERPROFILE%\Desktop\Open Personal Assistant.url" (
    echo ✓ Created: Open Personal Assistant.url
) else (
    echo ✗ Failed to create Web shortcut
)

echo.
echo Shortcut creation completed!
echo.
echo Check your desktop for:
echo - Start Personal Assistant.lnk
echo - Stop Personal Assistant.lnk  
echo - Open Personal Assistant.url
echo.
echo Usage:
echo 1. Double-click "Start Personal Assistant" to launch
echo 2. Wait for startup to complete (console window will appear)
echo 3. Double-click "Open Personal Assistant" to use the web interface
echo 4. Double-click "Stop Personal Assistant" when finished
echo.
pause
