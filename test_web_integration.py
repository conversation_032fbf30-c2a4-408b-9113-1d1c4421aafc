#!/usr/bin/env python3
"""
Web Interface Integration Test
Tests all the key API endpoints to ensure proper integration.
"""

import asyncio
import aiohttp
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

async def test_endpoint(session, method, endpoint, data=None, expected_status=200):
    """Test a single API endpoint."""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == 'GET':
            async with session.get(url) as response:
                result = await response.json()
                status = response.status
        elif method.upper() == 'POST':
            async with session.post(url, json=data) as response:
                result = await response.json()
                status = response.status
        else:
            print(f"❌ Unsupported method: {method}")
            return False
        
        if status == expected_status:
            print(f"✅ {method} {endpoint} - Status: {status}")
            return True, result
        else:
            print(f"❌ {method} {endpoint} - Expected: {expected_status}, Got: {status}")
            return False, result
            
    except Exception as e:
        print(f"❌ {method} {endpoint} - Error: {e}")
        return False, None

async def main():
    """Run integration tests."""
    print("🚀 Starting Web Interface Integration Tests")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        tests_passed = 0
        tests_total = 0
        
        # Test system status
        print("\n📊 Testing System Status")
        tests_total += 1
        success, data = await test_endpoint(session, 'GET', '/api/status')
        if success and data.get('status') == 'running':
            tests_passed += 1
            print(f"   System version: {data.get('version')}")
            print(f"   Agents enabled: {len([k for k, v in data.get('agents_enabled', {}).items() if v])}")
        
        # Test tasks API
        print("\n✅ Testing Tasks API")
        tests_total += 3
        
        # Get tasks
        success, data = await test_endpoint(session, 'GET', '/api/tasks')
        if success:
            tests_passed += 1
            print(f"   Found {len(data.get('tasks', []))} tasks")
        
        # Get task stats
        success, data = await test_endpoint(session, 'GET', '/api/tasks/stats')
        if success:
            tests_passed += 1
            stats = data.get('stats', {})
            print(f"   Task stats - Total: {stats.get('total')}, In Progress: {stats.get('in_progress')}")
        
        # Test task extraction
        task_text = "I need to follow up with the client about the project proposal and schedule a meeting."
        success, data = await test_endpoint(session, 'POST', '/api/tasks/extract', {'text': task_text})
        if success:
            tests_passed += 1
            print(f"   Extracted {len(data.get('tasks', []))} tasks from text")
        
        # Test contacts API
        print("\n👥 Testing Contacts API")
        tests_total += 2
        
        # Get contacts
        success, data = await test_endpoint(session, 'GET', '/api/contacts')
        if success:
            tests_passed += 1
            print(f"   Found {data.get('total', 0)} contacts")
        
        # Test contact quality scan
        success, data = await test_endpoint(session, 'GET', '/api/contacts/quality/scan')
        if success:
            tests_passed += 1
            print(f"   Scanned {data.get('contacts_scanned', 0)} contacts")
            print(f"   Found {data.get('contacts_with_issues', 0)} contacts with issues")
        
        # Test notes API
        print("\n📝 Testing Notes API")
        tests_total += 2
        
        # Get notes
        success, data = await test_endpoint(session, 'GET', '/api/notes')
        if success:
            tests_passed += 1
            print(f"   Found {len(data.get('notes', []))} notes")
        
        # Create a test note
        note_data = {
            'title': 'Integration Test Note',
            'content': 'This is a test note created during integration testing.',
            'category': 'test'
        }
        success, data = await test_endpoint(session, 'POST', '/api/notes', note_data)
        if success:
            tests_passed += 1
            print(f"   Created test note: {data.get('note', {}).get('title')}")
        
        # Test projects API
        print("\n📁 Testing Projects API")
        tests_total += 2
        
        # Get projects
        success, data = await test_endpoint(session, 'GET', '/api/projects')
        if success:
            tests_passed += 1
            print(f"   Found {len(data.get('projects', []))} projects")
        
        # Get project stats
        success, data = await test_endpoint(session, 'GET', '/api/projects/stats')
        if success:
            tests_passed += 1
            stats = data.get('stats', {})
            print(f"   Project stats - Active: {stats.get('active')}, Completed: {stats.get('completed')}")
        
        # Test email/Gmail API
        print("\n📧 Testing Email/Gmail API")
        tests_total += 1
        
        # Get Gmail status
        success, data = await test_endpoint(session, 'GET', '/api/gmail/status')
        if success:
            tests_passed += 1
            gmail_status = data.get('gmail_status', {})
            print(f"   Gmail authenticated: {gmail_status.get('authenticated')}")
            print(f"   Auto-label enabled: {gmail_status.get('auto_label_enabled')}")
        
        # Test chat API
        print("\n💬 Testing Chat API")
        tests_total += 1
        
        # Send chat message
        chat_data = {'message': 'Hello, this is a test message for integration testing.'}
        success, data = await test_endpoint(session, 'POST', '/api/chat', chat_data)
        if success:
            tests_passed += 1
            print(f"   Chat response received (length: {len(data.get('response', ''))})")
        
        # Test search API
        print("\n🔍 Testing Search API")
        tests_total += 1
        
        # Search
        search_data = {'query': 'test', 'limit': 5}
        success, data = await test_endpoint(session, 'POST', '/api/search', search_data)
        if success:
            tests_passed += 1
            print(f"   Search returned {len(data.get('results', []))} results")
        
        # Test settings API
        print("\n⚙️ Testing Settings API")
        tests_total += 1
        
        # Get settings
        success, data = await test_endpoint(session, 'GET', '/api/settings')
        if success:
            tests_passed += 1
            settings = data.get('settings', {})
            print(f"   Settings loaded - Theme: {settings.get('theme')}, Language: {settings.get('language')}")
        
        # Test calendar API
        print("\n📅 Testing Calendar API")
        tests_total += 1
        
        # Get upcoming events
        success, data = await test_endpoint(session, 'GET', '/api/calendar/events/upcoming')
        if success:
            tests_passed += 1
            print(f"   Found {len(data.get('events', []))} upcoming events")
        
        # Summary
        print("\n" + "=" * 50)
        print(f"🎯 Integration Test Results")
        print(f"Tests Passed: {tests_passed}/{tests_total}")
        print(f"Success Rate: {(tests_passed/tests_total)*100:.1f}%")
        
        if tests_passed == tests_total:
            print("🎉 All tests passed! Web interface is fully integrated.")
        else:
            print(f"⚠️  {tests_total - tests_passed} tests failed. Check the logs above.")
        
        print("\n🌐 Web Interface URLs:")
        print(f"   Dashboard: {BASE_URL}/")
        print(f"   Email: {BASE_URL}/email")
        print(f"   Tasks: {BASE_URL}/tasks")
        print(f"   Contacts: {BASE_URL}/contacts")
        print(f"   Notes: {BASE_URL}/notes")
        print(f"   Projects: {BASE_URL}/projects")
        print(f"   Chat: {BASE_URL}/chat")
        print(f"   Search: {BASE_URL}/search")
        print(f"   Settings: {BASE_URL}/settings")

if __name__ == "__main__":
    asyncio.run(main())
