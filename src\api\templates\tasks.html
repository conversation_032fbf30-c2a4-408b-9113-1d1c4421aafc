{% extends "base.html" %}

{% block content %}
<div class="tasks-page">
    <header class="page-header">
        <h1>Task Management</h1>
        <p class="page-subtitle">AI-powered task creation and management</p>
    </header>

    <!-- Task Stats -->
    <section class="task-stats">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📋</div>
                <div class="stat-content">
                    <h3>Total Tasks</h3>
                    <div class="stat-number" id="totalTasks">-</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">⏳</div>
                <div class="stat-content">
                    <h3>In Progress</h3>
                    <div class="stat-number" id="inProgressTasks">-</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <h3>Completed</h3>
                    <div class="stat-number" id="completedTasks">-</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">🔥</div>
                <div class="stat-content">
                    <h3>High Priority</h3>
                    <div class="stat-number" id="highPriorityTasks">-</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Actions -->
    <section class="quick-actions">
        <h2>Quick Actions</h2>
        <div class="action-grid">
            <button class="action-card" id="createTaskBtn">
                <div class="action-icon">➕</div>
                <h3>Create Task</h3>
                <p>Add a new task manually</p>
            </button>
            
            <button class="action-card" id="aiTaskBtn">
                <div class="action-icon">🤖</div>
                <h3>AI Task Creation</h3>
                <p>Let AI extract tasks from text</p>
            </button>
            
            <button class="action-card" id="emailTasksBtn">
                <div class="action-icon">📧</div>
                <h3>Email Tasks</h3>
                <p>View tasks created from emails</p>
            </button>
            
            <button class="action-card" id="refreshTasksBtn">
                <div class="action-icon">🔄</div>
                <h3>Refresh</h3>
                <p>Reload all tasks</p>
            </button>
        </div>
    </section>

    <!-- Task Filters -->
    <section class="task-filters">
        <div class="filter-bar">
            <select id="statusFilter">
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
            </select>
            
            <select id="priorityFilter">
                <option value="all">All Priority</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
            </select>
            
            <select id="sourceFilter">
                <option value="all">All Sources</option>
                <option value="manual">Manual</option>
                <option value="email">Email</option>
                <option value="ai_generated">AI Generated</option>
            </select>
            
            <button class="btn btn-primary" id="applyFiltersBtn">Apply Filters</button>
        </div>
    </section>

    <!-- Create Task Form -->
    <section class="create-task" id="createTaskSection" style="display: none;">
        <h2>Create New Task</h2>
        <div class="task-form">
            <div class="form-group">
                <label for="taskTitle">Title</label>
                <input type="text" id="taskTitle" placeholder="Enter task title">
            </div>
            
            <div class="form-group">
                <label for="taskDescription">Description</label>
                <textarea id="taskDescription" rows="3" placeholder="Task description (optional)"></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="taskPriority">Priority</label>
                    <select id="taskPriority">
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="low">Low</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="taskDueDate">Due Date</label>
                    <input type="date" id="taskDueDate">
                </div>
            </div>
            
            <div class="form-group">
                <label for="taskTags">Tags (comma-separated)</label>
                <input type="text" id="taskTags" placeholder="work, urgent, meeting">
            </div>
            
            <div class="form-actions">
                <button class="btn btn-primary" id="saveTaskBtn">Save Task</button>
                <button class="btn btn-secondary" id="cancelTaskBtn">Cancel</button>
            </div>
        </div>
    </section>

    <!-- AI Task Creation -->
    <section class="ai-task-creation" id="aiTaskSection" style="display: none;">
        <h2>AI Task Extraction</h2>
        <div class="ai-form">
            <div class="form-group">
                <label for="aiTaskText">Text to Analyze</label>
                <textarea id="aiTaskText" rows="6" placeholder="Paste email content, meeting notes, or any text. AI will extract actionable tasks..."></textarea>
            </div>
            
            <div class="form-actions">
                <button class="btn btn-primary" id="extractTasksBtn">Extract Tasks</button>
                <button class="btn btn-secondary" id="cancelAiTaskBtn">Cancel</button>
            </div>
            
            <div class="extracted-tasks" id="extractedTasks" style="display: none;">
                <h3>Extracted Tasks</h3>
                <div class="tasks-preview" id="tasksPreview">
                    <!-- Extracted tasks will appear here -->
                </div>
                <div class="preview-actions">
                    <button class="btn btn-success" id="saveExtractedBtn">Save All Tasks</button>
                    <button class="btn btn-secondary" id="editExtractedBtn">Edit Tasks</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Tasks List -->
    <section class="tasks-list">
        <div class="list-header">
            <h2>Your Tasks</h2>
            <div class="list-controls">
                <select id="sortBy">
                    <option value="created_at">Date Created</option>
                    <option value="due_date">Due Date</option>
                    <option value="priority">Priority</option>
                    <option value="title">Title</option>
                </select>
                <button class="btn btn-secondary" id="toggleViewBtn">📋 List View</button>
            </div>
        </div>
        
        <div class="tasks-container" id="tasksContainer">
            <div class="loading-placeholder">Loading tasks...</div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<style>
.tasks-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.stat-icon {
    font-size: var(--font-size-xxl);
}

.stat-content h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.stat-number {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--accent-primary);
}

.filter-bar {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.filter-bar select {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.task-form, .ai-form {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    max-width: 600px;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.form-group input, .form-group textarea, .form-group select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.tasks-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.task-item {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    transition: all 0.2s ease;
}

.task-item:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 2px 8px var(--shadow);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.task-title {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.task-priority {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.priority-high {
    background-color: #dc3545;
    color: white;
}

.priority-medium {
    background-color: #ffc107;
    color: #000;
}

.priority-low {
    background-color: #28a745;
    color: white;
}

.task-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    line-height: 1.5;
}

.task-meta {
    display: flex;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
}

.task-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.task-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-in_progress {
    background-color: #007bff;
    color: white;
}

.status-completed {
    background-color: #28a745;
    color: white;
}

.status-cancelled {
    background-color: #6c757d;
    color: white;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.list-controls {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.extracted-tasks {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.task-preview {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
}

.preview-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.loading-placeholder, .error-message, .no-tasks {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
}

.error-message {
    color: #dc3545;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class TaskManager {
    constructor() {
        this.currentFilter = { status: 'all', priority: 'all', source: 'all' };
        this.currentSort = 'created_at';
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadTasks();
        await this.loadTaskStats();
    }

    setupEventListeners() {
        // Quick actions
        document.getElementById('createTaskBtn').addEventListener('click', () => this.showCreateTask());
        document.getElementById('aiTaskBtn').addEventListener('click', () => this.showAiTask());
        document.getElementById('emailTasksBtn').addEventListener('click', () => this.filterEmailTasks());
        document.getElementById('refreshTasksBtn').addEventListener('click', () => this.loadTasks());
        
        // Form actions
        document.getElementById('saveTaskBtn').addEventListener('click', () => this.saveTask());
        document.getElementById('cancelTaskBtn').addEventListener('click', () => this.hideCreateTask());
        document.getElementById('extractTasksBtn').addEventListener('click', () => this.extractTasks());
        document.getElementById('cancelAiTaskBtn').addEventListener('click', () => this.hideAiTask());
        
        // Filters
        document.getElementById('applyFiltersBtn').addEventListener('click', () => this.applyFilters());
        document.getElementById('sortBy').addEventListener('change', (e) => {
            this.currentSort = e.target.value;
            this.loadTasks();
        });
    }

    async loadTasks() {
        const container = document.getElementById('tasksContainer');
        container.innerHTML = '<div class="loading-placeholder">Loading tasks...</div>';

        try {
            const response = await fetch('/api/tasks');
            const data = await response.json();

            if (data.status === 'success') {
                this.renderTasks(container, data.tasks);
            } else {
                container.innerHTML = `<div class="error-message">Error: ${data.message}</div>`;
            }
        } catch (error) {
            container.innerHTML = `<div class="error-message">Error loading tasks: ${error.message}</div>`;
        }
    }

    async loadTaskStats() {
        try {
            const response = await fetch('/api/tasks/stats');
            const data = await response.json();

            if (data.status === 'success') {
                const stats = data.stats;
                document.getElementById('totalTasks').textContent = stats.total || 0;
                document.getElementById('inProgressTasks').textContent = stats.in_progress || 0;
                document.getElementById('completedTasks').textContent = stats.completed || 0;
                document.getElementById('highPriorityTasks').textContent = stats.high_priority || 0;
            }
        } catch (error) {
            console.error('Error loading task stats:', error);
        }
    }

    renderTasks(container, tasks) {
        if (!tasks || tasks.length === 0) {
            container.innerHTML = '<div class="no-tasks">No tasks found</div>';
            return;
        }

        const tasksHtml = tasks.map(task => {
            const dueDate = task.due_date ? new Date(task.due_date).toLocaleDateString() : 'No due date';
            const createdDate = new Date(task.created_at).toLocaleDateString();
            
            return `
                <div class="task-item" data-task-id="${task.id}">
                    <div class="task-header">
                        <h3 class="task-title">${task.title}</h3>
                        <div class="task-badges">
                            <span class="task-priority priority-${task.priority}">${task.priority.toUpperCase()}</span>
                            <span class="task-status status-${task.status}">${task.status.replace('_', ' ').toUpperCase()}</span>
                        </div>
                    </div>
                    
                    ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
                    
                    <div class="task-meta">
                        <span>Created: ${createdDate}</span>
                        <span>Due: ${dueDate}</span>
                        <span>Source: ${task.source || 'manual'}</span>
                        ${task.confidence_score ? `<span>Confidence: ${Math.round(task.confidence_score * 100)}%</span>` : ''}
                    </div>
                    
                    <div class="task-actions">
                        <button class="btn btn-sm btn-primary" onclick="taskManager.editTask('${task.id}')">Edit</button>
                        <button class="btn btn-sm btn-success" onclick="taskManager.completeTask('${task.id}')">Complete</button>
                        <button class="btn btn-sm btn-secondary" onclick="taskManager.deleteTask('${task.id}')">Delete</button>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = tasksHtml;
    }

    showCreateTask() {
        document.getElementById('createTaskSection').style.display = 'block';
        document.getElementById('aiTaskSection').style.display = 'none';
        document.getElementById('taskTitle').focus();
    }

    hideCreateTask() {
        document.getElementById('createTaskSection').style.display = 'none';
        this.clearTaskForm();
    }

    showAiTask() {
        document.getElementById('aiTaskSection').style.display = 'block';
        document.getElementById('createTaskSection').style.display = 'none';
        document.getElementById('aiTaskText').focus();
    }

    hideAiTask() {
        document.getElementById('aiTaskSection').style.display = 'none';
        document.getElementById('aiTaskText').value = '';
        document.getElementById('extractedTasks').style.display = 'none';
    }

    async saveTask() {
        const taskData = {
            title: document.getElementById('taskTitle').value,
            description: document.getElementById('taskDescription').value,
            priority: document.getElementById('taskPriority').value,
            due_date: document.getElementById('taskDueDate').value,
            tags: document.getElementById('taskTags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
            source: 'manual'
        };

        if (!taskData.title) {
            app.showNotification('Task title is required', 'error');
            return;
        }

        try {
            const response = await fetch('/api/tasks', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(taskData)
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Task created successfully!', 'success');
                this.hideCreateTask();
                await this.loadTasks();
                await this.loadTaskStats();
            } else {
                app.showNotification(`Error: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error creating task: ${error.message}`, 'error');
        }
    }

    async extractTasks() {
        const text = document.getElementById('aiTaskText').value.trim();
        if (!text) {
            app.showNotification('Please enter text to analyze', 'error');
            return;
        }

        try {
            const response = await fetch('/api/tasks/extract', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ text: text })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.showExtractedTasks(data.tasks);
            } else {
                app.showNotification(`Error: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error extracting tasks: ${error.message}`, 'error');
        }
    }

    showExtractedTasks(tasks) {
        const container = document.getElementById('tasksPreview');
        const section = document.getElementById('extractedTasks');

        if (!tasks || tasks.length === 0) {
            app.showNotification('No tasks found in the text', 'info');
            return;
        }

        const tasksHtml = tasks.map((task, index) => `
            <div class="task-preview" data-index="${index}">
                <strong>${task.title}</strong>
                ${task.description ? `<div>${task.description}</div>` : ''}
                <small>Priority: ${task.priority} | Confidence: ${Math.round(task.confidence_score * 100)}%</small>
            </div>
        `).join('');

        container.innerHTML = tasksHtml;
        section.style.display = 'block';

        // Store extracted tasks for saving
        this.extractedTasksData = tasks;
    }

    async completeTask(taskId) {
        try {
            const response = await fetch(`/api/tasks/${taskId}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ status: 'completed' })
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Task completed!', 'success');
                await this.loadTasks();
                await this.loadTaskStats();
            } else {
                app.showNotification(`Error: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error completing task: ${error.message}`, 'error');
        }
    }

    async deleteTask(taskId) {
        if (!confirm('Are you sure you want to delete this task?')) return;

        try {
            const response = await fetch(`/api/tasks/${taskId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Task deleted!', 'success');
                await this.loadTasks();
                await this.loadTaskStats();
            } else {
                app.showNotification(`Error: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error deleting task: ${error.message}`, 'error');
        }
    }

    clearTaskForm() {
        document.getElementById('taskTitle').value = '';
        document.getElementById('taskDescription').value = '';
        document.getElementById('taskPriority').value = 'medium';
        document.getElementById('taskDueDate').value = '';
        document.getElementById('taskTags').value = '';
    }

    applyFilters() {
        this.currentFilter = {
            status: document.getElementById('statusFilter').value,
            priority: document.getElementById('priorityFilter').value,
            source: document.getElementById('sourceFilter').value
        };
        this.loadTasks();
    }

    filterEmailTasks() {
        document.getElementById('sourceFilter').value = 'email';
        this.applyFilters();
    }
}

// Initialize task manager
let taskManager;
document.addEventListener('DOMContentLoaded', () => {
    taskManager = new TaskManager();
});
</script>
{% endblock %}
