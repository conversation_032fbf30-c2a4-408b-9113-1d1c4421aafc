"""Change tracking system for learning from user actions."""

from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum

from ..core.logging import logger
from ..core.config import settings


class ChangeType(Enum):
    """Types of changes that can be tracked."""
    TASK_PRIORITY_CHANGE = "task_priority_change"
    TASK_METADATA_EDIT = "task_metadata_edit"
    TASK_DELETION = "task_deletion"
    TASK_COMPLETION = "task_completion"
    EMAIL_TAG_CHANGE = "email_tag_change"
    EMAIL_IMPORTANCE_CHANGE = "email_importance_change"
    CONTACT_MERGE = "contact_merge"
    CONTACT_CONTEXT_CHANGE = "contact_context_change"
    DOCUMENT_CATEGORIZATION = "document_categorization"
    MANUAL_RATING = "manual_rating"


@dataclass
class ChangeEvent:
    """Represents a user action that can be learned from."""
    id: str
    timestamp: datetime
    change_type: ChangeType
    entity_type: str  # 'task', 'email', 'contact', 'document'
    entity_id: str
    user_id: Optional[str]
    context: str  # 'work', 'personal', or other context
    
    # Change details
    field_changed: str
    original_value: Any
    new_value: Any
    
    # Learning metadata
    confidence_before: Optional[float]  # AI's confidence in original prediction
    user_effort: float  # How much effort user put into the change (1.0 = manual edit, 0.1 = quick click)
    learning_weight: float  # How much this change should influence learning
    
    # Additional context
    metadata: Dict[str, Any]


class ChangeTracker:
    """
    Tracks user changes and modifications for machine learning.
    
    This system observes user actions and creates training data
    to improve AI predictions and recommendations.
    """
    
    def __init__(self):
        self.config = settings.learning.change_tracking
        self.enabled = self.config.enabled if hasattr(self.config, 'enabled') else True
        
    async def track_change(
        self,
        change_type: ChangeType,
        entity_type: str,
        entity_id: str,
        field_changed: str,
        original_value: Any,
        new_value: Any,
        context: str = "unknown",
        confidence_before: Optional[float] = None,
        user_effort: float = 1.0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Track a user change for learning purposes.
        
        Args:
            change_type: Type of change being tracked
            entity_type: Type of entity being changed
            entity_id: ID of the entity
            field_changed: Which field was changed
            original_value: Original AI prediction/value
            new_value: User's corrected value
            context: Context of the change (work/personal)
            confidence_before: AI's confidence in original prediction
            user_effort: Effort level (1.0 = manual, 0.1 = quick action)
            metadata: Additional context information
            
        Returns:
            Change event ID
        """
        if not self.enabled:
            return ""
        
        change_id = f"{entity_type}_{entity_id}_{int(datetime.now().timestamp())}"
        
        # Calculate learning weight based on various factors
        learning_weight = self._calculate_learning_weight(
            change_type, user_effort, confidence_before
        )
        
        change_event = ChangeEvent(
            id=change_id,
            timestamp=datetime.now(),
            change_type=change_type,
            entity_type=entity_type,
            entity_id=entity_id,
            user_id=None,  # Could be added for multi-user systems
            context=context,
            field_changed=field_changed,
            original_value=original_value,
            new_value=new_value,
            confidence_before=confidence_before,
            user_effort=user_effort,
            learning_weight=learning_weight,
            metadata=metadata or {}
        )
        
        # Store the change event
        await self._store_change_event(change_event)
        
        # Log for debugging
        logger.info(f"Tracked change: {change_type.value} for {entity_type} {entity_id}")
        
        return change_id
    
    async def track_task_priority_change(
        self,
        task_id: str,
        original_priority: int,
        new_priority: int,
        context: str,
        ai_confidence: Optional[float] = None
    ) -> str:
        """Track when user changes task priority."""
        return await self.track_change(
            change_type=ChangeType.TASK_PRIORITY_CHANGE,
            entity_type="task",
            entity_id=task_id,
            field_changed="priority",
            original_value=original_priority,
            new_value=new_priority,
            context=context,
            confidence_before=ai_confidence,
            user_effort=0.3,  # Priority changes are usually quick
            metadata={
                "priority_difference": abs(new_priority - original_priority),
                "direction": "increase" if new_priority > original_priority else "decrease"
            }
        )
    
    async def track_email_importance_change(
        self,
        email_id: str,
        original_importance: float,
        new_importance: float,
        context: str,
        method: str = "manual"
    ) -> str:
        """Track when user changes email importance scoring."""
        user_effort = 1.0 if method == "manual" else 0.2
        
        return await self.track_change(
            change_type=ChangeType.EMAIL_IMPORTANCE_CHANGE,
            entity_type="email",
            entity_id=email_id,
            field_changed="importance_score",
            original_value=original_importance,
            new_value=new_importance,
            context=context,
            user_effort=user_effort,
            metadata={
                "change_method": method,
                "score_difference": abs(new_importance - original_importance)
            }
        )
    
    async def track_metadata_edit(
        self,
        entity_type: str,
        entity_id: str,
        field_name: str,
        original_value: Any,
        new_value: Any,
        context: str
    ) -> str:
        """Track when user edits metadata fields."""
        return await self.track_change(
            change_type=ChangeType.TASK_METADATA_EDIT,
            entity_type=entity_type,
            entity_id=entity_id,
            field_changed=field_name,
            original_value=original_value,
            new_value=new_value,
            context=context,
            user_effort=0.8,  # Metadata edits require some thought
            metadata={
                "field_type": type(new_value).__name__,
                "value_length": len(str(new_value)) if new_value else 0
            }
        )
    
    async def track_task_deletion(
        self,
        task_id: str,
        task_data: Dict[str, Any],
        context: str,
        reason: str = "user_deleted"
    ) -> str:
        """Track when user deletes a task (strong negative feedback)."""
        return await self.track_change(
            change_type=ChangeType.TASK_DELETION,
            entity_type="task",
            entity_id=task_id,
            field_changed="status",
            original_value="active",
            new_value="deleted",
            context=context,
            user_effort=0.5,  # Deletion is usually a quick action
            learning_weight=2.0,  # Deletions are strong negative signals
            metadata={
                "deletion_reason": reason,
                "task_age_hours": self._calculate_task_age(task_data),
                "original_priority": task_data.get("priority"),
                "original_importance": task_data.get("importance_score")
            }
        )
    
    async def get_learning_data(
        self,
        entity_type: Optional[str] = None,
        change_type: Optional[ChangeType] = None,
        context: Optional[str] = None,
        since: Optional[datetime] = None,
        limit: int = 1000
    ) -> List[ChangeEvent]:
        """
        Retrieve change events for model training.
        
        Args:
            entity_type: Filter by entity type
            change_type: Filter by change type
            context: Filter by context (work/personal)
            since: Only get changes since this date
            limit: Maximum number of events to return
            
        Returns:
            List of change events
        """
        # TODO: Implement database query
        # This would query the learning_feedback table
        logger.info(f"Retrieving learning data with filters: {entity_type}, {change_type}, {context}")
        return []
    
    def _calculate_learning_weight(
        self,
        change_type: ChangeType,
        user_effort: float,
        confidence_before: Optional[float]
    ) -> float:
        """Calculate how much weight this change should have in learning."""
        base_weight = 1.0
        
        # Adjust based on change type
        if change_type == ChangeType.TASK_DELETION:
            base_weight = 2.0  # Deletions are strong negative signals
        elif change_type == ChangeType.MANUAL_RATING:
            base_weight = 1.5  # Explicit ratings are valuable
        elif change_type == ChangeType.TASK_COMPLETION:
            base_weight = 0.5  # Completions are positive but less informative
        
        # Adjust based on user effort
        effort_multiplier = min(user_effort * 1.5, 2.0)
        
        # Adjust based on AI confidence
        confidence_multiplier = 1.0
        if confidence_before is not None:
            # If AI was very confident but wrong, weight more heavily
            if confidence_before > 0.8:
                confidence_multiplier = 1.5
            elif confidence_before < 0.3:
                confidence_multiplier = 0.7  # AI wasn't confident anyway
        
        final_weight = base_weight * effort_multiplier * confidence_multiplier
        return min(final_weight, 5.0)  # Cap at 5.0
    
    def _calculate_task_age(self, task_data: Dict[str, Any]) -> float:
        """Calculate how old a task was when deleted/modified."""
        created_at = task_data.get('created_at')
        if not created_at:
            return 0.0
        
        if isinstance(created_at, str):
            created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        
        age = datetime.now() - created_at
        return age.total_seconds() / 3600  # Return age in hours
    
    async def _store_change_event(self, change_event: ChangeEvent) -> None:
        """Store change event in database."""
        # TODO: Implement database storage
        # This would insert into the learning_feedback table
        logger.debug(f"Storing change event: {change_event.id}")
        pass


# Global change tracker instance
change_tracker = ChangeTracker()
