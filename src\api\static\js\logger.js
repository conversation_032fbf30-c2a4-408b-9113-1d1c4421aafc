/**
 * Frontend Logging System
 * Captures errors, user actions, and system events for debugging
 */

class FrontendLogger {
    constructor() {
        this.logBuffer = [];
        this.maxBufferSize = 100;
        this.sendInterval = 5000; // Send logs every 5 seconds
        this.isEnabled = true;
        
        // Initialize error handlers
        this.setupErrorHandlers();
        
        // Start periodic log sending
        this.startLogSending();
        
        this.log('INFO', 'Frontend logger initialized');
    }

    /**
     * Setup global error handlers
     */
    setupErrorHandlers() {
        // Catch JavaScript errors
        window.addEventListener('error', (event) => {
            this.log('ERROR', 'JavaScript Error', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error ? event.error.stack : null
            });
        });

        // Catch unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.log('ERROR', 'Unhandled Promise Rejection', {
                reason: event.reason,
                stack: event.reason ? event.reason.stack : null
            });
        });

        // Catch fetch errors
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            try {
                const response = await originalFetch(...args);
                
                // Log failed requests
                if (!response.ok) {
                    this.log('WARNING', 'HTTP Request Failed', {
                        url: args[0],
                        status: response.status,
                        statusText: response.statusText
                    });
                }
                
                return response;
            } catch (error) {
                this.log('ERROR', 'Fetch Error', {
                    url: args[0],
                    error: error.message,
                    stack: error.stack
                });
                throw error;
            }
        };
    }

    /**
     * Log a message
     */
    log(level, message, context = {}) {
        if (!this.isEnabled) return;

        const logEntry = {
            timestamp: new Date().toISOString(),
            level: level.toUpperCase(),
            message: message,
            context: context,
            url: window.location.href,
            userAgent: navigator.userAgent,
            sessionId: this.getSessionId()
        };

        // Add to buffer
        this.logBuffer.push(logEntry);

        // Console output for development
        const consoleMethod = level.toLowerCase() === 'error' ? 'error' :
                            level.toLowerCase() === 'warning' ? 'warn' :
                            level.toLowerCase() === 'debug' ? 'debug' : 'log';
        
        console[consoleMethod](`[${level}] ${message}`, context);

        // Trim buffer if too large
        if (this.logBuffer.length > this.maxBufferSize) {
            this.logBuffer = this.logBuffer.slice(-this.maxBufferSize);
        }

        // Send immediately for errors
        if (level.toUpperCase() === 'ERROR') {
            this.sendLogs();
        }
    }

    /**
     * Log user actions
     */
    logUserAction(action, details = {}) {
        this.log('INFO', `User Action: ${action}`, {
            action: action,
            details: details,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Log API calls
     */
    logApiCall(method, url, status, duration, error = null) {
        const level = error ? 'ERROR' : status >= 400 ? 'WARNING' : 'INFO';
        this.log(level, `API Call: ${method} ${url}`, {
            method: method,
            url: url,
            status: status,
            duration: duration,
            error: error
        });
    }

    /**
     * Log page navigation
     */
    logPageView(page) {
        this.log('INFO', `Page View: ${page}`, {
            page: page,
            referrer: document.referrer,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * Get or create session ID
     */
    getSessionId() {
        let sessionId = sessionStorage.getItem('frontend_session_id');
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            sessionStorage.setItem('frontend_session_id', sessionId);
        }
        return sessionId;
    }

    /**
     * Send logs to backend
     */
    async sendLogs() {
        if (this.logBuffer.length === 0) return;

        const logsToSend = [...this.logBuffer];
        this.logBuffer = [];

        try {
            const response = await fetch('/api/logs/frontend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    logs: logsToSend,
                    sessionId: this.getSessionId()
                })
            });

            if (!response.ok) {
                // Put logs back in buffer if send failed
                this.logBuffer.unshift(...logsToSend);
                console.warn('Failed to send logs to backend:', response.statusText);
            }
        } catch (error) {
            // Put logs back in buffer if send failed
            this.logBuffer.unshift(...logsToSend);
            console.warn('Error sending logs to backend:', error);
        }
    }

    /**
     * Start periodic log sending
     */
    startLogSending() {
        setInterval(() => {
            this.sendLogs();
        }, this.sendInterval);
    }

    /**
     * Enable/disable logging
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        this.log('INFO', `Frontend logging ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Get current log buffer
     */
    getLogBuffer() {
        return [...this.logBuffer];
    }

    /**
     * Clear log buffer
     */
    clearBuffer() {
        this.logBuffer = [];
        this.log('INFO', 'Log buffer cleared');
    }

    /**
     * Export logs as JSON
     */
    exportLogs() {
        const logs = this.getLogBuffer();
        const dataStr = JSON.stringify(logs, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `frontend_logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        link.click();
        
        this.log('INFO', 'Frontend logs exported');
    }
}

// Create global logger instance
window.frontendLogger = new FrontendLogger();

// Convenience methods
window.logInfo = (message, context) => window.frontendLogger.log('INFO', message, context);
window.logWarning = (message, context) => window.frontendLogger.log('WARNING', message, context);
window.logError = (message, context) => window.frontendLogger.log('ERROR', message, context);
window.logDebug = (message, context) => window.frontendLogger.log('DEBUG', message, context);
window.logUserAction = (action, details) => window.frontendLogger.logUserAction(action, details);

// Log initial page load
document.addEventListener('DOMContentLoaded', () => {
    window.frontendLogger.logPageView(window.location.pathname);
});

// Log page unload
window.addEventListener('beforeunload', () => {
    window.frontendLogger.sendLogs();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FrontendLogger;
}
