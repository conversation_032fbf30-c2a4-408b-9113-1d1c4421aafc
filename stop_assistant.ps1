# Personal Assistant - PowerShell Stop Script
# This script provides advanced stop functionality for the Personal Assistant

param(
    [switch]$Force,         # Force stop all Python processes
    [switch]$Verbose,       # Enable verbose output
    [string]$Port = "8000"  # Custom port to target
)

# Set console title
$Host.UI.RawUI.WindowTitle = "Personal Assistant - Stopping..."

# Color functions
function Write-Header($text) {
    Write-Host "`n========================================" -ForegroundColor Cyan
    Write-Host "   $text" -ForegroundColor Yellow
    Write-Host "========================================`n" -ForegroundColor Cyan
}

function Write-Success($text) {
    Write-Host "✅ $text" -ForegroundColor Green
}

function Write-Warning($text) {
    Write-Host "⚠️  $text" -ForegroundColor Yellow
}

function Write-Error($text) {
    Write-Host "❌ $text" -ForegroundColor Red
}

function Write-Info($text) {
    Write-Host "ℹ️  $text" -ForegroundColor Blue
}

function Stop-PersonalAssistant {
    Write-Header "Personal Assistant - Stopping..."
    
    $processesKilled = 0
    
    # Method 1: Stop processes using the specified port
    Write-Info "Checking for processes using port $Port..."
    try {
        $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        
        if ($connections) {
            foreach ($conn in $connections) {
                $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
                if ($process) {
                    Write-Info "Found process using port $Port`: $($process.ProcessName) (PID: $($process.Id))"
                    
                    # Check if it's likely our application
                    if ($process.ProcessName -eq "python" -or $process.ProcessName -eq "pythonw") {
                        Write-Info "Stopping Python process: PID $($process.Id)"
                        Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
                        $processesKilled++
                        Write-Success "Stopped process PID $($process.Id)"
                    }
                }
            }
        } else {
            Write-Info "No processes found using port $Port"
        }
    }
    catch {
        Write-Warning "Could not check port usage: $($_.Exception.Message)"
    }
    
    # Method 2: Find Python processes with web_app in command line
    Write-Info "Searching for Personal Assistant processes..."
    try {
        $pythonProcesses = Get-WmiObject Win32_Process | Where-Object { 
            $_.Name -eq "python.exe" -or $_.Name -eq "pythonw.exe" 
        }
        
        foreach ($proc in $pythonProcesses) {
            $commandLine = $proc.CommandLine
            if ($commandLine -and ($commandLine -like "*web_app*" -or $commandLine -like "*assistant*")) {
                Write-Info "Found Personal Assistant process: PID $($proc.ProcessId)"
                Write-Info "Command: $commandLine"
                
                try {
                    Stop-Process -Id $proc.ProcessId -Force -ErrorAction Stop
                    $processesKilled++
                    Write-Success "Stopped Personal Assistant process: PID $($proc.ProcessId)"
                }
                catch {
                    Write-Warning "Could not stop process PID $($proc.ProcessId): $($_.Exception.Message)"
                }
            }
        }
    }
    catch {
        Write-Warning "Could not search for Python processes: $($_.Exception.Message)"
    }
    
    # Method 3: Force stop all Python processes (if requested)
    if ($Force) {
        Write-Warning "Force mode enabled - stopping ALL Python processes..."
        try {
            $allPythonProcesses = Get-Process python*, pythonw* -ErrorAction SilentlyContinue
            foreach ($proc in $allPythonProcesses) {
                Write-Info "Force stopping Python process: $($proc.ProcessName) (PID: $($proc.Id))"
                Stop-Process -Id $proc.Id -Force -ErrorAction SilentlyContinue
                $processesKilled++
            }
            if ($allPythonProcesses) {
                Write-Success "Force stopped all Python processes"
            }
        }
        catch {
            Write-Warning "Could not force stop Python processes: $($_.Exception.Message)"
        }
    }
    
    # Method 4: Check for any remaining processes on the port
    Write-Info "Final check for port $Port usage..."
    try {
        $remainingConnections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        if ($remainingConnections) {
            Write-Warning "Port $Port is still in use by:"
            foreach ($conn in $remainingConnections) {
                $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
                if ($process) {
                    Write-Host "  - $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Success "Port $Port is now free"
        }
    }
    catch {
        Write-Info "Could not verify port status"
    }
    
    # Summary
    Write-Header "Stop Operation Complete"
    if ($processesKilled -gt 0) {
        Write-Success "Successfully stopped $processesKilled process(es)"
    } else {
        Write-Info "No Personal Assistant processes were found running"
    }
    
    # Wait a moment for processes to fully terminate
    Start-Sleep -Seconds 2
    
    Write-Success "Personal Assistant has been stopped"
}

function Show-RunningProcesses {
    Write-Info "Current Python processes:"
    try {
        $pythonProcesses = Get-Process python*, pythonw* -ErrorAction SilentlyContinue
        if ($pythonProcesses) {
            foreach ($proc in $pythonProcesses) {
                Write-Host "  - $($proc.ProcessName) (PID: $($proc.Id)) - CPU: $($proc.CPU)" -ForegroundColor Cyan
            }
        } else {
            Write-Info "No Python processes currently running"
        }
    }
    catch {
        Write-Warning "Could not list Python processes"
    }
}

function Show-PortUsage {
    param([string]$Port)
    
    Write-Info "Checking what's using port $Port..."
    try {
        $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
        if ($connections) {
            foreach ($conn in $connections) {
                $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
                if ($process) {
                    Write-Host "  - $($process.ProcessName) (PID: $($process.Id)) - State: $($conn.State)" -ForegroundColor Cyan
                }
            }
        } else {
            Write-Success "Port $Port is free"
        }
    }
    catch {
        Write-Info "Could not check port usage"
    }
}

# Main execution
try {
    if ($Verbose) {
        Write-Info "Verbose mode enabled"
        Show-RunningProcesses
        Show-PortUsage -Port $Port
    }
    
    Stop-PersonalAssistant
    
    if ($Verbose) {
        Write-Info "Post-stop status:"
        Show-RunningProcesses
        Show-PortUsage -Port $Port
    }
    
    $Host.UI.RawUI.WindowTitle = "Personal Assistant - Stopped"
}
catch {
    Write-Error "An unexpected error occurred: $($_.Exception.Message)"
    Write-Host $_.ScriptStackTrace -ForegroundColor Red
}
finally {
    if (-not $Verbose) {
        Start-Sleep -Seconds 2
    }
    Read-Host "Press Enter to exit"
}
