"""Email Processing Agent for the Assistant."""

from datetime import datetime
from typing import List, Optional, Dict, Any

from ..core.logging import logger
from ..core.config import settings
from ..knowledge.models import EmailTask, EmailContact
from .email_intelligence_service import email_intelligence_service
from .email_response_generator import email_response_generator


class EmailProcessorAgent:
    """
    Agent responsible for processing emails and extracting actionable information.
    
    Key responsibilities:
    - Parse incoming emails for task-worthy content
    - Extract contact information from email interactions
    - Score email importance based on content and context
    - Learn from user feedback to improve task creation and prioritization
    """
    
    def __init__(self):
        self.config = settings.agents.email_processor
        self.learning_enabled = self.config.learning_enabled if hasattr(self.config, 'learning_enabled') else True
        self.task_keywords = settings.integrations.email.processing.task_keywords
        
    async def process_email(self, email_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single email and extract actionable information.

        Args:
            email_data: Dictionary containing email information

        Returns:
            Dictionary with extracted tasks, contacts, tags, and metadata
        """
        logger.info(f"Processing email: {email_data.get('subject', 'No Subject')}")

        result = {
            'tasks': [],
            'contacts': [],
            'tags': [],
            'importance_score': 0.0,
            'urgency_score': 0.0,
            'category': 'unknown',
            'should_unsubscribe': False,
            'related_task_id': None,
            'metadata': {}
        }

        try:
            # Determine email context (work/personal)
            context = self._determine_context(email_data)
            result['context'] = context

            # Check if this is marketing/newsletter email
            is_marketing = await self._is_marketing_email(email_data)
            result['is_marketing'] = is_marketing

            if is_marketing:
                result['should_unsubscribe'] = True
                result['category'] = 'marketing'
            else:
                # Categorize email
                category = await self._categorize_email(email_data, context)
                result['category'] = category

                # Generate tags for importance and urgency
                tags = await self._generate_tags(email_data)
                result['tags'] = tags

                # Calculate importance and urgency scores
                importance_score = await self._calculate_importance(email_data)
                urgency_score = await self._calculate_urgency(email_data)
                result['importance_score'] = importance_score
                result['urgency_score'] = urgency_score

                # Check for related existing tasks
                related_task = await self._find_related_task(email_data)
                result['related_task_id'] = related_task

                # Intelligent email analysis
                email_analysis = await email_intelligence_service.analyze_email(email_data)
                result['intelligence'] = {
                    'priority': email_analysis.priority.value,
                    'urgency_score': email_analysis.urgency_score,
                    'importance_score': email_analysis.importance_score,
                    'sentiment': email_analysis.sentiment,
                    'recommended_action': email_analysis.recommended_action.value,
                    'response_deadline': email_analysis.response_deadline.isoformat() if email_analysis.response_deadline else None,
                    'key_points': email_analysis.key_points,
                    'questions_asked': email_analysis.questions_asked,
                    'action_items': email_analysis.action_items,
                    'meeting_request': email_analysis.meeting_request,
                    'requires_decision': email_analysis.requires_decision,
                    'confidence': email_analysis.confidence,
                    'reasoning': email_analysis.reasoning
                }

                # Extract tasks from email content (only if no related task found)
                if self.config.task_creation and not related_task:
                    tasks = await self._extract_tasks(email_data, context)
                    result['tasks'] = tasks

                    # Save tasks to database
                    saved_tasks = []
                    for task in tasks:
                        try:
                            from ..knowledge.task_service import task_service
                            saved_task = await task_service.create_task_from_email(task)
                            saved_tasks.append({
                                'id': str(saved_task.id),
                                'title': saved_task.title,
                                'status': saved_task.status,
                                'priority': saved_task.priority
                            })
                        except Exception as e:
                            logger.error(f"Error saving task to database: {e}")

                    result['saved_tasks'] = saved_tasks

                # Extract contact information
                if hasattr(self.config, 'contact_extraction') and self.config.contact_extraction:
                    contacts = await self._extract_contacts(email_data, context)
                    result['contacts'] = contacts

            # Add processing metadata
            result['metadata'] = {
                'processed_at': datetime.now().isoformat(),
                'agent_version': '1.0',
                'processing_time_ms': 0,  # TODO: Add timing
                'confidence_scores': {
                    'task_extraction': 0.8,
                    'importance_scoring': 0.7,
                    'urgency_scoring': 0.8,
                    'contact_extraction': 0.9,
                    'categorization': 0.85
                }
            }

        except Exception as e:
            logger.error(f"Error processing email {email_data.get('message_id', 'unknown')}: {e}")
            result['metadata']['error'] = str(e)

        return result
    
    async def _extract_tasks(self, email_data: Dict[str, Any]) -> List[EmailTask]:
        """Extract potential tasks from email content."""
        tasks = []
        
        # Get email content
        content = email_data.get('body_text', '') or email_data.get('body_html', '')
        subject = email_data.get('subject', '')
        
        # Simple keyword-based task detection (will be enhanced with AI)
        found_keywords = []
        for keyword in self.task_keywords:
            if keyword.lower() in content.lower() or keyword.lower() in subject.lower():
                found_keywords.append(keyword)
        
        if found_keywords:
            # Create a task based on the email
            task = EmailTask(
                title=f"Action required: {subject[:100]}",
                description=self._extract_task_description(content, found_keywords),
                priority=self._estimate_priority(email_data, found_keywords),
                importance_score=await self._calculate_task_importance(email_data, found_keywords),
                due_date=self._extract_due_date(content),
                source_email_id=email_data.get('message_id', ''),
                keywords_found=found_keywords,
                metadata={
                    'sender': email_data.get('sender_email', ''),
                    'received_date': email_data.get('received_date', ''),
                    'extraction_method': 'keyword_based'
                }
            )
            tasks.append(task)
        
        return tasks
    
    async def _extract_contacts(self, email_data: Dict[str, Any]) -> List[EmailContact]:
        """Extract contact information from email."""
        contacts = []
        
        # Extract sender information
        sender_email = email_data.get('sender_email')
        sender_name = email_data.get('sender_name')
        
        if sender_email:
            contact = EmailContact(
                email=sender_email,
                name=sender_name,
                first_interaction=email_data.get('received_date', datetime.now()),
                interaction_count=1,  # Will be updated when storing
                context=f"Email sender - Subject: {email_data.get('subject', '')[:50]}"
            )
            contacts.append(contact)
        
        # Extract recipients (for sent emails)
        recipients = email_data.get('recipients', [])
        for recipient in recipients:
            if isinstance(recipient, str):
                contact = EmailContact(
                    email=recipient,
                    name=None,
                    first_interaction=email_data.get('sent_date', datetime.now()),
                    interaction_count=1,
                    context=f"Email recipient - Subject: {email_data.get('subject', '')[:50]}"
                )
                contacts.append(contact)
        
        return contacts
    
    async def _calculate_importance(self, email_data: Dict[str, Any]) -> float:
        """Calculate the importance score of an email."""
        score = 0.5  # Base score
        
        # Factors that increase importance
        subject = email_data.get('subject', '').lower()
        content = email_data.get('body_text', '').lower()
        
        # Urgency indicators
        urgency_words = ['urgent', 'asap', 'immediate', 'deadline', 'due']
        for word in urgency_words:
            if word in subject or word in content:
                score += 0.2
        
        # Sender importance (TODO: implement sender scoring)
        sender = email_data.get('sender_email', '')
        if 'boss' in sender or 'manager' in sender:  # Placeholder logic
            score += 0.3
        
        # Length and complexity
        if len(content) > 1000:  # Longer emails might be more important
            score += 0.1
        
        # Ensure score is between 0 and 1
        return min(max(score, 0.0), 1.0)
    
    async def _calculate_task_importance(self, email_data: Dict[str, Any], keywords: List[str]) -> float:
        """Calculate importance score specifically for task creation."""
        base_score = await self._calculate_importance(email_data)
        
        # Adjust based on task-specific factors
        if 'deadline' in keywords:
            base_score += 0.2
        if 'urgent' in keywords:
            base_score += 0.3
        
        return min(base_score, 1.0)
    
    def _extract_task_description(self, content: str, keywords: List[str]) -> str:
        """Extract a meaningful task description from email content."""
        # Simple implementation - will be enhanced with AI
        lines = content.split('\n')
        
        # Find lines containing keywords
        relevant_lines = []
        for line in lines:
            for keyword in keywords:
                if keyword.lower() in line.lower():
                    relevant_lines.append(line.strip())
                    break
        
        if relevant_lines:
            return ' '.join(relevant_lines[:3])  # Take first 3 relevant lines
        
        # Fallback to first few lines
        return ' '.join(lines[:2])[:200] + '...' if len(' '.join(lines[:2])) > 200 else ' '.join(lines[:2])
    

    

    
    def _determine_context(self, email_data: Dict[str, Any]) -> str:
        """Determine if email is work or personal context."""
        sender_email = email_data.get('sender_email', '').lower()
        recipient_emails = email_data.get('recipients', [])

        # Check sender domain against work indicators
        work_domains = settings.integrations.contacts.management.work_email_domains
        personal_indicators = settings.integrations.contacts.management.personal_indicators

        for domain in work_domains:
            if domain in sender_email:
                return 'work'

        for indicator in personal_indicators:
            if indicator in sender_email:
                return 'personal'

        # Default logic - could be enhanced with ML
        if any(domain in sender_email for domain in ['company.com', 'corp.com', 'business.com']):
            return 'work'

        return 'personal'  # Default to personal

    async def _is_marketing_email(self, email_data: Dict[str, Any]) -> bool:
        """Determine if email is marketing/newsletter."""
        content = (email_data.get('body_text', '') + ' ' +
                  email_data.get('subject', '')).lower()

        marketing_keywords = settings.integrations.email.processing.marketing_keywords

        # Count marketing indicators
        marketing_score = 0
        for keyword in marketing_keywords:
            if keyword in content:
                marketing_score += 1

        # Check for typical marketing patterns
        if 'unsubscribe' in content and ('newsletter' in content or 'promotion' in content):
            marketing_score += 2

        # Simple threshold - could be enhanced with ML
        threshold = settings.integrations.email.processing.marketing_confidence_threshold
        return (marketing_score / len(marketing_keywords)) > threshold

    async def _categorize_email(self, email_data: Dict[str, Any], context: str) -> str:
        """Categorize email into types."""
        subject = email_data.get('subject', '').lower()
        content = email_data.get('body_text', '').lower()

        # Simple rule-based categorization
        if any(word in subject or word in content for word in ['meeting', 'calendar', 'schedule']):
            return 'meeting'
        elif any(word in subject or word in content for word in ['project', 'task', 'deadline']):
            return 'project'
        elif any(word in subject or word in content for word in ['invoice', 'payment', 'bill']):
            return 'financial'
        elif any(word in subject or word in content for word in ['social', 'facebook', 'twitter', 'linkedin']):
            return 'social'
        else:
            return context  # Default to work or personal

    async def _generate_tags(self, email_data: Dict[str, Any]) -> List[str]:
        """Generate importance and urgency tags."""
        tags = []

        subject = email_data.get('subject', '').lower()
        content = email_data.get('body_text', '').lower()

        # Urgency tags
        if any(word in subject or word in content for word in ['urgent', 'asap', 'immediate']):
            tags.append('urgent')
        elif any(word in subject or word in content for word in ['deadline', 'due', 'expires']):
            tags.append('normal')
        else:
            tags.append('low')

        # Importance tags (will be enhanced with ML)
        if any(word in subject for word in ['important', 'critical', 'urgent']):
            tags.append('high')
        elif any(word in subject or word in content for word in ['fyi', 'info', 'newsletter']):
            tags.append('low')
        else:
            tags.append('medium')

        return tags

    async def _calculate_urgency(self, email_data: Dict[str, Any]) -> float:
        """Calculate urgency score separate from importance."""
        score = 0.5  # Base score

        subject = email_data.get('subject', '').lower()
        content = email_data.get('body_text', '').lower()

        # Time-sensitive indicators
        urgency_words = ['urgent', 'asap', 'immediate', 'today', 'now']
        for word in urgency_words:
            if word in subject:
                score += 0.3
            elif word in content:
                score += 0.2

        # Deadline indicators
        deadline_words = ['deadline', 'due', 'expires', 'ends']
        for word in deadline_words:
            if word in subject or word in content:
                score += 0.2

        return min(max(score, 0.0), 1.0)

    async def _find_related_task(self, email_data: Dict[str, Any]) -> Optional[str]:
        """Find existing task that this email might be related to."""
        # TODO: Implement similarity search against existing tasks
        # This would use vector similarity or keyword matching to find related tasks
        # For now, return None (no related task found)

        subject = email_data.get('subject', '')

        # Simple implementation - look for "Re:" or "Fwd:" patterns
        if subject.startswith(('Re:', 'RE:', 'Fwd:', 'FWD:')):
            # This is likely a reply to an existing conversation
            # TODO: Search for tasks with similar subjects
            pass

        return None

    async def _extract_tasks(self, email_data: Dict[str, Any], context: str) -> List[EmailTask]:
        """Extract potential tasks from email content with context awareness."""
        tasks = []

        # Get email content
        content = email_data.get('body_text', '') or email_data.get('body_html', '')
        subject = email_data.get('subject', '')

        # Enhanced keyword detection for task creation
        found_keywords = []
        for keyword in self.task_keywords:
            if keyword.lower() in content.lower() or keyword.lower() in subject.lower():
                found_keywords.append(keyword)

        # Check for question patterns that need responses
        question_patterns = ['?', 'can you', 'could you', 'would you', 'please']
        has_questions = any(pattern in content.lower() for pattern in question_patterns)

        # Check for action items
        action_patterns = ['need to', 'have to', 'must', 'should', 'action required']
        has_actions = any(pattern in content.lower() for pattern in action_patterns)

        if found_keywords or has_questions or has_actions:
            # Create a task based on the email
            task = EmailTask(
                title=f"[{context.title()}] {subject[:100]}",
                description=self._extract_task_description(content, found_keywords),
                priority=self._estimate_priority(email_data, found_keywords, context),
                importance_score=await self._calculate_task_importance(email_data, found_keywords),
                due_date=self._extract_due_date(content),
                source_email_id=email_data.get('message_id', ''),
                keywords_found=found_keywords,
                task_metadata={
                    'sender': email_data.get('sender_email', ''),
                    'received_date': email_data.get('received_date', ''),
                    'context': context,
                    'has_questions': has_questions,
                    'has_actions': has_actions,
                    'extraction_method': 'enhanced_keyword_based'
                }
            )
            tasks.append(task)

        return tasks

    async def _extract_contacts(self, email_data: Dict[str, Any], context: str) -> List[EmailContact]:
        """Extract contact information with context awareness."""
        contacts = []

        # Extract sender information
        sender_email = email_data.get('sender_email')
        sender_name = email_data.get('sender_name')

        if sender_email:
            contact = EmailContact(
                email=sender_email,
                name=sender_name,
                first_interaction=email_data.get('received_date', datetime.now()),
                interaction_count=1,
                context=f"[{context}] Email sender - Subject: {email_data.get('subject', '')[:50]}"
            )
            contacts.append(contact)

        # Extract recipients (for sent emails) with context
        recipients = email_data.get('recipients', [])
        for recipient in recipients:
            if isinstance(recipient, str):
                contact = EmailContact(
                    email=recipient,
                    name=None,
                    first_interaction=email_data.get('sent_date', datetime.now()),
                    interaction_count=1,
                    context=f"[{context}] Email recipient - Subject: {email_data.get('subject', '')[:50]}"
                )
                contacts.append(contact)

        return contacts

    def _estimate_priority(self, email_data: Dict[str, Any], keywords: List[str], context: str) -> int:
        """Estimate task priority with context awareness."""
        priority = 3  # Default medium priority

        # Adjust based on keywords
        if 'urgent' in keywords or 'asap' in keywords:
            priority = 5
        elif 'deadline' in keywords:
            priority = 4
        elif 'follow up' in keywords:
            priority = 2

        # Context-based adjustments
        if context == 'work':
            # Work emails might have higher default priority
            priority = min(priority + 1, 5)

        return priority

    async def learn_from_feedback(self, feedback_data: Dict[str, Any]) -> None:
        """Learn from user feedback to improve future processing."""
        if not self.learning_enabled:
            return

        logger.info(f"Learning from feedback: {feedback_data.get('feedback_type', 'unknown')}")

        # Store feedback for model training
        feedback_entry = {
            'timestamp': datetime.now().isoformat(),
            'feedback_type': feedback_data.get('feedback_type'),
            'entity_type': feedback_data.get('entity_type'),
            'entity_id': feedback_data.get('entity_id'),
            'original_prediction': feedback_data.get('original_prediction'),
            'user_correction': feedback_data.get('user_correction'),
            'context': feedback_data.get('context', 'unknown')
        }

        # TODO: Store in learning database and trigger model retraining
        logger.info(f"Stored feedback for learning: {feedback_entry}")
        pass

    def _extract_task_description(self, content: str, keywords: List[str]) -> str:
        """Extract a meaningful task description from email content."""
        # Clean up the content
        lines = content.split('\n')
        meaningful_lines = []

        for line in lines:
            line = line.strip()
            # Skip empty lines, signatures, and common email artifacts
            if (line and
                not line.startswith('>') and  # Quoted text
                not line.startswith('From:') and
                not line.startswith('Sent:') and
                not line.startswith('To:') and
                not line.startswith('Subject:') and
                len(line) > 10):  # Skip very short lines
                meaningful_lines.append(line)

        # Take first few meaningful lines
        description = ' '.join(meaningful_lines[:3])

        # Truncate if too long
        if len(description) > 300:
            description = description[:297] + "..."

        # Add context about found keywords
        if keywords:
            description += f"\n\nDetected keywords: {', '.join(keywords)}"

        return description

    async def _calculate_task_importance(self, email_data: Dict[str, Any], keywords: List[str]) -> float:
        """Calculate task importance score based on multiple factors."""
        importance = 0.5  # Base importance

        # Sender importance (could be learned from user behavior)
        sender = email_data.get('sender_email', '').lower()

        # VIP senders (this could be learned)
        vip_domains = ['@company.com', '@client.com']  # Example
        if any(domain in sender for domain in vip_domains):
            importance += 0.2

        # Keyword-based importance
        high_importance_keywords = ['urgent', 'asap', 'deadline', 'action required']
        medium_importance_keywords = ['please', 'need', 'help', 'review']

        for keyword in keywords:
            if keyword.lower() in high_importance_keywords:
                importance += 0.3
            elif keyword.lower() in medium_importance_keywords:
                importance += 0.1

        # Subject line indicators
        subject = email_data.get('subject', '').lower()
        if any(word in subject for word in ['urgent', 'important', 'asap']):
            importance += 0.2

        # Time sensitivity
        if any(word in subject.lower() for word in ['today', 'tomorrow', 'deadline']):
            importance += 0.15

        return min(importance, 1.0)

    def _extract_due_date(self, content: str) -> Optional[datetime]:
        """Extract due date from email content."""
        import re
        from datetime import datetime, timedelta

        content_lower = content.lower()

        # Look for explicit dates
        date_patterns = [
            r'due\s+(by|on|before)\s+(\w+\s+\d{1,2})',  # "due by March 15"
            r'deadline[:\s]+(\w+\s+\d{1,2})',           # "deadline: March 15"
            r'by\s+(today|tomorrow|this\s+week|next\s+week)',  # relative dates
            r'due\s+(today|tomorrow|this\s+week|next\s+week)',
        ]

        for pattern in date_patterns:
            match = re.search(pattern, content_lower)
            if match:
                date_text = match.group(1) if len(match.groups()) == 1 else match.group(2)

                # Handle relative dates
                if 'today' in date_text:
                    return datetime.now().replace(hour=17, minute=0, second=0, microsecond=0)
                elif 'tomorrow' in date_text:
                    return (datetime.now() + timedelta(days=1)).replace(hour=17, minute=0, second=0, microsecond=0)
                elif 'this week' in date_text:
                    # End of this week (Friday)
                    days_until_friday = (4 - datetime.now().weekday()) % 7
                    if days_until_friday == 0:  # It's Friday
                        days_until_friday = 7
                    return (datetime.now() + timedelta(days=days_until_friday)).replace(hour=17, minute=0, second=0, microsecond=0)
                elif 'next week' in date_text:
                    # End of next week
                    days_until_next_friday = ((4 - datetime.now().weekday()) % 7) + 7
                    return (datetime.now() + timedelta(days=days_until_next_friday)).replace(hour=17, minute=0, second=0, microsecond=0)

                # TODO: Parse specific dates like "March 15", "15th", etc.
                # For now, return a default due date for matched patterns
                return (datetime.now() + timedelta(days=3)).replace(hour=17, minute=0, second=0, microsecond=0)

        return None


# Create global instance
email_processor_agent = EmailProcessorAgent()
