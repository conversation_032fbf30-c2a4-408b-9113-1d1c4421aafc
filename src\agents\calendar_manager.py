"""Calendar Management Agent for the Assistant."""

from datetime import datetime, timed<PERSON>ta
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.logging import logger
from ..core.config import settings


class ConflictResolution(Enum):
    """Strategies for resolving calendar conflicts."""
    PREFER_WORK = "prefer_work"
    PREFER_PERSONAL = "prefer_personal"
    MANUAL = "manual"


@dataclass
class CalendarEvent:
    """Represents a calendar event."""
    id: str
    calendar_id: str
    calendar_type: str  # 'personal', 'work'
    title: str
    description: Optional[str]
    start_time: datetime
    end_time: datetime
    all_day: bool
    location: Optional[str]
    attendees: List[str]
    status: str  # 'confirmed', 'tentative', 'cancelled'


@dataclass
class AvailabilitySlot:
    """Represents an available time slot."""
    start_time: datetime
    end_time: datetime
    calendar_types: List[str]  # Which calendars are free during this slot
    confidence: float  # How confident we are about this availability


@dataclass
class CalendarConflict:
    """Represents a conflict between calendar events."""
    event1: CalendarEvent
    event2: CalendarEvent
    conflict_type: str  # 'overlap', 'double_booking', 'travel_time'
    severity: float  # 0.0 to 1.0
    suggested_resolution: str


class CalendarManagerAgent:
    """
    Agent responsible for managing calendar synchronization and availability.
    
    Key responsibilities:
    - Sync events between personal and work calendars
    - Detect and resolve scheduling conflicts
    - Provide availability information for meeting scheduling
    - Optimize meeting times based on preferences and constraints
    """
    
    def __init__(self):
        self.config = settings.agents.calendar_manager
        self.calendar_config = settings.integrations.calendar
        self.working_hours = self.calendar_config.sync.working_hours
        self.buffer_minutes = self.calendar_config.sync.availability_buffer_minutes
        
    async def sync_calendars(self) -> Dict[str, Any]:
        """
        Synchronize events between personal and work calendars.
        
        Returns:
            Dictionary with sync results and any conflicts found
        """
        logger.info("Starting calendar synchronization")
        
        result = {
            'synced_events': 0,
            'conflicts_found': [],
            'availability_updated': False,
            'errors': []
        }
        
        try:
            # Get events from all calendars
            personal_events = await self._get_calendar_events('personal')
            work_events = await self._get_calendar_events('work')
            
            # Detect conflicts
            conflicts = await self._detect_conflicts(personal_events, work_events)
            result['conflicts_found'] = conflicts
            
            # Resolve conflicts based on strategy
            if conflicts:
                resolved_conflicts = await self._resolve_conflicts(conflicts)
                result['resolved_conflicts'] = resolved_conflicts
            
            # Update availability cache
            await self._update_availability_cache(personal_events + work_events)
            result['availability_updated'] = True
            
            result['synced_events'] = len(personal_events) + len(work_events)
            
        except Exception as e:
            logger.error(f"Error during calendar sync: {e}")
            result['errors'].append(str(e))
        
        return result
    
    async def get_availability(
        self, 
        start_date: datetime, 
        end_date: datetime,
        duration_minutes: int = 60,
        calendar_types: Optional[List[str]] = None
    ) -> List[AvailabilitySlot]:
        """
        Get available time slots for scheduling.
        
        Args:
            start_date: Start of the time range to check
            end_date: End of the time range to check
            duration_minutes: Minimum duration needed
            calendar_types: Which calendars to consider ('personal', 'work', or both)
            
        Returns:
            List of available time slots
        """
        logger.info(f"Finding availability from {start_date} to {end_date}")
        
        if calendar_types is None:
            calendar_types = ['personal', 'work']
        
        # Get all events in the time range
        all_events = []
        for calendar_type in calendar_types:
            events = await self._get_calendar_events(calendar_type, start_date, end_date)
            all_events.extend(events)
        
        # Find free slots
        availability_slots = self._find_free_slots(
            all_events, start_date, end_date, duration_minutes
        )
        
        # Filter by working hours if needed
        filtered_slots = self._filter_by_working_hours(availability_slots)
        
        return filtered_slots
    
    async def suggest_meeting_time(
        self,
        attendee_emails: List[str],
        duration_minutes: int,
        preferred_times: Optional[List[Tuple[datetime, datetime]]] = None,
        earliest_date: Optional[datetime] = None,
        latest_date: Optional[datetime] = None
    ) -> List[AvailabilitySlot]:
        """
        Suggest optimal meeting times considering all attendees.
        
        Args:
            attendee_emails: List of attendee email addresses
            duration_minutes: Meeting duration in minutes
            preferred_times: Preferred time ranges
            earliest_date: Earliest acceptable date
            latest_date: Latest acceptable date
            
        Returns:
            List of suggested meeting times, ranked by suitability
        """
        logger.info(f"Suggesting meeting times for {len(attendee_emails)} attendees")
        
        # Default to next 2 weeks if no date range specified
        if earliest_date is None:
            earliest_date = datetime.now()
        if latest_date is None:
            latest_date = earliest_date + timedelta(days=14)
        
        # Get availability for the organizer (user)
        user_availability = await self.get_availability(
            earliest_date, latest_date, duration_minutes
        )
        
        # TODO: Integrate with attendee availability (would need their calendar access)
        # For now, just return user availability
        
        # Rank suggestions based on preferences
        ranked_suggestions = self._rank_meeting_suggestions(
            user_availability, preferred_times
        )
        
        return ranked_suggestions[:10]  # Return top 10 suggestions

    async def _get_calendar_events(
        self, 
        calendar_type: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[CalendarEvent]:
        """Get events from a specific calendar."""
        # TODO: Implement actual calendar API calls
        # This is a placeholder that would integrate with Google Calendar, Outlook, etc.
        
        logger.info(f"Fetching {calendar_type} calendar events")
        
        # Placeholder - return empty list for now
        return []
    
    async def _detect_conflicts(
        self, 
        personal_events: List[CalendarEvent],
        work_events: List[CalendarEvent]
    ) -> List[CalendarConflict]:
        """Detect conflicts between calendar events."""
        conflicts = []
        
        # Check for overlapping events
        all_events = personal_events + work_events
        
        for i, event1 in enumerate(all_events):
            for event2 in all_events[i+1:]:
                if self._events_overlap(event1, event2):
                    conflict = CalendarConflict(
                        event1=event1,
                        event2=event2,
                        conflict_type='overlap',
                        severity=self._calculate_conflict_severity(event1, event2),
                        suggested_resolution=self._suggest_conflict_resolution(event1, event2)
                    )
                    conflicts.append(conflict)
        
        return conflicts
    
    async def _resolve_conflicts(self, conflicts: List[CalendarConflict]) -> List[Dict[str, Any]]:
        """Resolve calendar conflicts based on configured strategy."""
        resolved = []
        
        strategy = ConflictResolution(self.calendar_config.sync.conflict_resolution)
        
        for conflict in conflicts:
            resolution = {
                'conflict': conflict,
                'action': 'none',
                'reason': ''
            }
            
            if strategy == ConflictResolution.PREFER_WORK:
                if conflict.event1.calendar_type == 'work':
                    resolution['action'] = 'keep_event1'
                    resolution['reason'] = 'Work calendar takes priority'
                elif conflict.event2.calendar_type == 'work':
                    resolution['action'] = 'keep_event2'
                    resolution['reason'] = 'Work calendar takes priority'
            
            elif strategy == ConflictResolution.PREFER_PERSONAL:
                if conflict.event1.calendar_type == 'personal':
                    resolution['action'] = 'keep_event1'
                    resolution['reason'] = 'Personal calendar takes priority'
                elif conflict.event2.calendar_type == 'personal':
                    resolution['action'] = 'keep_event2'
                    resolution['reason'] = 'Personal calendar takes priority'
            
            else:  # MANUAL
                resolution['action'] = 'manual_review'
                resolution['reason'] = 'Manual review required'
            
            resolved.append(resolution)
        
        return resolved
    
    def _events_overlap(self, event1: CalendarEvent, event2: CalendarEvent) -> bool:
        """Check if two events overlap in time."""
        return (event1.start_time < event2.end_time and 
                event2.start_time < event1.end_time)
    
    def _calculate_conflict_severity(self, event1: CalendarEvent, event2: CalendarEvent) -> float:
        """Calculate the severity of a conflict between two events."""
        # Simple implementation - could be enhanced with more factors
        overlap_duration = min(event1.end_time, event2.end_time) - max(event1.start_time, event2.start_time)
        total_duration = max(event1.end_time, event2.end_time) - min(event1.start_time, event2.start_time)
        
        return overlap_duration.total_seconds() / total_duration.total_seconds()
    
    def _suggest_conflict_resolution(self, event1: CalendarEvent, event2: CalendarEvent) -> str:
        """Suggest how to resolve a conflict between two events."""
        if event1.calendar_type == 'work' and event2.calendar_type == 'personal':
            return "Consider rescheduling personal event"
        elif event1.calendar_type == 'personal' and event2.calendar_type == 'work':
            return "Consider rescheduling personal event"
        else:
            return "Manual review required"
    
    def _find_free_slots(
        self,
        events: List[CalendarEvent],
        start_date: datetime,
        end_date: datetime,
        duration_minutes: int
    ) -> List[AvailabilitySlot]:
        """Find free time slots between events."""
        # Sort events by start time
        sorted_events = sorted(events, key=lambda e: e.start_time)
        
        free_slots = []
        current_time = start_date
        
        for event in sorted_events:
            # If there's a gap before this event
            if current_time + timedelta(minutes=duration_minutes + self.buffer_minutes) <= event.start_time:
                slot = AvailabilitySlot(
                    start_time=current_time,
                    end_time=event.start_time - timedelta(minutes=self.buffer_minutes),
                    calendar_types=['personal', 'work'],  # Both calendars are free
                    confidence=0.9
                )
                free_slots.append(slot)
            
            # Move current time to after this event
            current_time = max(current_time, event.end_time + timedelta(minutes=self.buffer_minutes))
        
        # Check for time after the last event
        if current_time + timedelta(minutes=duration_minutes) <= end_date:
            slot = AvailabilitySlot(
                start_time=current_time,
                end_time=end_date,
                calendar_types=['personal', 'work'],
                confidence=0.9
            )
            free_slots.append(slot)
        
        return free_slots
    
    def _filter_by_working_hours(self, slots: List[AvailabilitySlot]) -> List[AvailabilitySlot]:
        """Filter availability slots to only include working hours."""
        filtered_slots = []
        
        for slot in slots:
            # TODO: Implement working hours filtering
            # For now, just return all slots
            filtered_slots.append(slot)
        
        return filtered_slots
    
    def _rank_meeting_suggestions(
        self,
        availability_slots: List[AvailabilitySlot],
        preferred_times: Optional[List[Tuple[datetime, datetime]]] = None
    ) -> List[AvailabilitySlot]:
        """Rank meeting suggestions by suitability."""
        # Simple ranking - could be enhanced with ML
        ranked_slots = sorted(availability_slots, key=lambda slot: (
            -slot.confidence,  # Higher confidence first
            slot.start_time    # Earlier times preferred
        ))
        
        return ranked_slots
    
    async def _update_availability_cache(self, events: List[CalendarEvent]) -> None:
        """Update the availability cache with current events."""
        # TODO: Implement availability caching
        logger.info(f"Updated availability cache with {len(events)} events")
        pass


# Create global instance
calendar_manager_agent = CalendarManagerAgent()
