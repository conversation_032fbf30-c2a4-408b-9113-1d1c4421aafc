"""Main email processing service that orchestrates email handling."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import asdict

from ...core.logging import logger
from ...core.config import settings
from ...agents.email_processor import EmailProcessorAgent
from ...learning.change_tracker import change_tracker, ChangeType
from .gmail_client import GmailClient
from .parser import EmailParser


class EmailService:
    """
    Main email processing service that coordinates email fetching,
    parsing, and processing across multiple accounts and providers.
    """
    
    def __init__(self):
        self.config = settings.integrations.email
        self.processing_config = self.config.processing
        
        # Initialize components
        self.gmail_clients = {}
        self.email_parser = EmailParser()
        self.email_processor = EmailProcessorAgent()
        
        # Processing state
        self.last_sync_times = {}
        self.is_processing = False
        
    async def initialize(self) -> bool:
        """Initialize email service and authenticate with providers."""
        logger.info("Initializing email service...")
        
        success = True
        
        # Initialize Gmail clients - focus on personal account only for now
        if self.config.providers.gmail.enabled:
            # Only initialize personal account
            gmail_accounts = self.config.providers.gmail.accounts
            if hasattr(gmail_accounts, 'personal') and getattr(gmail_accounts, 'personal') is not None:
                try:
                    client = GmailClient('personal')
                    if await client.authenticate():
                        self.gmail_clients['personal'] = client
                        logger.info("Successfully initialized Gmail personal account")
                    else:
                        logger.error("Failed to authenticate Gmail personal account")
                        success = False
                except Exception as e:
                    logger.error(f"Error initializing Gmail personal account: {e}")
                    success = False
            else:
                logger.info("Gmail personal account not configured, skipping")
        
        # TODO: Initialize Proton client
        if self.config.providers.proton.enabled:
            logger.info("Proton Mail integration not yet implemented")
        
        return success
    
    async def sync_all_emails(self, force_full_sync: bool = False) -> Dict[str, Any]:
        """
        Sync emails from all configured providers.
        
        Args:
            force_full_sync: If True, ignore last sync time and sync all emails
            
        Returns:
            Dictionary with sync results
        """
        if self.is_processing:
            logger.warning("Email sync already in progress, skipping")
            return {'status': 'skipped', 'reason': 'already_processing'}
        
        self.is_processing = True
        logger.info("Starting email sync across all accounts")
        
        results = {
            'status': 'success',
            'accounts_processed': 0,
            'total_emails': 0,
            'new_tasks': 0,
            'new_contacts': 0,
            'errors': [],
            'processing_time': 0
        }
        
        start_time = datetime.now()
        
        try:
            # Process Gmail accounts
            for account_type, client in self.gmail_clients.items():
                try:
                    account_result = await self._sync_gmail_account(
                        client, account_type, force_full_sync
                    )
                    
                    results['accounts_processed'] += 1
                    results['total_emails'] += account_result.get('emails_processed', 0)
                    results['new_tasks'] += account_result.get('tasks_created', 0)
                    results['new_contacts'] += account_result.get('contacts_created', 0)
                    
                    if account_result.get('errors'):
                        results['errors'].extend(account_result['errors'])
                    
                except Exception as e:
                    error_msg = f"Error syncing Gmail {account_type}: {e}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            # TODO: Process Proton account
            
            # Update last sync time
            self.last_sync_times['last_full_sync'] = datetime.now()
            
        except Exception as e:
            logger.error(f"Unexpected error during email sync: {e}")
            results['status'] = 'error'
            results['errors'].append(str(e))
        
        finally:
            self.is_processing = False
            results['processing_time'] = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"Email sync completed: {results['total_emails']} emails, "
                   f"{results['new_tasks']} tasks, {results['new_contacts']} contacts")
        
        return results
    
    async def _sync_gmail_account(
        self,
        client: GmailClient,
        account_type: str,
        force_full_sync: bool
    ) -> Dict[str, Any]:
        """Sync emails from a specific Gmail account."""
        logger.info(f"Syncing Gmail {account_type} account")
        
        result = {
            'emails_processed': 0,
            'tasks_created': 0,
            'contacts_created': 0,
            'emails_tagged': 0,
            'errors': []
        }
        
        try:
            # Determine sync timeframe
            since_date = None
            if not force_full_sync:
                last_sync = self.last_sync_times.get(f'gmail_{account_type}')
                if last_sync:
                    since_date = last_sync - timedelta(hours=1)  # Small overlap
                else:
                    # First sync - get emails from last 30 days
                    since_date = datetime.now() - timedelta(days=self.processing_config.max_age_days)
            
            # Fetch emails
            emails = await client.get_messages(
                query="in:inbox",  # Only inbox as requested
                max_results=self.processing_config.batch_size,
                since_date=since_date
            )
            
            logger.info(f"Retrieved {len(emails)} emails from Gmail {account_type}")
            
            # Process each email
            for email_data in emails:
                try:
                    await self._process_single_email(email_data, client, result)
                except Exception as e:
                    error_msg = f"Error processing email {email_data.get('message_id', 'unknown')}: {e}"
                    logger.error(error_msg)
                    result['errors'].append(error_msg)
            
            # Update last sync time for this account
            self.last_sync_times[f'gmail_{account_type}'] = datetime.now()
            
        except Exception as e:
            error_msg = f"Error syncing Gmail {account_type} account: {e}"
            logger.error(error_msg)
            result['errors'].append(error_msg)
        
        return result
    
    async def _process_single_email(
        self,
        email_data: Dict[str, Any],
        client: GmailClient,
        result: Dict[str, Any]
    ) -> None:
        """Process a single email through the full pipeline."""
        message_id = email_data.get('message_id', 'unknown')
        
        try:
            # Parse email content
            parsed_email = self.email_parser.parse_email(email_data)
            
            # Process with AI agent
            processing_result = await self.email_processor.process_email(asdict(parsed_email))
            
            # Handle results
            await self._handle_processing_results(
                parsed_email, processing_result, client, result
            )
            
            result['emails_processed'] += 1
            
        except Exception as e:
            logger.error(f"Error in email processing pipeline for {message_id}: {e}")
            raise
    
    async def _handle_processing_results(
        self,
        parsed_email,
        processing_result: Dict[str, Any],
        client: GmailClient,
        result: Dict[str, Any]
    ) -> None:
        """Handle the results of email processing."""
        
        # Add tags to email
        tags_to_add = []
        
        # Add importance/urgency tags
        if processing_result.get('importance_score', 0) > 0.7:
            tags_to_add.append('Assistant/High-Importance')
        elif processing_result.get('importance_score', 0) < 0.3:
            tags_to_add.append('Assistant/Low-Importance')
        
        if processing_result.get('urgency_score', 0) > 0.7:
            tags_to_add.append('Assistant/Urgent')
        
        # Add category tags
        category = processing_result.get('category', 'unknown')
        if category != 'unknown':
            tags_to_add.append(f'Assistant/{category.title()}')
        
        # Add marketing tag if detected
        if processing_result.get('is_marketing', False):
            tags_to_add.append('Assistant/Marketing')
        
        # Apply tags to Gmail
        for tag in tags_to_add:
            try:
                await client.add_label(parsed_email.message_id, tag)
                result['emails_tagged'] += 1
            except Exception as e:
                logger.warning(f"Could not add tag '{tag}' to email: {e}")
        
        # Create tasks if any were extracted
        tasks = processing_result.get('tasks', [])
        for task_data in tasks:
            try:
                # TODO: Store task in database
                logger.info(f"Would create task: {task_data.get('title', 'Untitled')}")
                result['tasks_created'] += 1
            except Exception as e:
                logger.error(f"Error creating task: {e}")
        
        # Create/update contacts
        contacts = processing_result.get('contacts', [])
        for contact_data in contacts:
            try:
                # TODO: Store contact in database
                logger.info(f"Would create/update contact: {contact_data.get('email', 'Unknown')}")
                result['contacts_created'] += 1
            except Exception as e:
                logger.error(f"Error creating contact: {e}")
        
        # Handle unsubscribe suggestions
        if processing_result.get('should_unsubscribe', False):
            logger.info(f"Suggesting unsubscribe for marketing email: {parsed_email.subject}")
            # TODO: Implement unsubscribe handling
        
        # Store email data for future reference
        # TODO: Store in database
        logger.debug(f"Processed email: {parsed_email.subject}")
    
    async def get_sync_status(self) -> Dict[str, Any]:
        """Get current sync status and statistics."""
        return {
            'is_processing': self.is_processing,
            'last_sync_times': self.last_sync_times,
            'configured_accounts': {
                'gmail_personal': 'personal' in self.gmail_clients,
                'gmail_work': 'work' in self.gmail_clients,
                'proton': False  # TODO: Implement
            },
            'processing_config': {
                'check_interval_minutes': self.processing_config.check_interval_minutes,
                'batch_size': self.processing_config.batch_size,
                'auto_create_tasks': self.processing_config.auto_create_tasks,
                'auto_tag': self.processing_config.auto_tag
            }
        }
    
    async def process_user_feedback(
        self,
        email_id: str,
        feedback_type: str,
        original_value: Any,
        new_value: Any,
        context: str
    ) -> None:
        """Process user feedback for learning."""
        try:
            await change_tracker.track_change(
                change_type=ChangeType.EMAIL_IMPORTANCE_CHANGE,
                entity_type='email',
                entity_id=email_id,
                field_changed=feedback_type,
                original_value=original_value,
                new_value=new_value,
                context=context,
                user_effort=0.8,  # Manual feedback is high effort
                metadata={'feedback_source': 'email_service'}
            )
            
            # Trigger learning update
            await self.email_processor.learn_from_feedback({
                'feedback_type': feedback_type,
                'entity_type': 'email',
                'entity_id': email_id,
                'original_prediction': original_value,
                'user_correction': new_value,
                'context': context
            })
            
        except Exception as e:
            logger.error(f"Error processing user feedback: {e}")


# Global email service instance
email_service = EmailService()
