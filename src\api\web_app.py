"""Web application for the Assistant interface."""

from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
import time
import traceback
import uuid

from fastapi import FastAP<PERSON>, Request, UploadFile, File, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from pydantic import BaseModel

from ..core.config import settings
from ..core.logging import logger
from ..knowledge.task_service import task_service
from ..knowledge.database import get_db, init_database, close_database
from ..knowledge.contact_quality_service import contact_quality_service
from ..agents.email_intelligence_service import email_intelligence_service
from ..agents.email_response_generator import email_response_generator
from ..agents.email_processor import email_processor_agent
from ..agents.calendar_manager import calendar_manager_agent
from ..agents.task_manager import task_manager_agent
from ..agents.contact_manager import contact_manager_agent
from ..agents.note_keeper import note_keeper_agent
from ..agents.project_manager import project_manager_agent
from ..agents.research import research_agent
from ..agents.archivist import archivist_agent
from ..integrations.gmail.gmail_integration import gmail_integration

# Initialize FastAPI app
app = FastAPI(
    title=settings.api.web_ui_title,
    description="Local knowledge library powered by AI agents",
    version="0.1.0"
)

# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all HTTP requests and responses."""
    # Generate request ID for tracking
    request_id = str(uuid.uuid4())[:8]
    start_time = time.time()

    # Log request
    logger.info(
        f"[{request_id}] {request.method} {request.url.path} - "
        f"Client: {request.client.host if request.client else 'unknown'} - "
        f"User-Agent: {request.headers.get('user-agent', 'unknown')}"
    )

    # Add request ID to request state
    request.state.request_id = request_id

    try:
        # Process request
        response = await call_next(request)

        # Calculate processing time
        process_time = time.time() - start_time

        # Log response
        logger.info(
            f"[{request_id}] Response: {response.status_code} - "
            f"Time: {process_time:.3f}s"
        )

        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id

        return response

    except Exception as e:
        # Log error
        process_time = time.time() - start_time
        logger.error(
            f"[{request_id}] Error: {str(e)} - "
            f"Time: {process_time:.3f}s - "
            f"Traceback: {traceback.format_exc()}"
        )
        raise

# Database lifecycle events
@app.on_event("startup")
async def startup_event():
    """Initialize database on startup."""
    init_database()
    logger.info("Database initialized")

@app.on_event("shutdown")
async def shutdown_event():
    """Close database on shutdown."""
    close_database()
    logger.info("Database closed")

# Setup static files and templates
static_dir = Path(__file__).parent / "static"
templates_dir = Path(__file__).parent / "templates"

# Create directories if they don't exist
static_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
templates = Jinja2Templates(directory=str(templates_dir))


# Pydantic models for API
class SearchQuery(BaseModel):
    query: str
    filters: Optional[dict] = None
    limit: int = 10


class SearchResult(BaseModel):
    id: str
    title: str
    content: str
    score: float
    metadata: dict


class ChatMessage(BaseModel):
    message: str
    agent: Optional[str] = None


class ChatResponse(BaseModel):
    response: str
    agent: str
    timestamp: str


# Routes
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Main dashboard page."""
    return templates.TemplateResponse("index.html", {
        "request": request,
        "title": "Dashboard"
    })


@app.get("/search", response_class=HTMLResponse)
async def search_page(request: Request):
    """Search interface page."""
    return templates.TemplateResponse("search.html", {
        "request": request,
        "title": "Search"
    })


@app.get("/upload", response_class=HTMLResponse)
async def upload_page(request: Request):
    """Document upload page."""
    return templates.TemplateResponse("upload.html", {
        "request": request,
        "title": "Upload Documents"
    })


@app.get("/notes", response_class=HTMLResponse)
async def notes_page(request: Request):
    """Notes management page."""
    return templates.TemplateResponse("notes.html", {
        "request": request,
        "title": "Notes"
    })


@app.get("/tasks", response_class=HTMLResponse)
async def tasks_page(request: Request):
    """Task management page."""
    return templates.TemplateResponse("tasks.html", {
        "request": request,
        "title": "Tasks"
    })


@app.get("/projects", response_class=HTMLResponse)
async def projects_page(request: Request):
    """Project management page."""
    return templates.TemplateResponse("projects.html", {
        "request": request,
        "title": "Projects"
    })


@app.get("/chat", response_class=HTMLResponse)
async def chat_page(request: Request):
    """Agent chat interface page."""
    return templates.TemplateResponse("chat.html", {
        "request": request,
        "title": "Chat with Agents"
    })


@app.get("/email", response_class=HTMLResponse)
async def email_page(request: Request):
    """Email management page."""
    return templates.TemplateResponse("email.html", {
        "request": request,
        "title": "Email Management"
    })


@app.get("/calendar", response_class=HTMLResponse)
async def calendar_page(request: Request):
    """Calendar management page."""
    return templates.TemplateResponse("calendar.html", {
        "request": request,
        "title": "Calendar"
    })


@app.get("/contacts", response_class=HTMLResponse)
async def contacts_page(request: Request):
    """Contact management page."""
    return templates.TemplateResponse("contacts.html", {
        "request": request,
        "title": "Contacts"
    })


@app.get("/settings", response_class=HTMLResponse)
async def settings_page(request: Request):
    """Settings and configuration page."""
    return templates.TemplateResponse("settings.html", {
        "request": request,
        "title": "Settings"
    })


@app.get("/logs", response_class=HTMLResponse)
async def logs_page(request: Request):
    """Logs viewing page."""
    return templates.TemplateResponse("logs.html", {
        "request": request,
        "title": "System Logs"
    })


# API endpoints
@app.post("/api/search")
async def api_search(query: SearchQuery):
    """Search the knowledge base."""
    try:
        logger.info(f"Search query: {query.query}")

        # For now, return mock search results
        # TODO: Implement actual search functionality
        mock_results = [
            {
                "id": "1",
                "title": f"Document about {query.query}",
                "content": f"This is a sample document that matches your search for '{query.query}'...",
                "score": 0.95,
                "type": "document",
                "date": "2024-01-15",
                "snippet": f"Sample content related to {query.query}"
            },
            {
                "id": "2",
                "title": f"Note: {query.query} insights",
                "content": f"Personal notes about {query.query}...",
                "score": 0.87,
                "type": "note",
                "date": "2024-01-10",
                "snippet": f"Key insights about {query.query}"
            }
        ]

        return {
            "status": "success",
            "results": mock_results[:query.limit],
            "total": len(mock_results),
            "query": query.query
        }

    except Exception as e:
        logger.error(f"Search error: {e}")
        return {
            "status": "error",
            "message": f"Search failed: {str(e)}",
            "results": [],
            "total": 0
        }


@app.post("/api/upload")
async def api_upload(file: UploadFile = File(...)):
    """Upload documents for processing."""
    try:
        logger.info(f"Uploading file: {file.filename}")

        # For now, just return success
        # TODO: Implement actual file processing
        return {
            "status": "success",
            "message": f"File '{file.filename}' uploaded successfully",
            "filename": file.filename,
            "size": file.size if hasattr(file, 'size') else 0
        }

    except Exception as e:
        logger.error(f"Upload error: {e}")
        return {
            "status": "error",
            "message": f"Upload failed: {str(e)}"
        }


# Chat endpoint is defined later in the file


@app.post("/api/email/sync")
async def api_email_sync(force_full: bool = False):
    """Trigger email synchronization."""
    try:
        from ..integrations.email.email_service import email_service

        logger.info(f"Email sync requested (force_full={force_full})")
        result = await email_service.sync_all_emails(force_full_sync=force_full)

        return {
            "message": "Email sync completed",
            "status": result.get('status', 'unknown'),
            "results": result
        }
    except Exception as e:
        logger.error(f"Email sync failed: {e}")
        return {
            "message": f"Email sync failed: {str(e)}",
            "status": "error"
        }


@app.get("/api/email/status")
async def api_email_status():
    """Get email service status."""
    try:
        from ..integrations.email.email_service import email_service

        status = await email_service.get_sync_status()
        return {
            "status": "success",
            "email_service": status
        }
    except Exception as e:
        logger.error(f"Error getting email status: {e}")
        return {
            "status": "error",
            "message": str(e)
        }


@app.post("/api/email/initialize")
async def api_email_initialize():
    """Initialize email service and authenticate."""
    try:
        from ..integrations.email.email_service import email_service

        logger.info("Initializing email service")
        success = await email_service.initialize()

        if success:
            return {
                "message": "Email service initialized successfully",
                "status": "success"
            }
        else:
            return {
                "message": "Email service initialization failed",
                "status": "error"
            }
    except Exception as e:
        logger.error(f"Email initialization failed: {e}")
        return {
            "message": f"Email initialization failed: {str(e)}",
            "status": "error"
        }


@app.post("/api/email/feedback")
async def api_email_feedback(feedback_data: dict):
    """Submit feedback for email processing learning."""
    try:
        from ..integrations.email.email_service import email_service

        await email_service.process_user_feedback(
            email_id=feedback_data.get('email_id'),
            feedback_type=feedback_data.get('feedback_type'),
            original_value=feedback_data.get('original_value'),
            new_value=feedback_data.get('new_value'),
            context=feedback_data.get('context', 'unknown')
        )

        return {
            "message": "Feedback processed successfully",
            "status": "success"
        }
    except Exception as e:
        logger.error(f"Error processing email feedback: {e}")
        return {
            "message": f"Error processing feedback: {str(e)}",
            "status": "error"
        }


@app.post("/api/calendar/initialize")
async def api_calendar_initialize():
    """Initialize calendar service and authenticate."""
    try:
        from ..integrations.calendar.calendar_service import calendar_service

        logger.info("Initializing calendar service")
        success = await calendar_service.initialize()

        if success:
            return {
                "message": "Calendar service initialized successfully",
                "status": "success"
            }
        else:
            return {
                "message": "Calendar service initialization failed",
                "status": "error"
            }
    except Exception as e:
        logger.error(f"Calendar initialization failed: {e}")
        return {
            "message": f"Calendar initialization failed: {str(e)}",
            "status": "error"
        }


@app.post("/api/calendar/sync")
async def api_calendar_sync():
    """Trigger calendar synchronization."""
    try:
        from ..integrations.calendar.calendar_service import calendar_service

        logger.info("Calendar sync requested")
        result = await calendar_service.sync_calendars()

        return {
            "message": "Calendar sync completed",
            "status": result.get('status', 'unknown'),
            "results": result
        }
    except Exception as e:
        logger.error(f"Calendar sync failed: {e}")
        return {
            "message": f"Calendar sync failed: {str(e)}",
            "status": "error"
        }


@app.get("/api/calendar/events")
async def api_get_calendar_events(
    calendar_id: str = "primary",
    days_ahead: int = 30
):
    """Get calendar events."""
    try:
        from ..integrations.calendar.calendar_service import calendar_service
        from datetime import datetime, timedelta

        logger.info(f"Getting events from calendar {calendar_id}")

        start_date = datetime.now()
        end_date = start_date + timedelta(days=days_ahead)

        events = await calendar_service.get_events(
            start_date=start_date,
            end_date=end_date,
            calendar_id=calendar_id
        )

        return {
            "events": events,
            "calendar_id": calendar_id,
            "total": len(events)
        }
    except Exception as e:
        logger.error(f"Error getting calendar events: {e}")
        return {
            "message": f"Error getting calendar events: {str(e)}",
            "status": "error"
        }


@app.get("/api/calendar/calendars")
async def api_get_calendars():
    """Get list of available calendars."""
    try:
        from ..integrations.calendar.calendar_service import calendar_service

        logger.info("Getting calendar list")
        calendars = await calendar_service.get_calendars()

        return {
            "calendars": calendars,
            "total": len(calendars)
        }
    except Exception as e:
        logger.error(f"Error getting calendars: {e}")
        return {
            "message": f"Error getting calendars: {str(e)}",
            "status": "error"
        }


@app.get("/api/calendar/availability")
async def api_get_availability(
    days_ahead: int = 7,
    duration_minutes: int = 60
):
    """Get availability for scheduling."""
    try:
        from ..integrations.calendar.calendar_service import calendar_service
        from datetime import datetime, timedelta

        logger.info(f"Getting availability for next {days_ahead} days")

        start_date = datetime.now()
        end_date = start_date + timedelta(days=days_ahead)

        availability = await calendar_service.get_availability(
            start_date=start_date,
            end_date=end_date,
            duration_minutes=duration_minutes
        )

        return {
            "availability": availability,
            "duration_minutes": duration_minutes,
            "total_slots": len(availability)
        }
    except Exception as e:
        logger.error(f"Error getting availability: {e}")
        return {
            "message": f"Error getting availability: {str(e)}",
            "status": "error"
        }


@app.get("/api/tasks")
async def api_get_tasks(
    status: Optional[str] = None,
    priority: Optional[int] = None,
    source_type: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """Get tasks with optional filtering."""
    try:
        logger.info(f"Getting tasks with status: {status}, priority: {priority}, limit: {limit}")

        tasks = await task_service.get_tasks(
            status=status,
            priority=priority,
            source_type=source_type,
            limit=limit,
            offset=offset
        )

        # Convert tasks to dict format
        task_list = []
        for task in tasks:
            task_dict = {
                "id": str(task.id),
                "title": task.title,
                "description": task.description,
                "status": task.status,
                "priority": task.priority,
                "importance_score": task.importance_score,
                "due_date": task.due_date.isoformat() if task.due_date else None,
                "created_at": task.created_at.isoformat(),
                "updated_at": task.updated_at.isoformat(),
                "source_type": task.source_type,
                "source_id": task.source_id,
                "keywords_found": task.keywords_found,
                "metadata": task.task_metadata
            }
            task_list.append(task_dict)

        return {
            "tasks": task_list,
            "total": len(task_list),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error getting tasks: {e}")
        return {
            "message": f"Error getting tasks: {str(e)}",
            "status": "error"
        }


@app.post("/api/tasks")
async def api_create_task(task_data: dict):
    """Create a new task."""
    try:
        logger.info(f"Creating task: {task_data.get('title', 'Untitled')}")

        # Extract task data
        title = task_data.get('title')
        if not title:
            raise HTTPException(status_code=400, detail="Task title is required")

        description = task_data.get('description')
        priority = task_data.get('priority', 3)
        due_date = None
        if task_data.get('due_date'):
            from datetime import datetime
            due_date = datetime.fromisoformat(task_data['due_date'].replace('Z', '+00:00'))

        # Create task
        task = await task_service.create_task(
            title=title,
            description=description,
            priority=priority,
            due_date=due_date,
            metadata=task_data.get('metadata', {})
        )

        return {
            "message": "Task created successfully",
            "task_id": str(task.id),
            "task": {
                "id": str(task.id),
                "title": task.title,
                "description": task.description,
                "status": task.status,
                "priority": task.priority,
                "due_date": task.due_date.isoformat() if task.due_date else None,
                "created_at": task.created_at.isoformat()
            },
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error creating task: {e}")
        return {
            "message": f"Error creating task: {str(e)}",
            "status": "error"
        }


@app.put("/api/tasks/{task_id}")
async def api_update_task(task_id: str, task_data: dict):
    """Update an existing task."""
    try:
        logger.info(f"Updating task {task_id}")

        # Prepare updates
        updates = {}

        if 'title' in task_data:
            updates['title'] = task_data['title']
        if 'description' in task_data:
            updates['description'] = task_data['description']
        if 'status' in task_data:
            updates['status'] = task_data['status']
        if 'priority' in task_data:
            updates['priority'] = task_data['priority']
        if 'user_priority' in task_data:
            updates['user_priority'] = task_data['user_priority']
        if 'due_date' in task_data:
            if task_data['due_date']:
                from datetime import datetime
                updates['due_date'] = datetime.fromisoformat(task_data['due_date'].replace('Z', '+00:00'))
            else:
                updates['due_date'] = None

        # Update task
        task = await task_service.update_task(task_id, updates)

        return {
            "message": "Task updated successfully",
            "task_id": str(task.id),
            "task": {
                "id": str(task.id),
                "title": task.title,
                "description": task.description,
                "status": task.status,
                "priority": task.priority,
                "user_priority": task.user_priority,
                "due_date": task.due_date.isoformat() if task.due_date else None,
                "updated_at": task.updated_at.isoformat()
            },
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error updating task {task_id}: {e}")
        return {
            "message": f"Error updating task: {str(e)}",
            "status": "error"
        }


@app.delete("/api/tasks/{task_id}")
async def api_delete_task(task_id: str):
    """Delete a task."""
    try:
        logger.info(f"Deleting task {task_id}")

        success = await task_service.delete_task(task_id)

        return {
            "message": "Task deleted successfully",
            "task_id": task_id,
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error deleting task {task_id}: {e}")
        return {
            "message": f"Error deleting task: {str(e)}",
            "status": "error"
        }


@app.get("/api/tasks/suggested")
async def api_get_suggested_tasks():
    """Get all suggested tasks awaiting approval."""
    try:
        logger.info("Getting suggested tasks")

        tasks = await task_service.get_suggested_tasks()

        # Convert tasks to dict format
        task_list = []
        for task in tasks:
            task_dict = {
                "id": str(task.id),
                "title": task.title,
                "description": task.description,
                "priority": task.priority,
                "importance_score": task.importance_score,
                "due_date": task.due_date.isoformat() if task.due_date else None,
                "created_at": task.created_at.isoformat(),
                "source_type": task.source_type,
                "source_id": task.source_id,
                "keywords_found": task.keywords_found,
                "extraction_confidence": task.extraction_confidence,
                "metadata": task.task_metadata
            }
            task_list.append(task_dict)

        return {
            "tasks": task_list,
            "total": len(task_list),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error getting suggested tasks: {e}")
        return {
            "message": f"Error getting suggested tasks: {str(e)}",
            "status": "error"
        }


@app.post("/api/tasks/{task_id}/approve")
async def api_approve_task(task_id: str):
    """Approve a suggested task."""
    try:
        logger.info(f"Approving task {task_id}")

        task = await task_service.approve_suggested_task(task_id)

        return {
            "message": "Task approved successfully",
            "task_id": str(task.id),
            "task": {
                "id": str(task.id),
                "title": task.title,
                "status": task.status,
                "updated_at": task.updated_at.isoformat()
            },
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error approving task {task_id}: {e}")
        return {
            "message": f"Error approving task: {str(e)}",
            "status": "error"
        }


@app.post("/api/tasks/{task_id}/reject")
async def api_reject_task(task_id: str):
    """Reject a suggested task."""
    try:
        logger.info(f"Rejecting task {task_id}")

        success = await task_service.reject_suggested_task(task_id)

        return {
            "message": "Task rejected successfully",
            "task_id": task_id,
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error rejecting task {task_id}: {e}")
        return {
            "message": f"Error rejecting task: {str(e)}",
            "status": "error"
        }


@app.get("/api/tasks/overdue")
async def api_get_overdue_tasks():
    """Get all overdue tasks."""
    try:
        logger.info("Getting overdue tasks")

        tasks = await task_service.get_overdue_tasks()

        # Convert tasks to dict format
        task_list = []
        for task in tasks:
            task_dict = {
                "id": str(task.id),
                "title": task.title,
                "description": task.description,
                "status": task.status,
                "priority": task.priority,
                "due_date": task.due_date.isoformat() if task.due_date else None,
                "created_at": task.created_at.isoformat(),
                "source_type": task.source_type,
                "days_overdue": (datetime.now() - task.due_date).days if task.due_date else 0
            }
            task_list.append(task_dict)

        return {
            "tasks": task_list,
            "total": len(task_list),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error getting overdue tasks: {e}")
        return {
            "message": f"Error getting overdue tasks: {str(e)}",
            "status": "error"
        }


# Contact Management Endpoints
@app.post("/api/contacts")
async def api_create_contact(contact_data: dict):
    """Create a new contact."""
    try:
        logger.info(f"Creating contact: {contact_data.get('email', 'No email')}")

        from ..knowledge.models import Contact
        import uuid

        with next(get_db()) as session:
            # Create contact
            contact = Contact(
                email=contact_data.get('email'),
                first_name=contact_data.get('first_name'),
                last_name=contact_data.get('last_name'),
                display_name=contact_data.get('display_name'),
                company=contact_data.get('company'),
                job_title=contact_data.get('job_title'),
                phone_numbers=contact_data.get('phone_numbers', []),
                addresses=contact_data.get('addresses', []),
                notes=contact_data.get('notes'),
                tags=contact_data.get('tags', []),
                source='manual',
                contact_metadata=contact_data.get('metadata', {})
            )

            session.add(contact)
            session.commit()
            session.refresh(contact)

            return {
                "message": "Contact created successfully",
                "contact_id": str(contact.id),
                "contact": {
                    "id": str(contact.id),
                    "email": contact.email,
                    "display_name": contact.display_name,
                    "first_name": contact.first_name,
                    "last_name": contact.last_name,
                    "phone_numbers": contact.phone_numbers,
                    "created_at": contact.created_at.isoformat()
                },
                "status": "success"
            }

    except Exception as e:
        logger.error(f"Error creating contact: {e}")
        return {
            "message": f"Error creating contact: {str(e)}",
            "status": "error"
        }


@app.get("/api/contacts")
async def api_get_contacts(limit: int = 50, offset: int = 0):
    """Get contacts."""
    try:
        logger.info(f"Getting contacts (limit: {limit}, offset: {offset})")

        from ..knowledge.models import Contact

        with next(get_db()) as session:
            contacts = session.query(Contact).offset(offset).limit(limit).all()

            contact_list = []
            for contact in contacts:
                contact_dict = {
                    "id": str(contact.id),
                    "email": contact.email,
                    "first_name": contact.first_name,
                    "last_name": contact.last_name,
                    "display_name": contact.display_name,
                    "company": contact.company,
                    "job_title": contact.job_title,
                    "phone_numbers": contact.phone_numbers,
                    "tags": contact.tags,
                    "created_at": contact.created_at.isoformat(),
                    "updated_at": contact.updated_at.isoformat()
                }
                contact_list.append(contact_dict)

            return {
                "contacts": contact_list,
                "total": len(contact_list),
                "status": "success"
            }

    except Exception as e:
        logger.error(f"Error getting contacts: {e}")
        return {
            "message": f"Error getting contacts: {str(e)}",
            "status": "error"
        }


# Contact Quality Management Endpoints
@app.get("/api/contacts/quality/validate/{contact_id}")
async def api_validate_contact_quality(contact_id: str):
    """Validate contact data quality."""
    try:
        logger.info(f"Validating contact quality: {contact_id}")

        # Get contact from database
        from ..knowledge.models import Contact
        import uuid

        with next(get_db()) as session:
            contact = session.query(Contact).filter(Contact.id == uuid.UUID(contact_id)).first()

            if not contact:
                raise HTTPException(status_code=404, detail="Contact not found")

            # Validate contact quality
            quality_report = await contact_quality_service.validate_contact(contact, session)

            return {
                "contact_id": contact_id,
                "quality_report": quality_report,
                "status": "success"
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating contact quality {contact_id}: {e}")
        return {
            "message": f"Error validating contact quality: {str(e)}",
            "status": "error"
        }


@app.post("/api/contacts/quality/fix/{contact_id}")
async def api_fix_contact_quality(contact_id: str, fixes_data: dict):
    """Apply quality fixes to a contact."""
    try:
        logger.info(f"Applying quality fixes to contact: {contact_id}")

        fixes = fixes_data.get('fixes', [])
        if not fixes:
            return {
                "message": "No fixes provided",
                "status": "error"
            }

        # Apply fixes
        result = await contact_quality_service.fix_contact_issues(contact_id, fixes)

        return {
            "contact_id": contact_id,
            "fix_result": result,
            "status": "success" if result.get('success') else "error"
        }

    except Exception as e:
        logger.error(f"Error fixing contact quality {contact_id}: {e}")
        return {
            "message": f"Error fixing contact quality: {str(e)}",
            "status": "error"
        }


@app.get("/api/contacts/quality/scan")
async def api_scan_all_contacts_quality(limit: int = 50, offset: int = 0):
    """Scan all contacts for quality issues."""
    try:
        logger.info(f"Scanning contacts for quality issues (limit: {limit}, offset: {offset})")

        from ..knowledge.models import Contact

        with next(get_db()) as session:
            # Get contacts to scan
            contacts = session.query(Contact).offset(offset).limit(limit).all()

            quality_reports = []

            for contact in contacts:
                try:
                    quality_report = await contact_quality_service.validate_contact(contact, session)

                    # Only include contacts with issues
                    if quality_report['issues']:
                        quality_reports.append({
                            'contact_id': str(contact.id),
                            'contact_name': contact.display_name or f"{contact.first_name or ''} {contact.last_name or ''}".strip(),
                            'contact_email': contact.email,
                            'quality_score': quality_report['quality_score'],
                            'issue_count': len(quality_report['issues']),
                            'issues': quality_report['issues'],
                            'suggestions': quality_report['suggestions']
                        })

                except Exception as e:
                    logger.error(f"Error validating contact {contact.id}: {e}")
                    continue

            # Sort by quality score (worst first)
            quality_reports.sort(key=lambda x: x['quality_score'])

            return {
                "contacts_scanned": len(contacts),
                "contacts_with_issues": len(quality_reports),
                "quality_reports": quality_reports,
                "status": "success"
            }

    except Exception as e:
        logger.error(f"Error scanning contacts for quality issues: {e}")
        return {
            "message": f"Error scanning contacts: {str(e)}",
            "status": "error"
        }


@app.post("/api/contacts/quality/auto-fix")
async def api_auto_fix_contacts(fix_data: dict):
    """Automatically fix common contact quality issues."""
    try:
        logger.info("Starting automatic contact quality fixes")

        # Get parameters
        max_contacts = fix_data.get('max_contacts', 10)
        fix_types = fix_data.get('fix_types', ['phone_cleanup', 'email_validation'])
        min_confidence = fix_data.get('min_confidence', 0.8)

        from ..knowledge.models import Contact

        with next(get_db()) as session:
            contacts = session.query(Contact).limit(max_contacts).all()

            fixed_contacts = []

            for contact in contacts:
                try:
                    # Validate contact
                    quality_report = await contact_quality_service.validate_contact(contact, session)

                    # Find auto-fixable issues
                    auto_fixes = []
                    for suggestion in quality_report.get('suggestions', []):
                        if (suggestion.get('type') in fix_types and
                            suggestion.get('confidence', 1.0) >= min_confidence):
                            auto_fixes.append({
                                'type': suggestion['type'],
                                'field': suggestion['field'],
                                'value': suggestion['suggested']
                            })

                    if auto_fixes:
                        # Apply fixes
                        fix_result = await contact_quality_service.fix_contact_issues(
                            str(contact.id), auto_fixes, session
                        )

                        if fix_result.get('success'):
                            fixed_contacts.append({
                                'contact_id': str(contact.id),
                                'contact_name': contact.display_name or f"{contact.first_name or ''} {contact.last_name or ''}".strip(),
                                'fixes_applied': fix_result.get('applied_fixes', [])
                            })

                except Exception as e:
                    logger.error(f"Error auto-fixing contact {contact.id}: {e}")
                    continue

            return {
                "contacts_processed": len(contacts),
                "contacts_fixed": len(fixed_contacts),
                "fixed_contacts": fixed_contacts,
                "status": "success"
            }

    except Exception as e:
        logger.error(f"Error in auto-fix contacts: {e}")
        return {
            "message": f"Error in auto-fix: {str(e)}",
            "status": "error"
        }


# Email Intelligence Endpoints
@app.post("/api/email/analyze")
async def api_analyze_email(email_data: dict):
    """Analyze an email for priority, sentiment, and recommended actions."""
    try:
        logger.info(f"Analyzing email: {email_data.get('subject', 'No subject')}")

        # Analyze email
        analysis = await email_intelligence_service.analyze_email(email_data)

        return {
            "email_id": analysis.email_id,
            "analysis": {
                "priority": analysis.priority.value,
                "urgency_score": analysis.urgency_score,
                "importance_score": analysis.importance_score,
                "sentiment": analysis.sentiment,
                "recommended_action": analysis.recommended_action.value,
                "response_deadline": analysis.response_deadline.isoformat() if analysis.response_deadline else None,
                "key_points": analysis.key_points,
                "questions_asked": analysis.questions_asked,
                "action_items": analysis.action_items,
                "meeting_request": analysis.meeting_request,
                "requires_decision": analysis.requires_decision,
                "confidence": analysis.confidence,
                "reasoning": analysis.reasoning
            },
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error analyzing email: {e}")
        return {
            "message": f"Error analyzing email: {str(e)}",
            "status": "error"
        }


@app.post("/api/email/generate-response")
async def api_generate_email_response(request_data: dict):
    """Generate a professional email response based on user decision."""
    try:
        logger.info(f"Generating email response for decision: {request_data.get('user_decision', 'No decision')}")

        # Extract request data
        email_analysis_data = request_data.get('email_analysis', {})
        user_decision = request_data.get('user_decision', '')
        user_context = request_data.get('context', {})

        # Convert analysis data back to EmailAnalysis object
        from ..agents.email_intelligence_service import EmailAnalysis, EmailPriority, EmailAction

        analysis = EmailAnalysis(
            email_id=email_analysis_data.get('email_id', ''),
            priority=EmailPriority(email_analysis_data.get('priority', 'medium')),
            urgency_score=email_analysis_data.get('urgency_score', 0.5),
            importance_score=email_analysis_data.get('importance_score', 0.5),
            sentiment=email_analysis_data.get('sentiment', 'neutral'),
            recommended_action=EmailAction(email_analysis_data.get('recommended_action', 'respond_later')),
            response_deadline=None,  # Will be parsed if needed
            key_points=email_analysis_data.get('key_points', []),
            questions_asked=email_analysis_data.get('questions_asked', []),
            action_items=email_analysis_data.get('action_items', []),
            meeting_request=email_analysis_data.get('meeting_request', False),
            requires_decision=email_analysis_data.get('requires_decision', False),
            confidence=email_analysis_data.get('confidence', 0.5),
            reasoning=email_analysis_data.get('reasoning', '')
        )

        # Generate response
        response_draft = await email_response_generator.generate_response(
            analysis, user_decision, user_context
        )

        return {
            "response_draft": {
                "original_email_id": response_draft.original_email_id,
                "subject": response_draft.subject,
                "body": response_draft.body,
                "tone": response_draft.tone,
                "confidence": response_draft.confidence,
                "reasoning": response_draft.reasoning,
                "requires_review": response_draft.requires_review
            },
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error generating email response: {e}")
        return {
            "message": f"Error generating email response: {str(e)}",
            "status": "error"
        }


@app.get("/api/email/priority-inbox")
async def api_get_priority_inbox(limit: int = 20):
    """Get prioritized inbox with intelligent analysis."""
    try:
        logger.info(f"Getting priority inbox (limit: {limit})")

        # This would integrate with your email service to get recent emails
        # For now, return a placeholder structure

        return {
            "message": "Priority inbox feature requires email sync to be active",
            "priority_emails": [],
            "total": 0,
            "status": "info"
        }

    except Exception as e:
        logger.error(f"Error getting priority inbox: {e}")
        return {
            "message": f"Error getting priority inbox: {str(e)}",
            "status": "error"
        }


@app.post("/api/email/quick-actions")
async def api_email_quick_actions(action_data: dict):
    """Perform quick actions on emails (archive, respond, delegate, etc.)."""
    try:
        logger.info(f"Performing email quick action: {action_data.get('action', 'No action')}")

        email_id = action_data.get('email_id')
        action = action_data.get('action')

        if not email_id or not action:
            return {
                "message": "Email ID and action are required",
                "status": "error"
            }

        # This would integrate with your email service
        # For now, return success for supported actions
        supported_actions = [
            'archive', 'mark_read', 'mark_unread', 'star', 'unstar',
            'move_to_folder', 'add_label', 'remove_label'
        ]

        if action in supported_actions:
            return {
                "message": f"Action '{action}' performed on email {email_id}",
                "email_id": email_id,
                "action": action,
                "status": "success"
            }
        else:
            return {
                "message": f"Unsupported action: {action}",
                "status": "error"
            }

    except Exception as e:
        logger.error(f"Error performing email quick action: {e}")
        return {
            "message": f"Error performing email action: {str(e)}",
            "status": "error"
        }


# Gmail Integration Endpoints
@app.post("/api/gmail/initialize")
async def api_gmail_initialize():
    """Initialize Gmail integration and authenticate."""
    try:
        logger.info("=== Starting Gmail integration initialization ===")
        logger.info(f"Gmail integration object: {gmail_integration}")
        logger.info(f"Gmail service object: {gmail_integration.gmail_service}")

        success = await gmail_integration.initialize()
        logger.info(f"Gmail integration initialize() returned: {success}")

        if success:
            user_email = gmail_integration.gmail_service.user_email
            logger.info(f"Gmail authentication successful for: {user_email}")
            return {
                "message": "Gmail integration initialized successfully",
                "user_email": user_email,
                "status": "success"
            }
        else:
            logger.error("Gmail integration initialize() returned False")
            return {
                "message": "Gmail authentication failed. Please check credentials.",
                "status": "error"
            }

    except Exception as e:
        logger.error(f"Exception in Gmail integration: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "message": f"Error initializing Gmail: {str(e)}",
            "status": "error"
        }


@app.get("/api/gmail/status")
async def api_gmail_status():
    """Get Gmail integration status."""
    try:
        status = gmail_integration.get_status()
        return {
            "gmail_status": status,
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error getting Gmail status: {e}")
        return {
            "message": f"Error getting Gmail status: {str(e)}",
            "status": "error"
        }


@app.get("/api/gmail/unread")
async def api_gmail_get_unread(max_emails: int = 50):
    """Get and process unread emails from Gmail."""
    try:
        logger.info(f"Getting unread emails from Gmail (max: {max_emails})")

        processed_emails = await gmail_integration.process_unread_emails(max_emails)

        # Convert to JSON-serializable format
        email_list = []
        for processed in processed_emails:
            email_dict = {
                "id": processed.gmail_message.id,
                "subject": processed.gmail_message.subject,
                "sender_email": processed.gmail_message.sender_email,
                "sender_name": processed.gmail_message.sender_name,
                "received_date": processed.gmail_message.received_date.isoformat(),
                "snippet": processed.gmail_message.snippet,
                "is_unread": processed.gmail_message.is_unread,
                "is_important": processed.gmail_message.is_important,
                "requires_attention": processed.requires_attention,
                "analysis": {
                    "priority": processed.analysis.priority.value,
                    "urgency_score": processed.analysis.urgency_score,
                    "importance_score": processed.analysis.importance_score,
                    "sentiment": processed.analysis.sentiment,
                    "recommended_action": processed.analysis.recommended_action.value,
                    "response_deadline": processed.analysis.response_deadline.isoformat() if processed.analysis.response_deadline else None,
                    "key_points": processed.analysis.key_points,
                    "questions_asked": processed.analysis.questions_asked,
                    "action_items": processed.analysis.action_items,
                    "meeting_request": processed.analysis.meeting_request,
                    "requires_decision": processed.analysis.requires_decision,
                    "confidence": processed.analysis.confidence,
                    "reasoning": processed.analysis.reasoning
                }
            }
            email_list.append(email_dict)

        return {
            "emails": email_list,
            "total": len(email_list),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error getting unread emails: {e}")
        return {
            "message": f"Error getting unread emails: {str(e)}",
            "status": "error"
        }


@app.get("/api/gmail/priority-inbox")
async def api_gmail_priority_inbox(max_emails: int = 20):
    """Get priority inbox with intelligent analysis."""
    try:
        logger.info(f"Getting priority inbox from Gmail (max: {max_emails})")

        processed_emails = await gmail_integration.get_priority_inbox(max_emails)

        # Convert to JSON-serializable format
        email_list = []
        for processed in processed_emails:
            email_dict = {
                "id": processed.gmail_message.id,
                "subject": processed.gmail_message.subject,
                "sender_email": processed.gmail_message.sender_email,
                "sender_name": processed.gmail_message.sender_name,
                "received_date": processed.gmail_message.received_date.isoformat(),
                "snippet": processed.gmail_message.snippet,
                "requires_attention": processed.requires_attention,
                "analysis": {
                    "priority": processed.analysis.priority.value,
                    "urgency_score": processed.analysis.urgency_score,
                    "importance_score": processed.analysis.importance_score,
                    "sentiment": processed.analysis.sentiment,
                    "recommended_action": processed.analysis.recommended_action.value,
                    "key_points": processed.analysis.key_points[:3],  # Limit for display
                    "questions_asked": processed.analysis.questions_asked,
                    "requires_decision": processed.analysis.requires_decision,
                    "confidence": processed.analysis.confidence,
                    "reasoning": processed.analysis.reasoning
                }
            }
            email_list.append(email_dict)

        return {
            "priority_emails": email_list,
            "total": len(email_list),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error getting priority inbox: {e}")
        return {
            "message": f"Error getting priority inbox: {str(e)}",
            "status": "error"
        }


@app.post("/api/gmail/reply")
async def api_gmail_reply(request_data: dict):
    """Generate and send a reply to a Gmail message."""
    try:
        email_id = request_data.get('email_id')
        user_decision = request_data.get('user_decision')
        send_immediately = request_data.get('send_immediately', False)

        if not email_id or not user_decision:
            return {
                "message": "Email ID and user decision are required",
                "status": "error"
            }

        logger.info(f"Generating reply for Gmail message {email_id}")

        # Get the Gmail message (this would need to be implemented)
        # For now, we'll return a placeholder
        return {
            "message": "Gmail reply functionality requires message retrieval implementation",
            "email_id": email_id,
            "user_decision": user_decision,
            "status": "info"
        }

    except Exception as e:
        logger.error(f"Error generating Gmail reply: {e}")
        return {
            "message": f"Error generating Gmail reply: {str(e)}",
            "status": "error"
        }


@app.post("/api/gmail/sync")
async def api_gmail_sync(hours: int = 1):
    """Sync recent Gmail messages."""
    try:
        logger.info(f"Syncing Gmail messages from last {hours} hours")

        processed_count = await gmail_integration.sync_recent_emails(hours)

        return {
            "message": f"Synced {processed_count} emails successfully",
            "processed_count": processed_count,
            "hours": hours,
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Error syncing Gmail: {e}")
        return {
            "message": f"Error syncing Gmail: {str(e)}",
            "status": "error"
        }


@app.post("/api/contacts/initialize")
async def api_contacts_initialize():
    """Initialize contacts service and authenticate."""
    try:
        from ..integrations.contacts.contacts_service import contacts_service

        logger.info("Initializing contacts service")
        success = await contacts_service.initialize()

        if success:
            return {
                "message": "Contacts service initialized successfully",
                "status": "success"
            }
        else:
            return {
                "message": "Contacts service initialization failed",
                "status": "error"
            }
    except Exception as e:
        logger.error(f"Contacts initialization failed: {e}")
        return {
            "message": f"Contacts initialization failed: {str(e)}",
            "status": "error"
        }


@app.post("/api/contacts/sync")
async def api_contacts_sync():
    """Trigger contacts synchronization."""
    try:
        from ..integrations.contacts.contacts_service import contacts_service

        logger.info("Contacts sync requested")
        result = await contacts_service.sync_contacts()

        return {
            "message": "Contacts sync completed",
            "status": result.get('status', 'unknown'),
            "results": result
        }
    except Exception as e:
        logger.error(f"Contacts sync failed: {e}")
        return {
            "message": f"Contacts sync failed: {str(e)}",
            "status": "error"
        }


@app.get("/api/contacts")
async def api_get_contacts(search: Optional[str] = None, limit: int = 50):
    """Get contacts with optional search."""
    try:
        from ..integrations.contacts.contacts_service import contacts_service

        logger.info(f"Getting contacts with search: {search}, limit: {limit}")
        contacts = await contacts_service.get_contacts(
            search_query=search,
            limit=limit
        )

        return {
            "contacts": contacts,
            "total": len(contacts),
            "search": search
        }
    except Exception as e:
        logger.error(f"Error getting contacts: {e}")
        return {
            "message": f"Error getting contacts: {str(e)}",
            "status": "error"
        }


@app.get("/api/contacts/stats")
async def api_get_contact_stats():
    """Get contact statistics."""
    try:
        from ..integrations.contacts.contacts_service import contacts_service

        logger.info("Getting contact statistics")
        stats = await contacts_service.get_contact_stats()

        return {
            "stats": stats,
            "status": "success"
        }
    except Exception as e:
        logger.error(f"Error getting contact stats: {e}")
        return {
            "message": f"Error getting contact stats: {str(e)}",
            "status": "error"
        }


@app.post("/api/contacts/search")
async def api_search_contacts(search_data: dict):
    """Search contacts by query."""
    try:
        from ..integrations.contacts.contacts_service import contacts_service

        query = search_data.get('query', '')
        logger.info(f"Searching contacts for: {query}")

        contacts = await contacts_service.search_contacts(query)

        return {
            "contacts": contacts,
            "total": len(contacts),
            "query": query
        }
    except Exception as e:
        logger.error(f"Error searching contacts: {e}")
        return {
            "message": f"Error searching contacts: {str(e)}",
            "status": "error"
        }


@app.post("/api/contacts/import")
async def api_import_contacts(file: UploadFile = File(...)):
    """Import contacts from CSV file."""
    try:
        from ..integrations.contacts.contacts_service import contacts_service

        logger.info(f"Importing contacts from {file.filename}")

        # Read CSV content
        content = await file.read()
        csv_data = content.decode('utf-8')

        result = await contacts_service.import_from_csv(csv_data)

        return {
            "message": f"Import completed from {file.filename}",
            "status": result.get('status', 'unknown'),
            "results": result
        }
    except Exception as e:
        logger.error(f"Error importing contacts: {e}")
        return {
            "message": f"Error importing contacts: {str(e)}",
            "status": "error"
        }


@app.get("/api/calendar/availability")
async def api_get_availability(
    start_date: str,
    end_date: str,
    duration_minutes: int = 60
):
    """Get availability for scheduling."""
    # TODO: Implement availability calculation
    logger.info(f"Getting availability from {start_date} to {end_date}")
    return {"availability_slots": [], "total_slots": 0}


# Notes API Endpoints
@app.get("/api/notes")
async def api_get_notes(sort: str = "updated", category: str = "all"):
    """Get notes with optional filtering and sorting."""
    try:
        logger.info(f"Getting notes (sort: {sort}, category: {category})")

        # Mock notes data
        mock_notes = [
            {
                "id": "1",
                "title": "Meeting Notes - Project Alpha",
                "content": "Discussed project timeline and deliverables...",
                "category": "work",
                "created_at": "2024-01-15T10:00:00Z",
                "updated_at": "2024-01-15T15:30:00Z"
            },
            {
                "id": "2",
                "title": "Personal Ideas",
                "content": "Some thoughts about improving productivity...",
                "category": "personal",
                "created_at": "2024-01-14T09:00:00Z",
                "updated_at": "2024-01-14T09:30:00Z"
            },
            {
                "id": "3",
                "title": "Research Notes",
                "content": "Key findings from today's research session...",
                "category": "research",
                "created_at": "2024-01-13T14:00:00Z",
                "updated_at": "2024-01-13T16:00:00Z"
            }
        ]

        # Filter by category if specified
        if category != "all":
            mock_notes = [note for note in mock_notes if note["category"] == category]

        return {
            "status": "success",
            "notes": mock_notes
        }

    except Exception as e:
        logger.error(f"Error getting notes: {e}")
        return {
            "status": "error",
            "message": f"Error getting notes: {str(e)}",
            "notes": []
        }


@app.post("/api/notes")
async def api_create_note(note_data: dict):
    """Create a new note."""
    try:
        logger.info(f"Creating note: {note_data.get('title', 'Untitled')}")

        # Mock note creation
        new_note = {
            "id": "new_note_id",
            "title": note_data.get("title", "Untitled"),
            "content": note_data.get("content", ""),
            "category": note_data.get("category", "general"),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }

        return {
            "status": "success",
            "message": "Note created successfully",
            "note": new_note
        }

    except Exception as e:
        logger.error(f"Error creating note: {e}")
        return {
            "status": "error",
            "message": f"Error creating note: {str(e)}"
        }


@app.put("/api/notes/{note_id}")
async def api_update_note(note_id: str, note_data: dict):
    """Update an existing note."""
    try:
        logger.info(f"Updating note {note_id}")

        # Mock note update
        updated_note = {
            "id": note_id,
            "title": note_data.get("title", "Untitled"),
            "content": note_data.get("content", ""),
            "category": note_data.get("category", "general"),
            "updated_at": datetime.now().isoformat()
        }

        return {
            "status": "success",
            "message": "Note updated successfully",
            "note": updated_note
        }

    except Exception as e:
        logger.error(f"Error updating note: {e}")
        return {
            "status": "error",
            "message": f"Error updating note: {str(e)}"
        }


@app.delete("/api/notes/{note_id}")
async def api_delete_note(note_id: str):
    """Delete a note."""
    try:
        logger.info(f"Deleting note {note_id}")

        return {
            "status": "success",
            "message": "Note deleted successfully"
        }

    except Exception as e:
        logger.error(f"Error deleting note: {e}")
        return {
            "status": "error",
            "message": f"Error deleting note: {str(e)}"
        }


@app.get("/api/notes/{note_id}")
async def api_get_note(note_id: str):
    """Get a specific note."""
    try:
        logger.info(f"Getting note {note_id}")

        # Mock note data
        note = {
            "id": note_id,
            "title": "Sample Note",
            "content": "This is a sample note content...",
            "category": "general",
            "created_at": "2024-01-15T10:00:00Z",
            "updated_at": "2024-01-15T15:30:00Z"
        }

        return {
            "status": "success",
            "note": note
        }

    except Exception as e:
        logger.error(f"Error getting note: {e}")
        return {
            "status": "error",
            "message": f"Error getting note: {str(e)}"
        }


# Projects API Endpoints
@app.get("/api/projects")
async def api_get_projects(status: str = "all", sort: str = "updated"):
    """Get projects with optional filtering and sorting."""
    try:
        logger.info(f"Getting projects (status: {status}, sort: {sort})")

        # Mock projects data
        mock_projects = [
            {
                "id": "1",
                "name": "Website Redesign",
                "description": "Complete overhaul of company website",
                "status": "active",
                "progress": 65,
                "due_date": "2024-02-15",
                "created_at": "2024-01-01T10:00:00Z",
                "updated_at": "2024-01-15T15:30:00Z",
                "tags": ["web", "design", "urgent"]
            },
            {
                "id": "2",
                "name": "Mobile App Development",
                "description": "Native mobile app for iOS and Android",
                "status": "active",
                "progress": 30,
                "due_date": "2024-03-30",
                "created_at": "2024-01-05T09:00:00Z",
                "updated_at": "2024-01-14T11:00:00Z",
                "tags": ["mobile", "development"]
            },
            {
                "id": "3",
                "name": "Marketing Campaign",
                "description": "Q1 marketing campaign planning",
                "status": "completed",
                "progress": 100,
                "due_date": "2024-01-31",
                "created_at": "2023-12-15T14:00:00Z",
                "updated_at": "2024-01-31T16:00:00Z",
                "tags": ["marketing", "campaign"]
            }
        ]

        # Filter by status if specified
        if status != "all":
            mock_projects = [project for project in mock_projects if project["status"] == status]

        return {
            "status": "success",
            "projects": mock_projects
        }

    except Exception as e:
        logger.error(f"Error getting projects: {e}")
        return {
            "status": "error",
            "message": f"Error getting projects: {str(e)}",
            "projects": []
        }


@app.post("/api/projects")
async def api_create_project(project_data: dict):
    """Create a new project."""
    try:
        logger.info(f"Creating project: {project_data.get('name', 'Untitled')}")

        # Mock project creation
        new_project = {
            "id": "new_project_id",
            "name": project_data.get("name", "Untitled Project"),
            "description": project_data.get("description", ""),
            "status": project_data.get("status", "active"),
            "progress": 0,
            "due_date": project_data.get("due_date"),
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "tags": project_data.get("tags", [])
        }

        return {
            "status": "success",
            "message": "Project created successfully",
            "project": new_project
        }

    except Exception as e:
        logger.error(f"Error creating project: {e}")
        return {
            "status": "error",
            "message": f"Error creating project: {str(e)}"
        }


@app.put("/api/projects/{project_id}")
async def api_update_project(project_id: str, project_data: dict):
    """Update an existing project."""
    try:
        logger.info(f"Updating project {project_id}")

        # Mock project update
        updated_project = {
            "id": project_id,
            "name": project_data.get("name", "Untitled Project"),
            "description": project_data.get("description", ""),
            "status": project_data.get("status", "active"),
            "progress": project_data.get("progress", 0),
            "due_date": project_data.get("due_date"),
            "updated_at": datetime.now().isoformat(),
            "tags": project_data.get("tags", [])
        }

        return {
            "status": "success",
            "message": "Project updated successfully",
            "project": updated_project
        }

    except Exception as e:
        logger.error(f"Error updating project: {e}")
        return {
            "status": "error",
            "message": f"Error updating project: {str(e)}"
        }


@app.delete("/api/projects/{project_id}")
async def api_delete_project(project_id: str):
    """Delete a project."""
    try:
        logger.info(f"Deleting project {project_id}")

        return {
            "status": "success",
            "message": "Project deleted successfully"
        }

    except Exception as e:
        logger.error(f"Error deleting project: {e}")
        return {
            "status": "error",
            "message": f"Error deleting project: {str(e)}"
        }


@app.get("/api/projects/stats")
async def api_get_project_stats():
    """Get project statistics."""
    try:
        logger.info("Getting project statistics")

        # Mock project stats
        stats = {
            "active": 2,
            "completed": 1,
            "on_hold": 0,
            "due_soon": 1,
            "total": 3
        }

        return {
            "status": "success",
            "stats": stats
        }

    except Exception as e:
        logger.error(f"Error getting project stats: {e}")
        return {
            "status": "error",
            "message": f"Error getting project stats: {str(e)}"
        }


# Calendar Events API Endpoints
@app.post("/api/calendar/events")
async def api_create_calendar_event(event_data: dict):
    """Create a new calendar event."""
    try:
        logger.info(f"Creating calendar event: {event_data.get('title', 'Untitled')}")

        # Mock event creation
        new_event = {
            "id": "new_event_id",
            "title": event_data.get("title", "Untitled Event"),
            "description": event_data.get("description", ""),
            "date": event_data.get("date"),
            "time": event_data.get("time"),
            "duration": event_data.get("duration", 60),
            "type": event_data.get("type", "meeting"),
            "all_day": event_data.get("all_day", False),
            "created_at": datetime.now().isoformat()
        }

        return {
            "status": "success",
            "message": "Event created successfully",
            "event": new_event
        }

    except Exception as e:
        logger.error(f"Error creating calendar event: {e}")
        return {
            "status": "error",
            "message": f"Error creating calendar event: {str(e)}"
        }


@app.put("/api/calendar/events/{event_id}")
async def api_update_calendar_event(event_id: str, event_data: dict):
    """Update an existing calendar event."""
    try:
        logger.info(f"Updating calendar event {event_id}")

        # Mock event update
        updated_event = {
            "id": event_id,
            "title": event_data.get("title", "Untitled Event"),
            "description": event_data.get("description", ""),
            "date": event_data.get("date"),
            "time": event_data.get("time"),
            "duration": event_data.get("duration", 60),
            "type": event_data.get("type", "meeting"),
            "all_day": event_data.get("all_day", False),
            "updated_at": datetime.now().isoformat()
        }

        return {
            "status": "success",
            "message": "Event updated successfully",
            "event": updated_event
        }

    except Exception as e:
        logger.error(f"Error updating calendar event: {e}")
        return {
            "status": "error",
            "message": f"Error updating calendar event: {str(e)}"
        }


@app.delete("/api/calendar/events/{event_id}")
async def api_delete_calendar_event(event_id: str):
    """Delete a calendar event."""
    try:
        logger.info(f"Deleting calendar event {event_id}")

        return {
            "status": "success",
            "message": "Event deleted successfully"
        }

    except Exception as e:
        logger.error(f"Error deleting calendar event: {e}")
        return {
            "status": "error",
            "message": f"Error deleting calendar event: {str(e)}"
        }


@app.get("/api/calendar/events/upcoming")
async def api_get_upcoming_events(limit: int = 10):
    """Get upcoming calendar events."""
    try:
        logger.info(f"Getting upcoming events (limit: {limit})")

        # Mock upcoming events
        upcoming_events = [
            {
                "id": "1",
                "title": "Team Meeting",
                "date": "2024-01-16",
                "time": "10:00",
                "type": "meeting",
                "all_day": False
            },
            {
                "id": "2",
                "title": "Project Review",
                "date": "2024-01-17",
                "time": "14:00",
                "type": "meeting",
                "all_day": False
            }
        ]

        return {
            "status": "success",
            "events": upcoming_events[:limit]
        }

    except Exception as e:
        logger.error(f"Error getting upcoming events: {e}")
        return {
            "status": "error",
            "message": f"Error getting upcoming events: {str(e)}",
            "events": []
        }


# Settings API Endpoints
@app.get("/api/settings")
async def api_get_settings():
    """Get user settings."""
    try:
        logger.info("Getting user settings")

        # Mock settings data
        settings_data = {
            "theme": "light",
            "language": "en",
            "email_notifications": True,
            "task_reminders": True,
            "calendar_alerts": True,
            "auto_email_analysis": True,
            "priority_threshold": 70,
            "auto_task_creation": True,
            "response_style": "professional",
            "response_length": "medium",
            "local_storage": True,
            "encrypt_data": True,
            "data_retention": "forever",
            "max_documents": 1000,
            "sync_interval": 15,
            "debug_mode": False,
            "verbose_logging": False
        }

        return {
            "status": "success",
            "settings": settings_data
        }

    except Exception as e:
        logger.error(f"Error getting settings: {e}")
        return {
            "status": "error",
            "message": f"Error getting settings: {str(e)}"
        }


@app.post("/api/settings")
async def api_save_settings(settings_data: dict):
    """Save user settings."""
    try:
        logger.info("Saving user settings")

        # Mock settings save
        return {
            "status": "success",
            "message": "Settings saved successfully"
        }

    except Exception as e:
        logger.error(f"Error saving settings: {e}")
        return {
            "status": "error",
            "message": f"Error saving settings: {str(e)}"
        }


@app.get("/api/integrations/status")
async def api_get_integrations_status():
    """Get integration status."""
    try:
        logger.info("Getting integrations status")

        # Check actual Gmail integration status
        gmail_status = gmail_integration.get_status()

        # Integration status with more detailed information
        integrations_status = {
            "gmail": {
                "connected": gmail_status.get("authenticated", False),
                "last_sync": gmail_integration.last_sync_time.isoformat() if gmail_integration.last_sync_time else None,
                "status": "connected" if gmail_status.get("authenticated", False) else "not_configured",
                "message": gmail_status.get("message", "Gmail integration status unknown"),
                "user_email": gmail_integration.gmail_service.user_email if gmail_integration.gmail_service.user_email else None
            },
            "calendar": {
                "connected": False,
                "last_sync": None,
                "status": "not_configured",
                "message": "Calendar integration not yet configured"
            },
            "contacts": {
                "connected": False,
                "last_sync": None,
                "status": "not_configured",
                "message": "Contacts integration not yet configured"
            }
        }

        return {
            "status": "success",
            "integrations": integrations_status
        }

    except Exception as e:
        logger.error(f"Error getting integrations status: {e}")
        return {
            "status": "error",
            "message": f"Error getting integrations status: {str(e)}"
        }


# Gmail Integration Endpoints

@app.post("/api/integrations/gmail/connect")
async def api_gmail_connect():
    """Connect to Gmail integration."""
    try:
        logger.info("Attempting to connect Gmail integration")

        # Use the real Gmail integration
        success = await gmail_integration.initialize()

        if success:
            return {
                "status": "success",
                "message": "Gmail integration connected successfully",
                "user_email": gmail_integration.gmail_service.user_email if gmail_integration.gmail_service.user_email else "Unknown",
                "connected": True
            }
        else:
            return {
                "status": "error",
                "message": "Gmail authentication failed. Google API credentials not configured.",
                "connected": False,
                "setup_required": True,
                "next_steps": [
                    "1. Create a Google Cloud project at console.cloud.google.com",
                    "2. Enable Gmail API in the project",
                    "3. Create OAuth 2.0 credentials (Desktop application)",
                    "4. Download credentials as 'gmail_personal_credentials.json'",
                    "5. Place the file in the 'config/' folder",
                    "6. Try connecting again"
                ],
                "help_url": "See GOOGLE_SETUP_GUIDE.md for detailed instructions"
            }
    except Exception as e:
        logger.error(f"Error connecting Gmail: {e}")
        return {
            "status": "error",
            "message": f"Error connecting Gmail: {str(e)}",
            "connected": False
        }


@app.post("/api/integrations/gmail/disconnect")
async def api_gmail_disconnect():
    """Disconnect Gmail integration."""
    try:
        logger.info("Disconnecting Gmail integration")

        # Reset the Gmail integration
        gmail_integration.gmail_service.service = None
        gmail_integration.gmail_service.credentials = None
        gmail_integration.gmail_service.user_email = None
        gmail_integration.last_sync_time = None

        return {
            "status": "success",
            "message": "Gmail integration disconnected successfully",
            "connected": False
        }
    except Exception as e:
        logger.error(f"Error disconnecting Gmail: {e}")
        return {
            "status": "error",
            "message": f"Error disconnecting Gmail: {str(e)}"
        }


@app.get("/api/integrations/gmail/status")
async def api_gmail_status():
    """Get Gmail integration status."""
    try:
        logger.info("Getting Gmail integration status")

        # Use the real Gmail integration status
        gmail_status = gmail_integration.get_status()

        return {
            "status": "success",
            "connected": gmail_status.get("authenticated", False),
            "last_sync": gmail_integration.last_sync_time.isoformat() if gmail_integration.last_sync_time else None,
            "account_info": {
                "email": gmail_integration.gmail_service.user_email if gmail_integration.gmail_service.user_email else None
            },
            "sync_status": "connected" if gmail_status.get("authenticated", False) else "not_configured",
            "message": gmail_status.get("message", "Gmail integration status unknown"),
            "details": gmail_status
        }
    except Exception as e:
        logger.error(f"Error getting Gmail status: {e}")
        return {
            "status": "error",
            "message": f"Error getting Gmail status: {str(e)}",
            "connected": False
        }


@app.get("/api/integrations/gmail/setup-status")
async def api_gmail_setup_status():
    """Check Gmail setup status and provide guidance."""
    try:
        import os

        # Check if credentials file exists
        credentials_path = "./config/gmail_personal_credentials.json"
        credentials_exist = os.path.exists(credentials_path)

        # Check if tokens directory exists
        tokens_dir = "./data/tokens"
        tokens_dir_exists = os.path.exists(tokens_dir)

        # Check if token file exists
        token_path = "./data/tokens/gmail_personal_token.json"
        token_exists = os.path.exists(token_path)

        setup_status = {
            "credentials_file_exists": credentials_exist,
            "credentials_path": credentials_path,
            "tokens_directory_exists": tokens_dir_exists,
            "tokens_directory": tokens_dir,
            "token_file_exists": token_exists,
            "token_path": token_path,
            "setup_complete": credentials_exist and tokens_dir_exists
        }

        if setup_status["setup_complete"]:
            message = "Gmail setup is ready. You can try connecting."
            status = "ready"
        elif credentials_exist:
            message = "Credentials found. Tokens directory ready. Try connecting to complete setup."
            status = "partial"
        else:
            message = "Gmail setup required. Please follow the setup guide."
            status = "not_configured"

        return {
            "status": "success",
            "setup_status": status,
            "message": message,
            "details": setup_status,
            "next_steps": [
                "See GOOGLE_SETUP_GUIDE.md for complete setup instructions",
                "Ensure you have created Google Cloud project and enabled Gmail API",
                "Download OAuth credentials and place in config/ folder"
            ] if status == "not_configured" else []
        }

    except Exception as e:
        logger.error(f"Error checking Gmail setup status: {e}")
        return {
            "status": "error",
            "message": f"Error checking setup status: {str(e)}"
        }


# Calendar Integration Endpoints

@app.post("/api/integrations/calendar/connect")
async def api_calendar_connect():
    """Connect to Calendar integration."""
    try:
        logger.info("Attempting to connect Calendar integration")

        return {
            "status": "info",
            "message": "Calendar integration is not yet implemented",
            "next_steps": [
                "Google Calendar OAuth integration is planned",
                "Currently using mock calendar data for development"
            ]
        }
    except Exception as e:
        logger.error(f"Error connecting Calendar: {e}")
        return {
            "status": "error",
            "message": f"Error connecting Calendar: {str(e)}"
        }


@app.post("/api/integrations/calendar/disconnect")
async def api_calendar_disconnect():
    """Disconnect Calendar integration."""
    try:
        logger.info("Disconnecting Calendar integration")

        return {
            "status": "success",
            "message": "Calendar integration disconnected"
        }
    except Exception as e:
        logger.error(f"Error disconnecting Calendar: {e}")
        return {
            "status": "error",
            "message": f"Error disconnecting Calendar: {str(e)}"
        }


# Contacts Integration Endpoints

@app.post("/api/integrations/contacts/connect")
async def api_contacts_connect():
    """Connect to Contacts integration."""
    try:
        logger.info("Attempting to connect Contacts integration")

        return {
            "status": "info",
            "message": "Contacts integration is not yet implemented",
            "next_steps": [
                "Google Contacts OAuth integration is planned",
                "Currently using mock contact data for development"
            ]
        }
    except Exception as e:
        logger.error(f"Error connecting Contacts: {e}")
        return {
            "status": "error",
            "message": f"Error connecting Contacts: {str(e)}"
        }


@app.post("/api/integrations/contacts/disconnect")
async def api_contacts_disconnect():
    """Disconnect Contacts integration."""
    try:
        logger.info("Disconnecting Contacts integration")

        return {
            "status": "success",
            "message": "Contacts integration disconnected"
        }
    except Exception as e:
        logger.error(f"Error disconnecting Contacts: {e}")
        return {
            "status": "error",
            "message": f"Error disconnecting Contacts: {str(e)}"
        }


# Additional API Endpoints
@app.get("/api/tasks/stats")
async def api_get_task_stats():
    """Get task statistics."""
    try:
        logger.info("Getting task statistics")

        # Mock task stats
        stats = {
            "total": 15,
            "in_progress": 5,
            "completed": 8,
            "high_priority": 3,
            "overdue": 1
        }

        return {
            "status": "success",
            "stats": stats
        }

    except Exception as e:
        logger.error(f"Error getting task stats: {e}")
        return {
            "status": "error",
            "message": f"Error getting task stats: {str(e)}"
        }


@app.post("/api/tasks/extract")
async def api_extract_tasks(request_data: dict):
    """Extract tasks from text using AI."""
    try:
        text = request_data.get("text", "")
        logger.info(f"Extracting tasks from text (length: {len(text)})")

        # Mock task extraction
        extracted_tasks = [
            {
                "title": "Follow up on project proposal",
                "description": "Send follow-up email to client about project proposal",
                "priority": "high",
                "confidence_score": 0.92
            },
            {
                "title": "Schedule team meeting",
                "description": "Organize weekly team sync meeting",
                "priority": "medium",
                "confidence_score": 0.85
            }
        ]

        return {
            "status": "success",
            "tasks": extracted_tasks,
            "total_extracted": len(extracted_tasks)
        }

    except Exception as e:
        logger.error(f"Error extracting tasks: {e}")
        return {
            "status": "error",
            "message": f"Error extracting tasks: {str(e)}",
            "tasks": []
        }


@app.get("/api/documents/recent")
async def api_get_recent_documents(limit: int = 10):
    """Get recently uploaded documents."""
    try:
        logger.info(f"Getting recent documents (limit: {limit})")

        # Mock recent documents
        recent_docs = [
            {
                "id": "1",
                "filename": "project_proposal.pdf",
                "title": "Project Proposal",
                "file_size": 1024000,
                "file_type": "pdf",
                "created_at": "2024-01-15T10:00:00Z"
            },
            {
                "id": "2",
                "filename": "meeting_notes.docx",
                "title": "Meeting Notes",
                "file_size": 512000,
                "file_type": "docx",
                "created_at": "2024-01-14T14:30:00Z"
            }
        ]

        return {
            "status": "success",
            "documents": recent_docs[:limit]
        }

    except Exception as e:
        logger.error(f"Error getting recent documents: {e}")
        return {
            "status": "error",
            "message": f"Error getting recent documents: {str(e)}",
            "documents": []
        }


@app.post("/api/chat")
async def api_chat(message_data: dict):
    """Chat with AI assistant."""
    try:
        message = message_data.get("message", "")
        logger.info(f"Chat message: {message}")

        # Mock AI response
        response = f"I understand you're asking about '{message}'. This is a mock response from the AI assistant. In a full implementation, this would connect to your AI model to provide intelligent responses based on your knowledge base."

        return {
            "status": "success",
            "response": response,
            "sources": [],
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error in chat: {e}")
        return {
            "status": "error",
            "message": f"Chat error: {str(e)}"
        }


@app.get("/api/status")
async def api_status():
    """Get system status."""
    return {
        "status": "running",
        "version": "0.1.0",
        "ollama_host": settings.ollama.host,
        "default_model": settings.ollama.default_model,
        "agents_enabled": {
            "archivist": settings.agents.archivist.enabled,
            "research": settings.agents.research.enabled,
            "task_manager": settings.agents.task_manager.enabled,
            "note_keeper": settings.agents.note_keeper.enabled,
            "project_manager": settings.agents.project_manager.enabled,
            "email_processor": settings.agents.email_processor.enabled,
            "calendar_manager": settings.agents.calendar_manager.enabled,
            "contact_manager": settings.agents.contact_manager.enabled,
        },
        "integrations": {
            "email": {
                "enabled": settings.integrations.email.enabled,
                "providers": {
                    "gmail": settings.integrations.email.providers.gmail.enabled,
                    "outlook": settings.integrations.email.providers.outlook.enabled,
                    "imap": settings.integrations.email.providers.imap.enabled,
                }
            },
            "calendar": {
                "enabled": settings.integrations.calendar.enabled,
                "providers": {
                    "google": settings.integrations.calendar.providers.google.enabled,
                    "outlook": settings.integrations.calendar.providers.outlook.enabled,
                }
            },
            "contacts": {
                "enabled": settings.integrations.contacts.enabled,
                "providers": {
                    "google": settings.integrations.contacts.providers.google.enabled,
                    "outlook": settings.integrations.contacts.providers.outlook.enabled,
                    "csv": settings.integrations.contacts.providers.csv.enabled,
                }
            }
        },
        "learning": {
            "enabled": settings.learning.enabled,
            "models_trained": False,  # TODO: Check actual model status
        }
    }


# Logging API Endpoints
@app.get("/api/logs")
async def api_get_logs(lines: int = 100, level: str = "INFO"):
    """Get recent log entries."""
    try:
        logger.info(f"Getting logs: {lines} lines, level: {level}")

        log_file = Path(settings.logging.file)

        if not log_file.exists():
            return {
                "status": "success",
                "logs": [],
                "message": "Log file not found"
            }

        # Read log file
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()

        # Filter by level if specified
        if level != "ALL":
            filtered_lines = []
            for line in all_lines:
                if f"| {level} " in line or f"|{level}" in line:
                    filtered_lines.append(line)
            all_lines = filtered_lines

        # Get last N lines
        recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

        # Parse log entries
        log_entries = []
        for line in recent_lines:
            line = line.strip()
            if line:
                # Try to parse log line
                parts = line.split(' | ')
                if len(parts) >= 4:
                    log_entry = {
                        "timestamp": parts[0],
                        "level": parts[1].strip(),
                        "location": parts[2],
                        "message": ' | '.join(parts[3:]),
                        "raw": line
                    }
                else:
                    log_entry = {
                        "timestamp": "",
                        "level": "INFO",
                        "location": "",
                        "message": line,
                        "raw": line
                    }
                log_entries.append(log_entry)

        return {
            "status": "success",
            "logs": log_entries,
            "total_lines": len(all_lines),
            "returned_lines": len(log_entries),
            "log_file": str(log_file)
        }

    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        return {
            "status": "error",
            "message": f"Error getting logs: {str(e)}",
            "logs": []
        }


@app.get("/api/logs/download")
async def api_download_logs():
    """Download the complete log file."""
    try:
        logger.info("Log file download requested")

        log_file = Path(settings.logging.file)

        if not log_file.exists():
            raise HTTPException(status_code=404, detail="Log file not found")

        # Return file response
        from fastapi.responses import FileResponse
        return FileResponse(
            path=str(log_file),
            filename=f"assistant_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log",
            media_type="text/plain"
        )

    except Exception as e:
        logger.error(f"Error downloading logs: {e}")
        raise HTTPException(status_code=500, detail=f"Error downloading logs: {str(e)}")


@app.post("/api/logs/clear")
async def api_clear_logs():
    """Clear the log file (keep backup)."""
    try:
        logger.info("Log file clear requested")

        log_file = Path(settings.logging.file)

        if log_file.exists():
            # Create backup
            backup_file = log_file.with_suffix(f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
            log_file.rename(backup_file)
            logger.info(f"Log file backed up to: {backup_file}")

        # Create new empty log file
        log_file.touch()
        logger.info("Log file cleared and recreated")

        return {
            "status": "success",
            "message": "Log file cleared successfully",
            "backup_created": str(backup_file) if log_file.exists() else None
        }

    except Exception as e:
        logger.error(f"Error clearing logs: {e}")
        return {
            "status": "error",
            "message": f"Error clearing logs: {str(e)}"
        }


@app.post("/api/logs/frontend")
async def api_log_frontend_error(log_data: dict):
    """Log frontend errors and events."""
    try:
        # Handle both single log and batch logs
        logs = log_data.get('logs', [])
        if not logs:
            # Single log format
            logs = [log_data]

        session_id = log_data.get('sessionId', 'unknown')
        logged_count = 0

        for log_entry in logs:
            level = log_entry.get('level', 'INFO').upper()
            message = log_entry.get('message', '')
            context = log_entry.get('context', {})
            user_agent = log_entry.get('userAgent', 'unknown')
            url = log_entry.get('url', 'unknown')
            timestamp = log_entry.get('timestamp', datetime.now().isoformat())

            # Format frontend log message
            log_message = f"[FRONTEND:{session_id}] {message}"
            if context:
                log_message += f" | Context: {json.dumps(context)}"
            log_message += f" | URL: {url}"

            # Log with appropriate level
            if level == 'ERROR':
                logger.error(log_message)
            elif level == 'WARNING' or level == 'WARN':
                logger.warning(log_message)
            elif level == 'DEBUG':
                logger.debug(log_message)
            else:
                logger.info(log_message)

            logged_count += 1

        return {
            "status": "success",
            "message": f"Frontend logs recorded: {logged_count} entries"
        }

    except Exception as e:
        logger.error(f"Error logging frontend message: {e}")
        return {
            "status": "error",
            "message": f"Error logging frontend message: {str(e)}"
        }


# Agent Management Endpoints

@app.post("/api/agents/task-manager/process")
async def api_task_manager_process():
    """Process tasks with the task manager agent."""
    try:
        logger.info("Processing tasks with task manager agent")
        result = await task_manager_agent.process_tasks()
        return result
    except Exception as e:
        logger.error(f"Error processing tasks: {e}")
        return {
            "status": "error",
            "message": f"Error processing tasks: {str(e)}"
        }


@app.post("/api/agents/task-manager/prioritize/{task_id}")
async def api_task_manager_prioritize(task_id: str, context: dict = None):
    """Prioritize a specific task."""
    try:
        logger.info(f"Prioritizing task {task_id}")
        result = await task_manager_agent.prioritize_task(task_id, context)
        return result
    except Exception as e:
        logger.error(f"Error prioritizing task {task_id}: {e}")
        return {
            "status": "error",
            "message": f"Error prioritizing task: {str(e)}"
        }


@app.post("/api/agents/contact-manager/process")
async def api_contact_manager_process():
    """Process contacts with the contact manager agent."""
    try:
        logger.info("Processing contacts with contact manager agent")
        result = await contact_manager_agent.process_contacts()
        return result
    except Exception as e:
        logger.error(f"Error processing contacts: {e}")
        return {
            "status": "error",
            "message": f"Error processing contacts: {str(e)}"
        }


@app.get("/api/agents/contact-manager/analyze/{contact_id}")
async def api_contact_manager_analyze(contact_id: str):
    """Analyze contact relationships."""
    try:
        logger.info(f"Analyzing contact relationships for {contact_id}")
        result = await contact_manager_agent.analyze_contact_relationships(contact_id)
        return result
    except Exception as e:
        logger.error(f"Error analyzing contact {contact_id}: {e}")
        return {
            "status": "error",
            "message": f"Error analyzing contact: {str(e)}"
        }


@app.get("/api/agents/contact-manager/merge-suggestions")
async def api_contact_manager_merge_suggestions(threshold: float = 0.8):
    """Get contact merge suggestions."""
    try:
        logger.info("Getting contact merge suggestions")
        suggestions = await contact_manager_agent.suggest_contact_merges(threshold)
        return {
            "status": "success",
            "suggestions": [
                {
                    "primary_contact_id": s.primary_contact_id,
                    "duplicate_contact_id": s.duplicate_contact_id,
                    "similarity_score": s.similarity_score,
                    "merge_confidence": s.merge_confidence,
                    "conflicting_fields": s.conflicting_fields,
                    "suggested_merge_data": s.suggested_merge_data
                }
                for s in suggestions
            ]
        }
    except Exception as e:
        logger.error(f"Error getting merge suggestions: {e}")
        return {
            "status": "error",
            "message": f"Error getting merge suggestions: {str(e)}"
        }


@app.post("/api/agents/note-keeper/process")
async def api_note_keeper_process(notes: List[dict]):
    """Process notes with the note keeper agent."""
    try:
        logger.info(f"Processing {len(notes)} notes with note keeper agent")
        result = await note_keeper_agent.process_notes(notes)
        return result
    except Exception as e:
        logger.error(f"Error processing notes: {e}")
        return {
            "status": "error",
            "message": f"Error processing notes: {str(e)}"
        }


@app.get("/api/agents/note-keeper/analyze/{note_id}")
async def api_note_keeper_analyze(note_id: str, notes: List[dict]):
    """Analyze a specific note."""
    try:
        logger.info(f"Analyzing note {note_id}")

        # Find the note
        note = next((n for n in notes if n.get('id') == note_id), None)
        if not note:
            return {"status": "error", "message": "Note not found"}

        result = await note_keeper_agent.analyze_note_content(note)
        return result
    except Exception as e:
        logger.error(f"Error analyzing note {note_id}: {e}")
        return {
            "status": "error",
            "message": f"Error analyzing note: {str(e)}"
        }


@app.post("/api/agents/project-manager/process")
async def api_project_manager_process(projects: List[dict]):
    """Process projects with the project manager agent."""
    try:
        logger.info(f"Processing {len(projects)} projects with project manager agent")
        result = await project_manager_agent.process_projects(projects)
        return result
    except Exception as e:
        logger.error(f"Error processing projects: {e}")
        return {
            "status": "error",
            "message": f"Error processing projects: {str(e)}"
        }


@app.get("/api/agents/project-manager/health/{project_id}")
async def api_project_manager_health(project_id: str, project_data: dict):
    """Analyze project health."""
    try:
        logger.info(f"Analyzing health for project {project_id}")
        result = await project_manager_agent.analyze_project_health(project_id, project_data)
        return result
    except Exception as e:
        logger.error(f"Error analyzing project health {project_id}: {e}")
        return {
            "status": "error",
            "message": f"Error analyzing project health: {str(e)}"
        }


@app.post("/api/agents/research/analyze")
async def api_research_analyze(content_items: List[dict]):
    """Analyze content for research insights."""
    try:
        logger.info(f"Analyzing {len(content_items)} content items for research")
        result = await research_agent.analyze_content_for_research(content_items)
        return result
    except Exception as e:
        logger.error(f"Error analyzing content for research: {e}")
        return {
            "status": "error",
            "message": f"Error analyzing content for research: {str(e)}"
        }


@app.post("/api/agents/research/extract-insights")
async def api_research_extract_insights(content: str, source: str = "unknown"):
    """Extract research insights from content."""
    try:
        logger.info(f"Extracting research insights from content")
        insights = await research_agent.extract_research_insights(content, source)
        return {
            "status": "success",
            "insights": [
                {
                    "topic": i.topic,
                    "insight": i.insight,
                    "confidence": i.confidence,
                    "sources": i.sources,
                    "evidence": i.evidence,
                    "created_at": i.created_at.isoformat()
                }
                for i in insights
            ]
        }
    except Exception as e:
        logger.error(f"Error extracting research insights: {e}")
        return {
            "status": "error",
            "message": f"Error extracting research insights: {str(e)}"
        }


@app.post("/api/agents/archivist/process")
async def api_archivist_process(content_items: List[dict]):
    """Process content for archiving."""
    try:
        logger.info(f"Processing {len(content_items)} content items for archiving")
        result = await archivist_agent.process_content_for_archiving(content_items)
        return result
    except Exception as e:
        logger.error(f"Error processing content for archiving: {e}")
        return {
            "status": "error",
            "message": f"Error processing content for archiving: {str(e)}"
        }


@app.post("/api/agents/archivist/archive")
async def api_archivist_archive(item: dict, reason: str = "manual"):
    """Archive a specific item."""
    try:
        logger.info(f"Archiving item {item.get('id')}")
        result = await archivist_agent.archive_item(item, reason)
        return result
    except Exception as e:
        logger.error(f"Error archiving item: {e}")
        return {
            "status": "error",
            "message": f"Error archiving item: {str(e)}"
        }


@app.get("/api/agents/archivist/search")
async def api_archivist_search(query: str, content_type: str = None, limit: int = 50):
    """Search archived content."""
    try:
        logger.info(f"Searching archive for: {query}")
        results = await archivist_agent.search_archive(query, content_type, limit)
        return {
            "status": "success",
            "results": results,
            "query": query,
            "total_results": len(results)
        }
    except Exception as e:
        logger.error(f"Error searching archive: {e}")
        return {
            "status": "error",
            "message": f"Error searching archive: {str(e)}"
        }


@app.get("/api/agents/archivist/stats")
async def api_archivist_stats():
    """Get archive statistics."""
    try:
        logger.info("Getting archive statistics")
        stats = await archivist_agent.get_archive_stats()
        return {
            "status": "success",
            "stats": {
                "total_items": stats.total_items,
                "items_by_type": stats.items_by_type,
                "storage_size_mb": stats.storage_size_mb,
                "oldest_item_date": stats.oldest_item_date.isoformat() if stats.oldest_item_date else None,
                "newest_item_date": stats.newest_item_date.isoformat() if stats.newest_item_date else None,
                "duplicates_found": stats.duplicates_found,
                "compression_ratio": stats.compression_ratio
            }
        }
    except Exception as e:
        logger.error(f"Error getting archive stats: {e}")
        return {
            "status": "error",
            "message": f"Error getting archive stats: {str(e)}"
        }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.api.web_app:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=True
    )
