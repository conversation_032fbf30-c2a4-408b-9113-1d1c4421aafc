{% extends "base.html" %}

{% block content %}
<div class="projects-page">
    <header class="page-header">
        <h1>Projects</h1>
        <p class="page-subtitle">Manage your projects and track progress</p>
    </header>

    <!-- Quick Actions -->
    <section class="quick-actions">
        <div class="action-grid">
            <button class="action-card" id="newProjectBtn">
                <div class="action-icon">📁</div>
                <h3>New Project</h3>
                <p>Create a new project</p>
            </button>
            
            <button class="action-card" id="templatesBtn">
                <div class="action-icon">📋</div>
                <h3>Templates</h3>
                <p>Use project templates</p>
            </button>
            
            <button class="action-card" id="archiveBtn">
                <div class="action-icon">📦</div>
                <h3>Archive</h3>
                <p>View archived projects</p>
            </button>
        </div>
    </section>

    <!-- Project Stats -->
    <section class="project-stats">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                    <h3>Active Projects</h3>
                    <div class="stat-number" id="activeProjects">-</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <h3>Completed</h3>
                    <div class="stat-number" id="completedProjects">-</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">⏰</div>
                <div class="stat-content">
                    <h3>Due Soon</h3>
                    <div class="stat-number" id="dueSoonProjects">-</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects List -->
    <section class="projects-list">
        <div class="list-header">
            <h2>Your Projects</h2>
            <div class="list-controls">
                <select id="statusFilter">
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="completed">Completed</option>
                    <option value="on_hold">On Hold</option>
                </select>
                
                <select id="sortBy">
                    <option value="updated">Last Updated</option>
                    <option value="created">Date Created</option>
                    <option value="due_date">Due Date</option>
                    <option value="name">Name</option>
                </select>
            </div>
        </div>
        
        <div class="projects-grid" id="projectsGrid">
            <div class="loading-placeholder">Loading projects...</div>
        </div>
    </section>
</div>

<!-- Project Modal -->
<div class="modal" id="projectModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">New Project</h2>
            <button class="modal-close" id="closeModal">×</button>
        </div>
        
        <div class="modal-body">
            <div class="form-group">
                <label for="projectName">Project Name</label>
                <input type="text" id="projectName" placeholder="Enter project name">
            </div>
            
            <div class="form-group">
                <label for="projectDescription">Description</label>
                <textarea id="projectDescription" rows="3" placeholder="Project description"></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="projectStatus">Status</label>
                    <select id="projectStatus">
                        <option value="active">Active</option>
                        <option value="on_hold">On Hold</option>
                        <option value="completed">Completed</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="projectDueDate">Due Date</label>
                    <input type="date" id="projectDueDate">
                </div>
            </div>
            
            <div class="form-group">
                <label for="projectTags">Tags (comma-separated)</label>
                <input type="text" id="projectTags" placeholder="work, client, urgent">
            </div>
        </div>
        
        <div class="modal-footer">
            <button class="btn btn-primary" id="saveProjectBtn">Save Project</button>
            <button class="btn btn-secondary" id="cancelProjectBtn">Cancel</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.projects-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.stat-icon {
    font-size: var(--font-size-xxl);
}

.stat-content h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.stat-number {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--accent-primary);
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.list-controls {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.list-controls select {
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-lg);
}

.project-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all 0.2s ease;
}

.project-card:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 2px 8px var(--shadow);
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.project-name {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    font-size: var(--font-size-lg);
}

.project-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background-color: #28a745;
    color: white;
}

.status-on_hold {
    background-color: #ffc107;
    color: #000;
}

.status-completed {
    background-color: #007bff;
    color: white;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
}

.project-progress {
    margin-bottom: var(--spacing-sm);
}

.progress-label {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background-color: var(--bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--accent-primary);
    transition: width 0.3s ease;
}

.project-actions {
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.project-card:hover .project-actions {
    opacity: 1;
}

.project-action {
    padding: var(--spacing-xs);
    border: none;
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: all 0.2s ease;
}

.project-action:hover {
    background-color: var(--accent-primary);
    color: white;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: var(--text-secondary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.form-group input, .form-group textarea, .form-group select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

.loading-placeholder, .no-projects {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
    grid-column: 1 / -1;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class ProjectsManager {
    constructor() {
        this.currentProject = null;
        this.projects = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadProjects();
        this.loadProjectStats();
    }

    setupEventListeners() {
        // Quick actions
        document.getElementById('newProjectBtn').addEventListener('click', () => this.showProjectModal());
        document.getElementById('templatesBtn').addEventListener('click', () => this.showTemplates());
        document.getElementById('archiveBtn').addEventListener('click', () => this.showArchive());

        // Modal actions
        document.getElementById('closeModal').addEventListener('click', () => this.hideProjectModal());
        document.getElementById('saveProjectBtn').addEventListener('click', () => this.saveProject());
        document.getElementById('cancelProjectBtn').addEventListener('click', () => this.hideProjectModal());

        // Filters
        document.getElementById('statusFilter').addEventListener('change', () => this.loadProjects());
        document.getElementById('sortBy').addEventListener('change', () => this.loadProjects());

        // Close modal on outside click
        document.getElementById('projectModal').addEventListener('click', (e) => {
            if (e.target.id === 'projectModal') {
                this.hideProjectModal();
            }
        });
    }

    async loadProjects() {
        const grid = document.getElementById('projectsGrid');
        grid.innerHTML = '<div class="loading-placeholder">Loading projects...</div>';

        try {
            const status = document.getElementById('statusFilter').value;
            const sort = document.getElementById('sortBy').value;
            
            const response = await fetch(`/api/projects?status=${status}&sort=${sort}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.projects = data.projects || [];
                this.renderProjects();
            } else {
                grid.innerHTML = '<div class="no-projects">Error loading projects</div>';
            }
        } catch (error) {
            console.error('Error loading projects:', error);
            grid.innerHTML = '<div class="no-projects">Error loading projects</div>';
        }
    }

    async loadProjectStats() {
        try {
            const response = await fetch('/api/projects/stats');
            const data = await response.json();

            if (data.status === 'success') {
                const stats = data.stats;
                document.getElementById('activeProjects').textContent = stats.active || 0;
                document.getElementById('completedProjects').textContent = stats.completed || 0;
                document.getElementById('dueSoonProjects').textContent = stats.due_soon || 0;
            }
        } catch (error) {
            console.error('Error loading project stats:', error);
        }
    }

    renderProjects() {
        const grid = document.getElementById('projectsGrid');

        if (this.projects.length === 0) {
            grid.innerHTML = '<div class="no-projects">No projects found. Create your first project!</div>';
            return;
        }

        const projectsHtml = this.projects.map(project => {
            const dueDate = project.due_date ? new Date(project.due_date).toLocaleDateString() : 'No due date';
            const updatedDate = new Date(project.updated_at).toLocaleDateString();
            const progress = project.progress || 0;
            
            return `
                <div class="project-card" onclick="projectsManager.viewProject('${project.id}')">
                    <div class="project-header">
                        <h3 class="project-name">${project.name || 'Untitled Project'}</h3>
                        <span class="project-status status-${project.status}">${project.status.replace('_', ' ')}</span>
                    </div>
                    
                    ${project.description ? `<div class="project-description">${project.description}</div>` : ''}
                    
                    <div class="project-meta">
                        <span>Due: ${dueDate}</span>
                        <span>Updated: ${updatedDate}</span>
                    </div>
                    
                    <div class="project-progress">
                        <div class="progress-label">
                            <span>Progress</span>
                            <span>${progress}%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progress}%"></div>
                        </div>
                    </div>
                    
                    <div class="project-actions">
                        <button class="project-action" onclick="event.stopPropagation(); projectsManager.editProject('${project.id}')">
                            ✏️
                        </button>
                        <button class="project-action" onclick="event.stopPropagation(); projectsManager.duplicateProject('${project.id}')">
                            📋
                        </button>
                        <button class="project-action" onclick="event.stopPropagation(); projectsManager.deleteProject('${project.id}')">
                            🗑️
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        grid.innerHTML = projectsHtml;
    }

    showProjectModal(project = null) {
        this.currentProject = project;
        
        if (project) {
            document.getElementById('modalTitle').textContent = 'Edit Project';
            document.getElementById('projectName').value = project.name || '';
            document.getElementById('projectDescription').value = project.description || '';
            document.getElementById('projectStatus').value = project.status || 'active';
            document.getElementById('projectDueDate').value = project.due_date || '';
            document.getElementById('projectTags').value = project.tags ? project.tags.join(', ') : '';
        } else {
            document.getElementById('modalTitle').textContent = 'New Project';
            document.getElementById('projectName').value = '';
            document.getElementById('projectDescription').value = '';
            document.getElementById('projectStatus').value = 'active';
            document.getElementById('projectDueDate').value = '';
            document.getElementById('projectTags').value = '';
        }
        
        document.getElementById('projectModal').style.display = 'flex';
        document.getElementById('projectName').focus();
    }

    hideProjectModal() {
        document.getElementById('projectModal').style.display = 'none';
        this.currentProject = null;
    }

    async saveProject() {
        const projectData = {
            name: document.getElementById('projectName').value.trim(),
            description: document.getElementById('projectDescription').value.trim(),
            status: document.getElementById('projectStatus').value,
            due_date: document.getElementById('projectDueDate').value,
            tags: document.getElementById('projectTags').value.split(',').map(tag => tag.trim()).filter(tag => tag)
        };

        if (!projectData.name) {
            app.showNotification('Project name is required', 'error');
            return;
        }

        try {
            let response;
            if (this.currentProject) {
                response = await fetch(`/api/projects/${this.currentProject.id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(projectData)
                });
            } else {
                response = await fetch('/api/projects', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(projectData)
                });
            }

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Project saved successfully!', 'success');
                this.hideProjectModal();
                await this.loadProjects();
                await this.loadProjectStats();
            } else {
                app.showNotification(`Error saving project: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error saving project: ${error.message}`, 'error');
        }
    }

    async editProject(projectId) {
        try {
            const response = await fetch(`/api/projects/${projectId}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.showProjectModal(data.project);
            }
        } catch (error) {
            app.showNotification('Error loading project', 'error');
        }
    }

    async deleteProject(projectId) {
        if (!confirm('Are you sure you want to delete this project?')) return;

        try {
            const response = await fetch(`/api/projects/${projectId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Project deleted successfully!', 'success');
                await this.loadProjects();
                await this.loadProjectStats();
            } else {
                app.showNotification(`Error deleting project: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error deleting project: ${error.message}`, 'error');
        }
    }

    async duplicateProject(projectId) {
        try {
            const response = await fetch(`/api/projects/${projectId}/duplicate`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Project duplicated successfully!', 'success');
                await this.loadProjects();
            } else {
                app.showNotification(`Error duplicating project: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error duplicating project: ${error.message}`, 'error');
        }
    }

    viewProject(projectId) {
        // This would open a detailed project view
        app.showNotification('Project details view coming soon!', 'info');
    }

    showTemplates() {
        app.showNotification('Project templates coming soon!', 'info');
    }

    showArchive() {
        document.getElementById('statusFilter').value = 'completed';
        this.loadProjects();
    }
}

// Initialize projects manager
let projectsManager;
document.addEventListener('DOMContentLoaded', () => {
    projectsManager = new ProjectsManager();
});
</script>
{% endblock %}
