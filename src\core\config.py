"""Configuration management for the Personal Assistant."""

from pathlib import Path
from typing import Dict, List, Optional

import yaml
from pydantic import BaseModel, Field


class OllamaConfig(BaseModel):
    """Ollama service configuration."""
    host: str = "http://localhost:11434"
    default_model: str = "deepseek-r1:latest"
    reasoning_model: str = "deepseek-r1:latest"
    fast_model: str = "deepseek-r1:latest"
    timeout: int = 60
    max_retries: int = 3


class VectorDBConfig(BaseModel):
    """Vector database configuration."""
    type: str = "chromadb"
    path: str = "./data/database/vector_db"
    collection_name: str = "knowledge_base"


class MetadataDBConfig(BaseModel):
    """Metadata database configuration."""
    type: str = "sqlite"
    path: str = "./data/database/metadata.db"


class EmbeddingConfig(BaseModel):
    """Embedding model configuration."""
    model: str = "sentence-transformers/all-MiniLM-L6-v2"
    chunk_size: int = 512
    chunk_overlap: int = 50


class KnowledgeConfig(BaseModel):
    """Knowledge storage configuration."""
    vector_db: VectorDBConfig = Field(default_factory=VectorDBConfig)
    metadata_db: MetadataDBConfig = Field(default_factory=MetadataDBConfig)
    embedding: EmbeddingConfig = Field(default_factory=EmbeddingConfig)


class ImageProcessingConfig(BaseModel):
    """Image processing configuration."""
    enabled: bool = True
    extract_text: bool = True
    generate_descriptions: bool = True


class URLProcessingConfig(BaseModel):
    """URL processing configuration."""
    enabled: bool = True
    fetch_content: bool = True
    extract_metadata: bool = True
    screenshot: bool = False


class ProcessingConfig(BaseModel):
    """Document processing configuration."""
    supported_formats: List[str] = Field(
        default_factory=lambda: [
            "pdf", "txt", "md", "rst", "html",
            "jpg", "jpeg", "png", "gif", "webp", "url"
        ]
    )
    max_file_size_mb: int = 100
    ocr_enabled: bool = True
    ocr_engine: str = "tesseract"
    image_processing: ImageProcessingConfig = Field(default_factory=ImageProcessingConfig)
    url_processing: URLProcessingConfig = Field(default_factory=URLProcessingConfig)


class AgentConfig(BaseModel):
    """Individual agent configuration."""
    enabled: bool = True
    model: str = "llama3.1:8b"
    temperature: float = 0.7
    max_tokens: int = 2000
    task_creation: bool = True
    contact_extraction: bool = True
    priority_learning: bool = True
    sentiment_analysis: bool = True
    email_drafting: bool = True
    writing_style_learning: bool = True
    learning_enabled: bool = True


class AgentsConfig(BaseModel):
    """All agents configuration."""
    archivist: AgentConfig = Field(default_factory=AgentConfig)
    research: AgentConfig = Field(default_factory=AgentConfig)
    task_manager: AgentConfig = Field(default_factory=AgentConfig)
    note_keeper: AgentConfig = Field(default_factory=AgentConfig)
    project_manager: AgentConfig = Field(default_factory=AgentConfig)
    email_processor: AgentConfig = Field(default_factory=AgentConfig)
    email_drafter: AgentConfig = Field(default_factory=AgentConfig)
    calendar_manager: AgentConfig = Field(default_factory=AgentConfig)
    contact_manager: AgentConfig = Field(default_factory=AgentConfig)


class APIConfig(BaseModel):
    """API configuration."""
    host: str = "localhost"
    port: int = 8000
    enable_web_ui: bool = True
    web_ui_title: str = "Assistant - Knowledge Library"


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = "INFO"
    file: str = "./data/logs/assistant.log"
    max_size_mb: int = 10
    backup_count: int = 5


class DataConfig(BaseModel):
    """Data directories configuration."""
    documents: str = "./data/documents"
    database: str = "./data/database"
    models: str = "./data/models"
    logs: str = "./data/logs"
    tokens: str = "./data/tokens"
    contacts: str = "./data/contacts"
    email_cache: str = "./data/email"
    calendar_cache: str = "./data/calendar"


# Integration Configuration Classes
class GmailAccountConfig(BaseModel):
    """Gmail account configuration."""
    credentials_file: str
    token_file: str
    context: str


class GmailAccountsConfig(BaseModel):
    """Gmail accounts configuration."""
    personal: Optional[GmailAccountConfig] = None
    work: Optional[GmailAccountConfig] = None


class GmailProviderConfig(BaseModel):
    """Gmail provider configuration."""
    enabled: bool = True
    accounts: GmailAccountsConfig = Field(default_factory=GmailAccountsConfig)


class ProtonProviderConfig(BaseModel):
    """Proton provider configuration."""
    enabled: bool = True
    host: str = "127.0.0.1"
    port: int = 1143
    username: str = ""
    password_file: str = "./config/proton_password.txt"
    context: str = "personal"


class OutlookProviderConfig(BaseModel):
    """Outlook provider configuration."""
    enabled: bool = False


class IMAPProviderConfig(BaseModel):
    """IMAP provider configuration."""
    enabled: bool = False


class EmailProvidersConfig(BaseModel):
    """Email providers configuration."""
    gmail: GmailProviderConfig = Field(default_factory=GmailProviderConfig)
    proton: ProtonProviderConfig = Field(default_factory=ProtonProviderConfig)
    outlook: OutlookProviderConfig = Field(default_factory=OutlookProviderConfig)
    imap: IMAPProviderConfig = Field(default_factory=IMAPProviderConfig)


class EmailProcessingConfig(BaseModel):
    """Email processing configuration."""
    batch_size: int = 100
    check_interval_minutes: int = 60
    max_age_days: int = 90
    folders_to_monitor: List[str] = Field(default_factory=lambda: ["INBOX"])
    skip_folders: List[str] = Field(default_factory=lambda: ["Spam", "Trash", "Drafts", "Archive", "All Mail"])
    auto_create_tasks: bool = True
    task_keywords: List[str] = Field(default_factory=lambda: ["todo", "action", "deadline", "follow up", "reminder", "please", "can you", "need", "asap", "urgent"])
    auto_tag: bool = True
    importance_tags: List[str] = Field(default_factory=lambda: ["high", "medium", "low"])
    urgency_tags: List[str] = Field(default_factory=lambda: ["urgent", "normal", "low"])
    category_tags: List[str] = Field(default_factory=lambda: ["work", "personal", "marketing", "newsletter", "social"])
    create_tasks_for_received: bool = True
    create_tasks_for_sent: bool = False
    duplicate_detection: bool = True
    context_similarity_threshold: float = 0.8
    auto_unsubscribe: bool = True
    marketing_keywords: List[str] = Field(default_factory=lambda: ["unsubscribe", "newsletter", "promotion", "deal", "sale", "offer"])
    marketing_confidence_threshold: float = 0.9


class EmailIntegrationConfig(BaseModel):
    """Email integration configuration."""
    enabled: bool = True
    providers: EmailProvidersConfig = Field(default_factory=EmailProvidersConfig)
    processing: EmailProcessingConfig = Field(default_factory=EmailProcessingConfig)


class GoogleCalendarConfig(BaseModel):
    """Google Calendar configuration."""
    enabled: bool = True
    credentials_file: str = "./config/google_calendar_credentials.json"
    token_file: str = "./data/tokens/google_calendar_token.json"
    calendars: Dict[str, str] = Field(default_factory=lambda: {"personal": "primary", "work": "", "car": ""})


class OutlookCalendarConfig(BaseModel):
    """Outlook Calendar configuration."""
    enabled: bool = False


class CalendarProvidersConfig(BaseModel):
    """Calendar providers configuration."""
    google: GoogleCalendarConfig = Field(default_factory=GoogleCalendarConfig)
    outlook: OutlookCalendarConfig = Field(default_factory=OutlookCalendarConfig)


class WorkingHoursConfig(BaseModel):
    """Working hours configuration."""
    start: str = "09:00"
    end: str = "17:00"
    timezone: str = "America/New_York"


class CalendarSyncConfig(BaseModel):
    """Calendar sync configuration."""
    check_interval_minutes: int = 30
    conflict_resolution: str = "notify"
    notification_method: str = "web_ui"
    availability_buffer_minutes: int = 15
    working_hours: WorkingHoursConfig = Field(default_factory=WorkingHoursConfig)
    separate_but_coordinated: bool = True
    cross_calendar_availability: bool = True
    meeting_optimization: bool = True


class CalendarIntegrationConfig(BaseModel):
    """Calendar integration configuration."""
    enabled: bool = True
    providers: CalendarProvidersConfig = Field(default_factory=CalendarProvidersConfig)
    sync: CalendarSyncConfig = Field(default_factory=CalendarSyncConfig)


class GoogleContactsConfig(BaseModel):
    """Google Contacts configuration."""
    enabled: bool = True
    credentials_file: str = "./config/google_contacts_credentials.json"
    token_file: str = "./data/tokens/google_contacts_token.json"
    contexts: List[str] = Field(default_factory=lambda: ["personal", "work"])


class AppfolioIntegrationConfig(BaseModel):
    """Appfolio integration configuration."""
    enabled: bool = True
    email_pattern: str = "*appfolio*"
    context: str = "work"


class CSVProviderConfig(BaseModel):
    """CSV provider configuration."""
    enabled: bool = True
    import_directory: str = "./data/contacts/csv"
    auto_import: bool = True
    appfolio_integration: AppfolioIntegrationConfig = Field(default_factory=AppfolioIntegrationConfig)


class OutlookContactsConfig(BaseModel):
    """Outlook Contacts configuration."""
    enabled: bool = False


class ContactProvidersConfig(BaseModel):
    """Contact providers configuration."""
    google: GoogleContactsConfig = Field(default_factory=GoogleContactsConfig)
    outlook: OutlookContactsConfig = Field(default_factory=OutlookContactsConfig)
    csv: CSVProviderConfig = Field(default_factory=CSVProviderConfig)


class ContactManagementConfig(BaseModel):
    """Contact management configuration."""
    auto_add_from_email: bool = True
    separate_work_personal: bool = True
    duplicate_detection: bool = True
    merge_strategy: str = "context_aware"
    backup_before_changes: bool = True
    work_email_domains: List[str] = Field(default_factory=list)
    personal_indicators: List[str] = Field(default_factory=lambda: ["gmail.com", "yahoo.com", "hotmail.com"])


class ContactsIntegrationConfig(BaseModel):
    """Contacts integration configuration."""
    enabled: bool = True
    providers: ContactProvidersConfig = Field(default_factory=ContactProvidersConfig)
    management: ContactManagementConfig = Field(default_factory=ContactManagementConfig)


class IntegrationsConfig(BaseModel):
    """All integrations configuration."""
    email: EmailIntegrationConfig = Field(default_factory=EmailIntegrationConfig)
    calendar: CalendarIntegrationConfig = Field(default_factory=CalendarIntegrationConfig)
    contacts: ContactsIntegrationConfig = Field(default_factory=ContactsIntegrationConfig)


class LearningModelsConfig(BaseModel):
    """Learning models configuration."""
    task_priority: str = "./data/models/task_priority_model.pkl"
    email_importance: str = "./data/models/email_importance_model.pkl"
    contact_relevance: str = "./data/models/contact_relevance_model.pkl"
    writing_style: str = "./data/models/writing_style_model.pkl"
    task_grouping: str = "./data/models/task_grouping_model.pkl"


class LearningTrainingConfig(BaseModel):
    """Learning training configuration."""
    min_samples: int = 10
    retrain_interval_days: int = 7
    backup_models: bool = True


class ChangeTrackingConfig(BaseModel):
    """Change tracking configuration."""
    enabled: bool = True
    track_all_modifications: bool = True
    log_user_actions: bool = True
    metadata_change_weight: float = 1.0


class LearningConfig(BaseModel):
    """Learning and adaptation configuration."""
    enabled: bool = True
    feedback_sources: List[str] = Field(default_factory=lambda: [
        "task_modifications", "task_deletions", "priority_changes",
        "metadata_edits", "tag_changes", "manual_ratings", "user_actions"
    ])
    models: LearningModelsConfig = Field(default_factory=LearningModelsConfig)
    training: LearningTrainingConfig = Field(default_factory=LearningTrainingConfig)
    change_tracking: ChangeTrackingConfig = Field(default_factory=ChangeTrackingConfig)


class ContextSeparationConfig(BaseModel):
    """Context separation configuration."""
    enabled: bool = True
    strict_separation: bool = True
    cross_context_learning: bool = False


class PrivacyConfig(BaseModel):
    """Privacy and security configuration."""
    email_content_retention_days: int = 365
    exclude_keywords: List[str] = Field(default_factory=list)
    anonymize_contacts: bool = False
    encrypt_credentials: bool = True
    context_separation: ContextSeparationConfig = Field(default_factory=ContextSeparationConfig)


class Settings(BaseModel):
    """Main configuration class."""
    ollama: OllamaConfig = Field(default_factory=OllamaConfig)
    knowledge: KnowledgeConfig = Field(default_factory=KnowledgeConfig)
    processing: ProcessingConfig = Field(default_factory=ProcessingConfig)
    integrations: IntegrationsConfig = Field(default_factory=IntegrationsConfig)
    agents: AgentsConfig = Field(default_factory=AgentsConfig)
    api: APIConfig = Field(default_factory=APIConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    learning: LearningConfig = Field(default_factory=LearningConfig)
    privacy: PrivacyConfig = Field(default_factory=PrivacyConfig)
    data: DataConfig = Field(default_factory=DataConfig)

    @classmethod
    def load_from_file(cls, config_path: Optional[str] = None) -> "Settings":
        """Load settings from YAML file."""
        if config_path is None:
            config_path = "config/settings.yaml"
        
        config_file = Path(config_path)
        if not config_file.exists():
            # Return default settings if config file doesn't exist
            return cls()
        
        with open(config_file, "r") as f:
            config_data = yaml.safe_load(f)
        
        return cls(**config_data)

    def ensure_directories(self) -> None:
        """Ensure all required directories exist."""
        directories = [
            self.data.documents,
            self.data.database,
            self.data.models,
            self.data.logs,
            Path(self.knowledge.vector_db.path).parent,
            Path(self.knowledge.metadata_db.path).parent,
            Path(self.logging.file).parent,
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings.load_from_file()
