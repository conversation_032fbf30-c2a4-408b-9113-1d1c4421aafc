"""Email Response Generator - Drafts professional email responses."""

import re
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from ..core.logging import logger
from .email_intelligence_service import EmailAnalysis, ResponseDraft, EmailAction


class EmailResponseGenerator:
    """Generates professional email responses based on user decisions."""
    
    def __init__(self):
        self.logger = logger
        
        # Response templates by category
        self.templates = {
            'acknowledge': {
                'professional': "Thank you for your email regarding {subject}. I have received your message and will {action} {timeframe}.",
                'friendly': "Thanks for reaching out about {subject}! I'll {action} and get back to you {timeframe}.",
                'formal': "I acknowledge receipt of your email dated {date} regarding {subject}. I will {action} {timeframe}."
            },
            
            'meeting_accept': {
                'professional': "I would be happy to meet to discuss {subject}. Let me check my calendar and propose some available times.",
                'friendly': "I'd love to meet about {subject}! Let me look at my calendar and send you some options.",
                'formal': "I accept your meeting request regarding {subject}. I will review my schedule and provide available time slots."
            },
            
            'meeting_decline': {
                'professional': "Unfortunately, I'm not available for a meeting at this time. Could we {alternative}?",
                'friendly': "I wish I could meet, but my schedule is pretty packed right now. How about we {alternative}?",
                'formal': "I regret that I am unable to accommodate a meeting at this time. I propose we {alternative}."
            },
            
            'information_request': {
                'professional': "I'll gather the information you requested regarding {subject} and send it over {timeframe}.",
                'friendly': "I'll get that information for you about {subject} and send it your way {timeframe}!",
                'formal': "I will compile the requested information concerning {subject} and provide it {timeframe}."
            },
            
            'approval': {
                'professional': "I have reviewed your request for {subject} and {decision}. {details}",
                'friendly': "I've looked over your request about {subject} and {decision}. {details}",
                'formal': "Upon review of your request regarding {subject}, I {decision}. {details}"
            },
            
            'clarification': {
                'professional': "Thank you for your email. Could you please clarify {question} so I can better assist you?",
                'friendly': "Thanks for reaching out! I just need to clarify {question} to make sure I help you properly.",
                'formal': "I require clarification regarding {question} in order to provide an appropriate response."
            },
            
            'delegation': {
                'professional': "I'm forwarding your request to {person} who can better assist you with {subject}.",
                'friendly': "I'm connecting you with {person} who's the expert on {subject} and can help you out!",
                'formal': "Your inquiry has been forwarded to {person} who has the appropriate expertise regarding {subject}."
            },
            
            'auto_reply': {
                'professional': "Thank you for your email. I'm currently {status} and will respond {timeframe}. For urgent matters, please {contact}.",
                'friendly': "Thanks for your message! I'm {status} right now but will get back to you {timeframe}. If it's urgent, {contact}.",
                'formal': "This is an automated response. I am currently {status} and will respond to your message {timeframe}. For immediate assistance, please {contact}."
            }
        }
        
        # Common alternatives for meeting declines
        self.meeting_alternatives = [
            "handle this via email",
            "schedule a brief phone call",
            "connect next week when my schedule opens up",
            "discuss this in our next regular meeting"
        ]
        
        # Professional closings
        self.closings = {
            'professional': ["Best regards", "Kind regards", "Sincerely"],
            'friendly': ["Best", "Thanks", "Cheers"],
            'formal': ["Respectfully", "Sincerely", "Yours truly"]
        }
    
    async def generate_response(
        self, 
        email_analysis: EmailAnalysis,
        user_decision: str,
        user_context: Optional[Dict[str, Any]] = None
    ) -> ResponseDraft:
        """Generate a professional email response based on user decision."""
        try:
            # Parse user decision
            decision_type, tone, details = self._parse_user_decision(user_decision)
            
            # Get original email context (would come from database)
            original_subject = user_context.get('subject', '') if user_context else ''
            original_sender = user_context.get('sender', '') if user_context else ''
            
            # Generate response based on decision type
            if decision_type == 'acknowledge':
                response = await self._generate_acknowledgment(email_analysis, tone, details, original_subject)
            elif decision_type == 'meeting_accept':
                response = await self._generate_meeting_acceptance(email_analysis, tone, details, original_subject)
            elif decision_type == 'meeting_decline':
                response = await self._generate_meeting_decline(email_analysis, tone, details, original_subject)
            elif decision_type == 'provide_info':
                response = await self._generate_information_response(email_analysis, tone, details, original_subject)
            elif decision_type == 'approve':
                response = await self._generate_approval_response(email_analysis, tone, details, original_subject)
            elif decision_type == 'clarify':
                response = await self._generate_clarification_request(email_analysis, tone, details, original_subject)
            elif decision_type == 'delegate':
                response = await self._generate_delegation_response(email_analysis, tone, details, original_subject)
            elif decision_type == 'custom':
                response = await self._generate_custom_response(email_analysis, tone, details, original_subject)
            else:
                response = await self._generate_default_response(email_analysis, tone, details, original_subject)
            
            # Calculate confidence and review requirement
            confidence = self._calculate_response_confidence(decision_type, email_analysis)
            requires_review = confidence < 0.8 or email_analysis.priority.value in ['critical', 'high']
            
            return ResponseDraft(
                original_email_id=email_analysis.email_id,
                subject=self._generate_subject(original_subject, decision_type),
                body=response,
                tone=tone,
                confidence=confidence,
                reasoning=f"Generated {decision_type} response in {tone} tone",
                requires_review=requires_review
            )
            
        except Exception as e:
            self.logger.error(f"Error generating email response: {e}")
            return self._generate_fallback_response(email_analysis, user_decision)
    
    def _parse_user_decision(self, user_decision: str) -> tuple[str, str, Dict[str, Any]]:
        """Parse user decision into type, tone, and details."""
        decision_lower = user_decision.lower()
        
        # Determine tone
        if 'formal' in decision_lower:
            tone = 'formal'
        elif 'friendly' in decision_lower or 'casual' in decision_lower:
            tone = 'friendly'
        else:
            tone = 'professional'  # Default
        
        # Determine decision type
        if any(word in decision_lower for word in ['acknowledge', 'confirm', 'received']):
            decision_type = 'acknowledge'
        elif any(word in decision_lower for word in ['accept meeting', 'schedule', 'meet']):
            decision_type = 'meeting_accept'
        elif any(word in decision_lower for word in ['decline meeting', 'can\'t meet', 'not available']):
            decision_type = 'meeting_decline'
        elif any(word in decision_lower for word in ['provide info', 'send info', 'information']):
            decision_type = 'provide_info'
        elif any(word in decision_lower for word in ['approve', 'accept', 'agree']):
            decision_type = 'approve'
        elif any(word in decision_lower for word in ['clarify', 'question', 'unclear']):
            decision_type = 'clarify'
        elif any(word in decision_lower for word in ['delegate', 'forward', 'refer']):
            decision_type = 'delegate'
        elif 'custom:' in decision_lower:
            decision_type = 'custom'
        else:
            decision_type = 'acknowledge'  # Default
        
        # Extract details
        details = {'raw_decision': user_decision}
        
        # Extract specific details based on decision type
        if decision_type == 'custom':
            custom_text = user_decision.split('custom:', 1)[1].strip() if 'custom:' in user_decision else ''
            details['custom_text'] = custom_text
        elif decision_type == 'delegate':
            # Try to extract person name
            person_match = re.search(r'to (\w+)', decision_lower)
            details['delegate_to'] = person_match.group(1) if person_match else 'the appropriate team member'
        elif decision_type == 'meeting_decline':
            # Try to extract alternative
            alt_match = re.search(r'instead (.+)', decision_lower)
            details['alternative'] = alt_match.group(1) if alt_match else 'handle this via email'
        
        return decision_type, tone, details
    
    async def _generate_acknowledgment(
        self, 
        analysis: EmailAnalysis, 
        tone: str, 
        details: Dict[str, Any],
        original_subject: str
    ) -> str:
        """Generate acknowledgment response."""
        template = self.templates['acknowledge'][tone]
        
        # Determine timeframe based on priority
        if analysis.priority.value == 'critical':
            timeframe = "within the next few hours"
            action = "address this immediately"
        elif analysis.priority.value == 'high':
            timeframe = "by end of day"
            action = "review this promptly"
        else:
            timeframe = "within the next few days"
            action = "look into this"
        
        response = template.format(
            subject=self._extract_subject_topic(original_subject),
            action=action,
            timeframe=timeframe,
            date=datetime.now().strftime("%B %d, %Y")
        )
        
        return self._add_closing(response, tone)
    
    async def _generate_meeting_acceptance(
        self, 
        analysis: EmailAnalysis, 
        tone: str, 
        details: Dict[str, Any],
        original_subject: str
    ) -> str:
        """Generate meeting acceptance response."""
        template = self.templates['meeting_accept'][tone]
        
        response = template.format(
            subject=self._extract_subject_topic(original_subject)
        )
        
        # Add calendar availability note
        response += "\n\nI'll send you some available time slots shortly."
        
        return self._add_closing(response, tone)
    
    async def _generate_meeting_decline(
        self, 
        analysis: EmailAnalysis, 
        tone: str, 
        details: Dict[str, Any],
        original_subject: str
    ) -> str:
        """Generate meeting decline response."""
        template = self.templates['meeting_decline'][tone]
        
        alternative = details.get('alternative', 'handle this via email')
        
        response = template.format(alternative=alternative)
        
        return self._add_closing(response, tone)
    
    async def _generate_information_response(
        self, 
        analysis: EmailAnalysis, 
        tone: str, 
        details: Dict[str, Any],
        original_subject: str
    ) -> str:
        """Generate information provision response."""
        template = self.templates['information_request'][tone]
        
        # Determine timeframe based on priority
        if analysis.priority.value == 'critical':
            timeframe = "within the next few hours"
        elif analysis.priority.value == 'high':
            timeframe = "by end of day"
        else:
            timeframe = "within the next few days"
        
        response = template.format(
            subject=self._extract_subject_topic(original_subject),
            timeframe=timeframe
        )
        
        return self._add_closing(response, tone)
    
    async def _generate_approval_response(
        self, 
        analysis: EmailAnalysis, 
        tone: str, 
        details: Dict[str, Any],
        original_subject: str
    ) -> str:
        """Generate approval response."""
        template = self.templates['approval'][tone]
        
        response = template.format(
            subject=self._extract_subject_topic(original_subject),
            decision="approve your request",
            details="Please proceed as outlined."
        )
        
        return self._add_closing(response, tone)
    
    async def _generate_clarification_request(
        self, 
        analysis: EmailAnalysis, 
        tone: str, 
        details: Dict[str, Any],
        original_subject: str
    ) -> str:
        """Generate clarification request."""
        template = self.templates['clarification'][tone]
        
        # Use first question from analysis or generic
        question = analysis.questions_asked[0] if analysis.questions_asked else "a few details"
        
        response = template.format(question=question)
        
        return self._add_closing(response, tone)
    
    async def _generate_delegation_response(
        self, 
        analysis: EmailAnalysis, 
        tone: str, 
        details: Dict[str, Any],
        original_subject: str
    ) -> str:
        """Generate delegation response."""
        template = self.templates['delegation'][tone]
        
        person = details.get('delegate_to', 'the appropriate team member')
        
        response = template.format(
            person=person,
            subject=self._extract_subject_topic(original_subject)
        )
        
        return self._add_closing(response, tone)
    
    async def _generate_custom_response(
        self, 
        analysis: EmailAnalysis, 
        tone: str, 
        details: Dict[str, Any],
        original_subject: str
    ) -> str:
        """Generate custom response based on user input."""
        custom_text = details.get('custom_text', '')
        
        if not custom_text:
            return await self._generate_default_response(analysis, tone, details, original_subject)
        
        # Enhance the custom text with professional formatting
        response = f"Thank you for your email.\n\n{custom_text}"
        
        return self._add_closing(response, tone)
    
    async def _generate_default_response(
        self, 
        analysis: EmailAnalysis, 
        tone: str, 
        details: Dict[str, Any],
        original_subject: str
    ) -> str:
        """Generate default acknowledgment response."""
        return await self._generate_acknowledgment(analysis, tone, details, original_subject)
    
    def _generate_fallback_response(self, analysis: EmailAnalysis, user_decision: str) -> ResponseDraft:
        """Generate fallback response when generation fails."""
        return ResponseDraft(
            original_email_id=analysis.email_id,
            subject="Re: Your Email",
            body="Thank you for your email. I will review it and get back to you soon.\n\nBest regards",
            tone='professional',
            confidence=0.3,
            reasoning="Fallback response due to generation error",
            requires_review=True
        )
    
    def _extract_subject_topic(self, subject: str) -> str:
        """Extract the main topic from email subject."""
        # Remove common prefixes
        subject = re.sub(r'^(re:|fwd?:|fw:)\s*', '', subject, flags=re.IGNORECASE)
        
        # If subject is too long, truncate
        if len(subject) > 50:
            subject = subject[:47] + "..."
        
        return subject if subject else "your inquiry"
    
    def _generate_subject(self, original_subject: str, decision_type: str) -> str:
        """Generate appropriate subject line for response."""
        if not original_subject:
            return "Re: Your Email"
        
        # Add Re: if not already present
        if not original_subject.lower().startswith('re:'):
            return f"Re: {original_subject}"
        
        return original_subject
    
    def _add_closing(self, response: str, tone: str) -> str:
        """Add appropriate closing to response."""
        closing = self.closings[tone][0]  # Use first option for consistency
        
        return f"{response}\n\n{closing}"
    
    def _calculate_response_confidence(self, decision_type: str, analysis: EmailAnalysis) -> float:
        """Calculate confidence in the generated response."""
        base_confidence = 0.7
        
        # Higher confidence for simple decisions
        if decision_type in ['acknowledge', 'meeting_accept']:
            base_confidence += 0.2
        
        # Lower confidence for complex decisions
        if decision_type in ['custom', 'delegate']:
            base_confidence -= 0.2
        
        # Adjust based on email analysis confidence
        base_confidence += (analysis.confidence - 0.5) * 0.2
        
        return max(0.1, min(1.0, base_confidence))


# Global email response generator instance
email_response_generator = EmailResponseGenerator()
