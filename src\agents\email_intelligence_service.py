"""Email Intelligence Service - Your Personal Email Assistant."""

import re
import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from ..core.logging import logger
from ..core.config import settings


class EmailPriority(str, Enum):
    """Email priority levels."""
    CRITICAL = "critical"      # Immediate response needed
    HIGH = "high"             # Response needed today
    MEDIUM = "medium"         # Response needed this week
    LOW = "low"              # FYI or can wait
    AUTOMATED = "automated"   # System/automated emails


class EmailAction(str, Enum):
    """Recommended actions for emails."""
    RESPOND_IMMEDIATELY = "respond_immediately"
    RESPOND_TODAY = "respond_today"
    RESPOND_LATER = "respond_later"
    DELEGATE = "delegate"
    ARCHIVE = "archive"
    SCHEDULE_MEETING = "schedule_meeting"
    CREATE_TASK = "create_task"
    FORWARD = "forward"
    AUTO_REPLY = "auto_reply"


@dataclass
class EmailAnalysis:
    """Analysis result for an email."""
    email_id: str
    priority: EmailPriority
    urgency_score: float  # 0.0 - 1.0
    importance_score: float  # 0.0 - 1.0
    sentiment: str  # positive, neutral, negative, urgent
    recommended_action: EmailAction
    response_deadline: Optional[datetime]
    key_points: List[str]
    questions_asked: List[str]
    action_items: List[str]
    meeting_request: bool
    requires_decision: bool
    confidence: float  # 0.0 - 1.0
    reasoning: str
    suggested_response: Optional[str] = None


@dataclass
class ResponseDraft:
    """Draft response for an email."""
    original_email_id: str
    subject: str
    body: str
    tone: str  # professional, friendly, formal, casual
    confidence: float
    reasoning: str
    requires_review: bool


class EmailIntelligenceService:
    """Intelligent email processing and automation service."""
    
    def __init__(self):
        self.logger = logger
        
        # Priority indicators
        self.critical_keywords = [
            'urgent', 'asap', 'emergency', 'critical', 'immediate',
            'deadline today', 'deadline tomorrow', 'overdue'
        ]
        
        self.high_priority_keywords = [
            'important', 'priority', 'deadline', 'due', 'meeting',
            'approval needed', 'decision needed', 'action required'
        ]
        
        self.automated_indicators = [
            'noreply', 'no-reply', 'donotreply', 'automated',
            'notification', 'alert', 'system', 'unsubscribe'
        ]
        
        # VIP sender patterns (can be learned from user behavior)
        self.vip_domains = [
            '@company.com',  # Work domain
            # Add more as needed
        ]
        
        # Question patterns
        self.question_patterns = [
            r'\?',
            r'can you\s+',
            r'could you\s+',
            r'would you\s+',
            r'will you\s+',
            r'do you\s+',
            r'are you\s+',
            r'what\s+(do|are|is|would)',
            r'when\s+(do|are|is|would)',
            r'where\s+(do|are|is|would)',
            r'how\s+(do|are|is|would)',
            r'why\s+(do|are|is|would)'
        ]
        
        # Meeting request patterns
        self.meeting_patterns = [
            r'meeting',
            r'call',
            r'schedule',
            r'available',
            r'calendar',
            r'appointment',
            r'discuss',
            r'chat'
        ]
        
        # Response templates
        self.response_templates = {
            'acknowledge': "Thank you for your email. I'll review this and get back to you {timeframe}.",
            'meeting_accept': "I'd be happy to meet. Let me check my calendar and propose some times.",
            'meeting_decline': "Unfortunately, I'm not available for a meeting at this time. Could we {alternative}?",
            'information_request': "I'll gather the information you requested and send it over {timeframe}.",
            'approval': "I've reviewed your request and {decision}. {details}",
            'delegation': "I'm forwarding this to {person} who can better assist you with this matter.",
            'clarification': "Thank you for reaching out. Could you please clarify {question}?"
        }
    
    async def analyze_email(self, email_data: Dict[str, Any]) -> EmailAnalysis:
        """Analyze an email and provide intelligent recommendations."""
        try:
            subject = email_data.get('subject', '')
            body = email_data.get('body', '')
            sender = email_data.get('sender_email', '')
            received_date = email_data.get('received_date')
            
            # Calculate priority and urgency
            priority, urgency_score = self._calculate_priority(subject, body, sender)
            importance_score = self._calculate_importance(subject, body, sender)
            
            # Analyze sentiment
            sentiment = self._analyze_sentiment(subject, body)
            
            # Extract key information
            key_points = self._extract_key_points(body)
            questions_asked = self._extract_questions(body)
            action_items = self._extract_action_items(body)
            
            # Check for meeting request
            meeting_request = self._detect_meeting_request(subject, body)
            
            # Check if decision is required
            requires_decision = self._requires_decision(body)
            
            # Determine recommended action
            recommended_action = self._recommend_action(
                priority, urgency_score, questions_asked, action_items, 
                meeting_request, requires_decision
            )
            
            # Calculate response deadline
            response_deadline = self._calculate_response_deadline(
                priority, urgency_score, received_date
            )
            
            # Calculate confidence
            confidence = self._calculate_confidence(
                priority, urgency_score, importance_score, sentiment
            )
            
            # Generate reasoning
            reasoning = self._generate_reasoning(
                priority, urgency_score, importance_score, sentiment,
                questions_asked, action_items, meeting_request
            )
            
            return EmailAnalysis(
                email_id=email_data.get('id', ''),
                priority=priority,
                urgency_score=urgency_score,
                importance_score=importance_score,
                sentiment=sentiment,
                recommended_action=recommended_action,
                response_deadline=response_deadline,
                key_points=key_points,
                questions_asked=questions_asked,
                action_items=action_items,
                meeting_request=meeting_request,
                requires_decision=requires_decision,
                confidence=confidence,
                reasoning=reasoning
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing email: {e}")
            # Return default analysis
            return EmailAnalysis(
                email_id=email_data.get('id', ''),
                priority=EmailPriority.MEDIUM,
                urgency_score=0.5,
                importance_score=0.5,
                sentiment='neutral',
                recommended_action=EmailAction.RESPOND_LATER,
                response_deadline=None,
                key_points=[],
                questions_asked=[],
                action_items=[],
                meeting_request=False,
                requires_decision=False,
                confidence=0.3,
                reasoning="Default analysis due to processing error"
            )
    
    def _calculate_priority(self, subject: str, body: str, sender: str) -> Tuple[EmailPriority, float]:
        """Calculate email priority and urgency score."""
        content = f"{subject} {body}".lower()
        urgency_score = 0.0
        
        # Check for automated emails first
        if any(indicator in sender.lower() for indicator in self.automated_indicators):
            return EmailPriority.AUTOMATED, 0.1
        
        # Check for critical keywords
        critical_matches = sum(1 for keyword in self.critical_keywords if keyword in content)
        if critical_matches > 0:
            urgency_score += critical_matches * 0.3
            
        # Check for high priority keywords
        high_matches = sum(1 for keyword in self.high_priority_keywords if keyword in content)
        if high_matches > 0:
            urgency_score += high_matches * 0.2
            
        # Check sender importance
        if any(domain in sender for domain in self.vip_domains):
            urgency_score += 0.2
            
        # Check for time-sensitive language
        time_sensitive_patterns = [
            r'today', r'tomorrow', r'this week', r'deadline',
            r'due\s+\w+', r'expires?', r'urgent'
        ]
        
        for pattern in time_sensitive_patterns:
            if re.search(pattern, content):
                urgency_score += 0.15
                
        # Normalize urgency score
        urgency_score = min(urgency_score, 1.0)
        
        # Determine priority level
        if urgency_score >= 0.8:
            return EmailPriority.CRITICAL, urgency_score
        elif urgency_score >= 0.6:
            return EmailPriority.HIGH, urgency_score
        elif urgency_score >= 0.3:
            return EmailPriority.MEDIUM, urgency_score
        else:
            return EmailPriority.LOW, urgency_score
    
    def _calculate_importance(self, subject: str, body: str, sender: str) -> float:
        """Calculate email importance score."""
        importance = 0.5  # Base importance
        
        # Sender importance
        if any(domain in sender for domain in self.vip_domains):
            importance += 0.3
            
        # Subject line indicators
        subject_lower = subject.lower()
        important_subject_words = ['important', 'urgent', 'action', 'decision', 'approval']
        for word in important_subject_words:
            if word in subject_lower:
                importance += 0.1
                
        # Body length (longer emails often more important)
        body_length = len(body.split())
        if body_length > 100:
            importance += 0.1
        elif body_length < 20:
            importance -= 0.1
            
        return min(importance, 1.0)
    
    def _analyze_sentiment(self, subject: str, body: str) -> str:
        """Analyze email sentiment."""
        content = f"{subject} {body}".lower()
        
        # Urgent indicators
        urgent_words = ['urgent', 'asap', 'emergency', 'critical', 'immediate']
        if any(word in content for word in urgent_words):
            return 'urgent'
            
        # Negative indicators
        negative_words = ['problem', 'issue', 'error', 'wrong', 'failed', 'disappointed']
        negative_count = sum(1 for word in negative_words if word in content)
        
        # Positive indicators
        positive_words = ['thank', 'great', 'excellent', 'good', 'pleased', 'happy']
        positive_count = sum(1 for word in positive_words if word in content)
        
        if negative_count > positive_count:
            return 'negative'
        elif positive_count > negative_count:
            return 'positive'
        else:
            return 'neutral'
    
    def _extract_key_points(self, body: str) -> List[str]:
        """Extract key points from email body."""
        # Simple implementation - could be enhanced with NLP
        sentences = body.split('.')
        key_points = []
        
        for sentence in sentences[:5]:  # Take first 5 sentences
            sentence = sentence.strip()
            if len(sentence) > 20 and len(sentence) < 200:
                key_points.append(sentence)
                
        return key_points
    
    def _extract_questions(self, body: str) -> List[str]:
        """Extract questions from email body."""
        questions = []
        sentences = body.split('.')
        
        for sentence in sentences:
            sentence = sentence.strip()
            for pattern in self.question_patterns:
                if re.search(pattern, sentence, re.IGNORECASE):
                    if '?' in sentence or any(q in sentence.lower() for q in ['can you', 'could you', 'would you']):
                        questions.append(sentence)
                        break
                        
        return questions[:3]  # Limit to 3 questions
    
    def _extract_action_items(self, body: str) -> List[str]:
        """Extract action items from email body."""
        action_patterns = [
            r'please\s+\w+',
            r'can you\s+\w+',
            r'need you to\s+\w+',
            r'action required',
            r'follow up',
            r'review\s+\w+',
            r'approve\s+\w+'
        ]
        
        action_items = []
        for pattern in action_patterns:
            matches = re.findall(pattern, body, re.IGNORECASE)
            action_items.extend(matches)
            
        return action_items[:3]  # Limit to 3 action items
    
    def _detect_meeting_request(self, subject: str, body: str) -> bool:
        """Detect if email contains a meeting request."""
        content = f"{subject} {body}".lower()
        
        meeting_score = 0
        for pattern in self.meeting_patterns:
            if re.search(pattern, content):
                meeting_score += 1
                
        return meeting_score >= 2
    
    def _requires_decision(self, body: str) -> bool:
        """Check if email requires a decision."""
        decision_patterns = [
            r'decision', r'approve', r'choose', r'select',
            r'yes or no', r'agree', r'confirm'
        ]
        
        content = body.lower()
        return any(re.search(pattern, content) for pattern in decision_patterns)
    
    def _recommend_action(
        self, 
        priority: EmailPriority, 
        urgency_score: float,
        questions: List[str],
        action_items: List[str],
        meeting_request: bool,
        requires_decision: bool
    ) -> EmailAction:
        """Recommend the best action for this email."""
        
        if priority == EmailPriority.AUTOMATED:
            return EmailAction.ARCHIVE
            
        if priority == EmailPriority.CRITICAL or urgency_score > 0.8:
            return EmailAction.RESPOND_IMMEDIATELY
            
        if meeting_request:
            return EmailAction.SCHEDULE_MEETING
            
        if requires_decision:
            return EmailAction.RESPOND_TODAY
            
        if questions or action_items:
            if priority == EmailPriority.HIGH:
                return EmailAction.RESPOND_TODAY
            else:
                return EmailAction.RESPOND_LATER
                
        if priority == EmailPriority.LOW:
            return EmailAction.ARCHIVE
            
        return EmailAction.RESPOND_LATER
    
    def _calculate_response_deadline(
        self, 
        priority: EmailPriority, 
        urgency_score: float, 
        received_date: Optional[datetime]
    ) -> Optional[datetime]:
        """Calculate when response is needed."""
        if not received_date:
            received_date = datetime.now()
            
        if priority == EmailPriority.CRITICAL:
            return received_date + timedelta(hours=2)
        elif priority == EmailPriority.HIGH:
            return received_date + timedelta(hours=8)
        elif priority == EmailPriority.MEDIUM:
            return received_date + timedelta(days=2)
        elif priority == EmailPriority.LOW:
            return received_date + timedelta(days=7)
        else:
            return None
    
    def _calculate_confidence(
        self, 
        priority: EmailPriority, 
        urgency_score: float,
        importance_score: float, 
        sentiment: str
    ) -> float:
        """Calculate confidence in the analysis."""
        confidence = 0.5
        
        # Higher confidence for clear priority signals
        if urgency_score > 0.7:
            confidence += 0.3
        elif urgency_score > 0.4:
            confidence += 0.2
            
        # Higher confidence for clear sentiment
        if sentiment in ['urgent', 'positive', 'negative']:
            confidence += 0.2
            
        return min(confidence, 1.0)
    
    def _generate_reasoning(
        self,
        priority: EmailPriority,
        urgency_score: float,
        importance_score: float,
        sentiment: str,
        questions: List[str],
        action_items: List[str],
        meeting_request: bool
    ) -> str:
        """Generate human-readable reasoning for the analysis."""
        reasons = []
        
        if priority == EmailPriority.CRITICAL:
            reasons.append("Contains critical urgency indicators")
        elif priority == EmailPriority.HIGH:
            reasons.append("Contains high priority keywords")
            
        if urgency_score > 0.7:
            reasons.append("High urgency score based on language analysis")
            
        if sentiment == 'urgent':
            reasons.append("Urgent tone detected")
        elif sentiment == 'negative':
            reasons.append("Negative sentiment may indicate issues")
            
        if questions:
            reasons.append(f"Contains {len(questions)} question(s) requiring response")
            
        if action_items:
            reasons.append(f"Contains {len(action_items)} action item(s)")
            
        if meeting_request:
            reasons.append("Appears to be a meeting request")
            
        if not reasons:
            reasons.append("Standard email analysis")
            
        return "; ".join(reasons)


# Global email intelligence service instance
email_intelligence_service = EmailIntelligenceService()
