#!/usr/bin/env python3
"""Development setup script for Assistant."""

import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return None


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 11):
        print("❌ Python 3.11+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def check_ollama():
    """Check if Ollama is installed and running."""
    print("🔄 Checking Ollama installation...")
    
    # Check if ollama command exists
    result = run_command("which ollama", "Checking Ollama installation")
    if not result:
        print("❌ Ollama is not installed. Please install from https://ollama.ai/")
        return False
    
    # Check if Ollama service is running
    result = run_command("ollama list", "Checking Ollama service")
    if result is None:
        print("❌ Ollama service is not running. Please start it with 'ollama serve'")
        return False
    
    print("✅ Ollama is installed and running")
    return True


def install_dependencies():
    """Install Python dependencies."""
    commands = [
        ("pip install -e .", "Installing project dependencies"),
        ("pip install -e .[dev]", "Installing development dependencies"),
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    return True


def setup_directories():
    """Create necessary directories."""
    directories = [
        "data/documents",
        "data/database",
        "data/models",
        "data/logs",
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")


def pull_ollama_model():
    """Pull the default Ollama model."""
    model = "deepseek-r1:latest"
    print(f"🔄 Pulling Ollama model: {model}")
    print("⚠️  This may take a while depending on your internet connection...")
    
    result = run_command(f"ollama pull {model}", f"Pulling {model}")
    if result:
        print(f"✅ Model {model} is ready")
        return True
    else:
        print(f"❌ Failed to pull model {model}")
        print("You can manually pull it later with: ollama pull deepseek-r1:latest")
        return False


def main():
    """Main setup function."""
    print("🚀 Setting up Assistant development environment...\n")
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Check Ollama
    ollama_ok = check_ollama()
    if ollama_ok:
        # Try to pull the model
        pull_ollama_model()
    else:
        print("⚠️  Ollama setup incomplete. Please install and configure Ollama manually.")
    
    print("\n🎉 Development environment setup complete!")
    print("\nNext steps:")
    print("1. Start the web interface: python -m src.api.web_app")
    print("2. Or use the CLI: python -m src.api.cli --help")
    
    if not ollama_ok:
        print("\nDon't forget to:")
        print("1. Install Ollama from https://ollama.ai/")
        print("2. Start Ollama service: ollama serve")
        print("3. Pull the model: ollama pull deepseek-r1:latest")


if __name__ == "__main__":
    main()
