{% extends "base.html" %}

{% block content %}
<div class="notes-page">
    <header class="page-header">
        <h1>Notes</h1>
        <p class="page-subtitle">Capture and organize your thoughts</p>
    </header>

    <!-- Quick Actions -->
    <section class="quick-actions">
        <div class="action-grid">
            <button class="action-card" id="newNoteBtn">
                <div class="action-icon">📝</div>
                <h3>New Note</h3>
                <p>Create a new note</p>
            </button>
            
            <button class="action-card" id="searchNotesBtn">
                <div class="action-icon">🔍</div>
                <h3>Search Notes</h3>
                <p>Find specific notes</p>
            </button>
            
            <button class="action-card" id="categoriesBtn">
                <div class="action-icon">📁</div>
                <h3>Categories</h3>
                <p>Organize by category</p>
            </button>
        </div>
    </section>

    <!-- Note Editor -->
    <section class="note-editor" id="noteEditor" style="display: none;">
        <div class="editor-header">
            <input type="text" id="noteTitle" placeholder="Note title...">
            <div class="editor-actions">
                <button class="btn btn-primary" id="saveNoteBtn">Save</button>
                <button class="btn btn-secondary" id="cancelNoteBtn">Cancel</button>
            </div>
        </div>
        
        <div class="editor-toolbar">
            <select id="noteCategory">
                <option value="general">General</option>
                <option value="work">Work</option>
                <option value="personal">Personal</option>
                <option value="ideas">Ideas</option>
                <option value="meeting">Meeting Notes</option>
            </select>
            
            <div class="format-buttons">
                <button class="format-btn" data-format="bold">B</button>
                <button class="format-btn" data-format="italic">I</button>
                <button class="format-btn" data-format="list">•</button>
            </div>
        </div>
        
        <textarea id="noteContent" placeholder="Start writing your note..."></textarea>
        
        <div class="editor-footer">
            <div class="note-meta">
                <span id="wordCount">0 words</span>
                <span id="lastSaved">Not saved</span>
            </div>
        </div>
    </section>

    <!-- Notes List -->
    <section class="notes-list">
        <div class="list-header">
            <h2>Your Notes</h2>
            <div class="list-controls">
                <select id="sortBy">
                    <option value="updated">Last Updated</option>
                    <option value="created">Date Created</option>
                    <option value="title">Title</option>
                    <option value="category">Category</option>
                </select>
                
                <select id="filterCategory">
                    <option value="all">All Categories</option>
                    <option value="general">General</option>
                    <option value="work">Work</option>
                    <option value="personal">Personal</option>
                    <option value="ideas">Ideas</option>
                    <option value="meeting">Meeting Notes</option>
                </select>
            </div>
        </div>
        
        <div class="notes-grid" id="notesGrid">
            <div class="loading-placeholder">Loading notes...</div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<style>
.notes-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.note-editor {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
}

.editor-header {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.editor-header input {
    flex: 1;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    font-weight: 500;
}

.editor-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.editor-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-lg);
}

.editor-toolbar select {
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.format-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.format-btn {
    width: 32px;
    height: 32px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s ease;
}

.format-btn:hover {
    background-color: var(--accent-primary);
    color: white;
}

.format-btn.active {
    background-color: var(--accent-primary);
    color: white;
}

#noteContent {
    width: 100%;
    min-height: 300px;
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    font-size: var(--font-size-base);
    line-height: 1.6;
    resize: vertical;
}

.editor-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.note-meta {
    display: flex;
    gap: var(--spacing-lg);
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.list-controls {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.list-controls select {
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.notes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.note-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all 0.2s ease;
    height: fit-content;
}

.note-card:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 2px 8px var(--shadow);
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.note-title {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    font-size: var(--font-size-lg);
}

.note-category {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 500;
    background-color: var(--accent-primary);
    color: white;
}

.note-preview {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.note-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.note-actions {
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.note-card:hover .note-actions {
    opacity: 1;
}

.note-action {
    padding: var(--spacing-xs);
    border: none;
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: all 0.2s ease;
}

.note-action:hover {
    background-color: var(--accent-primary);
    color: white;
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

.loading-placeholder, .no-notes {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
    grid-column: 1 / -1;
}

.search-highlight {
    background-color: yellow;
    color: black;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class NotesManager {
    constructor() {
        this.currentNote = null;
        this.notes = [];
        this.autoSaveTimer = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadNotes();
    }

    setupEventListeners() {
        // Quick actions
        document.getElementById('newNoteBtn').addEventListener('click', () => this.createNewNote());
        document.getElementById('searchNotesBtn').addEventListener('click', () => this.showSearchDialog());
        document.getElementById('categoriesBtn').addEventListener('click', () => this.showCategoriesView());

        // Editor actions
        document.getElementById('saveNoteBtn').addEventListener('click', () => this.saveNote());
        document.getElementById('cancelNoteBtn').addEventListener('click', () => this.cancelEdit());

        // Auto-save
        document.getElementById('noteContent').addEventListener('input', () => {
            this.updateWordCount();
            this.scheduleAutoSave();
        });

        document.getElementById('noteTitle').addEventListener('input', () => {
            this.scheduleAutoSave();
        });

        // Filters and sorting
        document.getElementById('sortBy').addEventListener('change', () => this.loadNotes());
        document.getElementById('filterCategory').addEventListener('change', () => this.loadNotes());

        // Format buttons
        document.querySelectorAll('.format-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.applyFormat(e.target.dataset.format));
        });
    }

    createNewNote() {
        this.currentNote = null;
        document.getElementById('noteEditor').style.display = 'block';
        document.getElementById('noteTitle').value = '';
        document.getElementById('noteContent').value = '';
        document.getElementById('noteCategory').value = 'general';
        document.getElementById('noteTitle').focus();
        this.updateWordCount();
        document.getElementById('lastSaved').textContent = 'Not saved';
    }

    async loadNotes() {
        const grid = document.getElementById('notesGrid');
        grid.innerHTML = '<div class="loading-placeholder">Loading notes...</div>';

        try {
            const sortBy = document.getElementById('sortBy').value;
            const category = document.getElementById('filterCategory').value;
            
            const response = await fetch(`/api/notes?sort=${sortBy}&category=${category}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.notes = data.notes || [];
                this.renderNotes();
            } else {
                grid.innerHTML = '<div class="no-notes">Error loading notes</div>';
            }
        } catch (error) {
            console.error('Error loading notes:', error);
            grid.innerHTML = '<div class="no-notes">Error loading notes</div>';
        }
    }

    renderNotes() {
        const grid = document.getElementById('notesGrid');

        if (this.notes.length === 0) {
            grid.innerHTML = '<div class="no-notes">No notes found. Create your first note!</div>';
            return;
        }

        const notesHtml = this.notes.map(note => {
            const preview = note.content ? note.content.substring(0, 150) + '...' : 'No content';
            const updatedDate = new Date(note.updated_at).toLocaleDateString();
            
            return `
                <div class="note-card" onclick="notesManager.editNote('${note.id}')">
                    <div class="note-header">
                        <h3 class="note-title">${note.title || 'Untitled'}</h3>
                        <span class="note-category">${note.category || 'general'}</span>
                    </div>
                    <div class="note-preview">${preview}</div>
                    <div class="note-footer">
                        <span>Updated: ${updatedDate}</span>
                        <div class="note-actions">
                            <button class="note-action" onclick="event.stopPropagation(); notesManager.duplicateNote('${note.id}')">
                                📋
                            </button>
                            <button class="note-action" onclick="event.stopPropagation(); notesManager.deleteNote('${note.id}')">
                                🗑️
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        grid.innerHTML = notesHtml;
    }

    async editNote(noteId) {
        try {
            const response = await fetch(`/api/notes/${noteId}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.currentNote = data.note;
                document.getElementById('noteEditor').style.display = 'block';
                document.getElementById('noteTitle').value = this.currentNote.title || '';
                document.getElementById('noteContent').value = this.currentNote.content || '';
                document.getElementById('noteCategory').value = this.currentNote.category || 'general';
                this.updateWordCount();
                document.getElementById('lastSaved').textContent = `Last saved: ${new Date(this.currentNote.updated_at).toLocaleString()}`;
                
                // Scroll to editor
                document.getElementById('noteEditor').scrollIntoView({ behavior: 'smooth' });
            }
        } catch (error) {
            app.showNotification('Error loading note', 'error');
        }
    }

    async saveNote() {
        const title = document.getElementById('noteTitle').value.trim();
        const content = document.getElementById('noteContent').value.trim();
        const category = document.getElementById('noteCategory').value;

        if (!title && !content) {
            app.showNotification('Note title or content is required', 'error');
            return;
        }

        const noteData = {
            title: title || 'Untitled',
            content: content,
            category: category
        };

        try {
            let response;
            if (this.currentNote) {
                // Update existing note
                response = await fetch(`/api/notes/${this.currentNote.id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(noteData)
                });
            } else {
                // Create new note
                response = await fetch('/api/notes', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(noteData)
                });
            }

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Note saved successfully!', 'success');
                document.getElementById('lastSaved').textContent = `Saved: ${new Date().toLocaleString()}`;
                this.currentNote = data.note;
                await this.loadNotes();
            } else {
                app.showNotification(`Error saving note: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error saving note: ${error.message}`, 'error');
        }
    }

    cancelEdit() {
        if (this.hasUnsavedChanges()) {
            if (!confirm('You have unsaved changes. Are you sure you want to cancel?')) {
                return;
            }
        }
        
        document.getElementById('noteEditor').style.display = 'none';
        this.currentNote = null;
        this.clearAutoSave();
    }

    hasUnsavedChanges() {
        if (!this.currentNote) {
            const title = document.getElementById('noteTitle').value.trim();
            const content = document.getElementById('noteContent').value.trim();
            return title || content;
        }
        
        const currentTitle = document.getElementById('noteTitle').value;
        const currentContent = document.getElementById('noteContent').value;
        
        return currentTitle !== (this.currentNote.title || '') || 
               currentContent !== (this.currentNote.content || '');
    }

    scheduleAutoSave() {
        this.clearAutoSave();
        this.autoSaveTimer = setTimeout(() => {
            if (this.hasUnsavedChanges()) {
                this.saveNote();
            }
        }, 5000); // Auto-save after 5 seconds of inactivity
    }

    clearAutoSave() {
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }
    }

    updateWordCount() {
        const content = document.getElementById('noteContent').value;
        const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
        document.getElementById('wordCount').textContent = `${wordCount} words`;
    }

    applyFormat(format) {
        const textarea = document.getElementById('noteContent');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);

        let replacement = selectedText;
        
        switch (format) {
            case 'bold':
                replacement = `**${selectedText}**`;
                break;
            case 'italic':
                replacement = `*${selectedText}*`;
                break;
            case 'list':
                replacement = selectedText.split('\n').map(line => `• ${line}`).join('\n');
                break;
        }

        textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start, start + replacement.length);
        
        this.updateWordCount();
        this.scheduleAutoSave();
    }

    async deleteNote(noteId) {
        if (!confirm('Are you sure you want to delete this note?')) return;

        try {
            const response = await fetch(`/api/notes/${noteId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Note deleted successfully!', 'success');
                await this.loadNotes();
            } else {
                app.showNotification(`Error deleting note: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error deleting note: ${error.message}`, 'error');
        }
    }

    async duplicateNote(noteId) {
        try {
            const response = await fetch(`/api/notes/${noteId}/duplicate`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Note duplicated successfully!', 'success');
                await this.loadNotes();
            } else {
                app.showNotification(`Error duplicating note: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error duplicating note: ${error.message}`, 'error');
        }
    }

    showSearchDialog() {
        const query = prompt('Search notes:');
        if (query) {
            this.searchNotes(query);
        }
    }

    async searchNotes(query) {
        try {
            const response = await fetch(`/api/notes/search?q=${encodeURIComponent(query)}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.notes = data.notes || [];
                this.renderNotes();
                app.showNotification(`Found ${this.notes.length} notes`, 'info');
            }
        } catch (error) {
            app.showNotification('Search failed', 'error');
        }
    }

    showCategoriesView() {
        // Filter by categories
        const categoryFilter = document.getElementById('filterCategory');
        categoryFilter.focus();
    }
}

// Initialize notes manager
let notesManager;
document.addEventListener('DOMContentLoaded', () => {
    notesManager = new NotesManager();
});
</script>
{% endblock %}
