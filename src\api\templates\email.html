{% extends "base.html" %}

{% block content %}
<div class="email-management">
    <header class="page-header">
        <h1>Email Intelligence</h1>
        <p class="page-subtitle">Your AI-powered personal email assistant</p>
    </header>

    <!-- Gmail Service Status -->
    <section class="service-status">
        <h2>Gmail Integration</h2>
        <div class="status-grid">
            <div class="status-card" id="gmailServiceStatus">
                <div class="status-icon">📧</div>
                <div class="status-content">
                    <h3>Gmail Service</h3>
                    <div class="status-indicator" id="serviceIndicator">
                        <span class="status-dot"></span>
                        <span class="status-text">Checking...</span>
                    </div>
                </div>
            </div>

            <div class="status-card" id="syncStatus">
                <div class="status-icon">🔄</div>
                <div class="status-content">
                    <h3>Last Sync</h3>
                    <div class="status-number" id="lastSyncTime">-</div>
                    <p class="status-label">Last synchronization</p>
                </div>
            </div>

            <div class="status-card" id="accountsStatus">
                <div class="status-icon">👤</div>
                <div class="status-content">
                    <h3>Connected Account</h3>
                    <div class="status-number" id="userEmail">-</div>
                    <p class="status-label">Gmail account</p>
                </div>
            </div>

            <div class="status-card" id="intelligenceStatus">
                <div class="status-icon">🧠</div>
                <div class="status-content">
                    <h3>AI Analysis</h3>
                    <div class="status-number" id="analysisCount">-</div>
                    <p class="status-label">Emails analyzed</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Actions -->
    <section class="quick-actions">
        <h2>Quick Actions</h2>
        <div class="action-grid">
            <button class="action-card" id="initializeBtn">
                <div class="action-icon">🚀</div>
                <h3>Connect Gmail</h3>
                <p>Set up Gmail API authentication</p>
            </button>

            <button class="action-card" id="syncEmailsBtn">
                <div class="action-icon">🔄</div>
                <h3>Sync Recent</h3>
                <p>Process recent emails with AI analysis</p>
            </button>

            <button class="action-card" id="priorityInboxBtn">
                <div class="action-icon">⭐</div>
                <h3>Priority Inbox</h3>
                <p>View emails that need immediate attention</p>
            </button>

            <button class="action-card" id="unreadEmailsBtn">
                <div class="action-icon">📬</div>
                <h3>Unread Emails</h3>
                <p>Process all unread emails with intelligence</p>
            </button>
        </div>
    </section>

    <!-- Priority Inbox -->
    <section class="priority-inbox" id="priorityInboxSection" style="display: none;">
        <h2>Priority Inbox</h2>
        <div class="inbox-controls">
            <button class="btn btn-secondary" id="refreshPriorityBtn">🔄 Refresh</button>
            <button class="btn btn-secondary" id="hidePriorityBtn">✕ Hide</button>
        </div>
        <div class="email-list" id="priorityEmailList">
            <div class="loading-placeholder">Loading priority emails...</div>
        </div>
    </section>

    <!-- Unread Emails -->
    <section class="unread-emails" id="unreadEmailsSection" style="display: none;">
        <h2>Unread Emails</h2>
        <div class="inbox-controls">
            <button class="btn btn-secondary" id="refreshUnreadBtn">🔄 Refresh</button>
            <button class="btn btn-secondary" id="hideUnreadBtn">✕ Hide</button>
        </div>
        <div class="email-list" id="unreadEmailList">
            <div class="loading-placeholder">Loading unread emails...</div>
        </div>
    </section>

    <!-- Email Response Generator -->
    <section class="response-generator" id="responseGeneratorSection" style="display: none;">
        <h2>Generate Response</h2>
        <div class="response-form">
            <div class="email-context" id="emailContext">
                <!-- Email context will be populated when generating response -->
            </div>

            <div class="form-group">
                <label for="userDecision">What would you like to do?</label>
                <select id="userDecision">
                    <option value="acknowledge">Acknowledge receipt</option>
                    <option value="approve">Approve request</option>
                    <option value="meeting_accept">Accept meeting</option>
                    <option value="meeting_decline">Decline meeting</option>
                    <option value="provide_info">Provide information</option>
                    <option value="clarify">Ask for clarification</option>
                    <option value="delegate">Delegate to someone else</option>
                    <option value="custom">Custom response</option>
                </select>
            </div>

            <div class="form-group" id="customResponseGroup" style="display: none;">
                <label for="customResponse">Custom response:</label>
                <textarea id="customResponse" rows="3" placeholder="Enter your custom response..."></textarea>
            </div>

            <div class="form-group">
                <label for="responseTone">Tone:</label>
                <select id="responseTone">
                    <option value="professional">Professional</option>
                    <option value="friendly">Friendly</option>
                    <option value="formal">Formal</option>
                </select>
            </div>

            <div class="form-actions">
                <button class="btn btn-primary" id="generateResponseBtn">Generate Response</button>
                <button class="btn btn-secondary" id="cancelResponseBtn">Cancel</button>
            </div>

            <div class="response-preview" id="responsePreview" style="display: none;">
                <h3>Generated Response:</h3>
                <div class="response-content" id="responseContent"></div>
                <div class="response-actions">
                    <button class="btn btn-success" id="sendResponseBtn">Send Response</button>
                    <button class="btn btn-secondary" id="copyResponseBtn">Copy to Clipboard</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Intelligence Settings -->
    <section class="intelligence-config">
        <h2>AI Settings</h2>
        <div class="config-form">
            <div class="form-group">
                <label>
                    <input type="checkbox" id="autoLabelEmails" checked>
                    Auto-label emails by priority
                </label>
                <small>Automatically add labels like "AI-Assistant/Critical" to emails</small>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" id="autoCreateTasks" checked>
                    Auto-create tasks from action items
                </label>
                <small>Create tasks automatically when emails contain action items</small>
            </div>

            <div class="form-group">
                <label for="priorityThreshold">Priority Threshold</label>
                <input type="range" id="priorityThreshold" min="0" max="1" step="0.1" value="0.7">
                <small>Urgency score threshold for priority inbox (0.0 - 1.0)</small>
            </div>

            <button class="btn btn-primary" id="saveConfigBtn">Save Settings</button>
        </div>
    </section>

    <!-- Recent Activity -->
    <section class="recent-activity">
        <h2>Recent Activity</h2>
        <div class="activity-log" id="activityLog">
            <div class="activity-item">
                <div class="activity-icon">📧</div>
                <div class="activity-content">
                    <p class="activity-title">Email service ready</p>
                    <p class="activity-time">Waiting for first sync...</p>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Loading Modal -->
<div class="modal" id="loadingModal" style="display: none;">
    <div class="modal-content">
        <div class="loading-spinner"></div>
        <h3>Processing...</h3>
        <p id="loadingMessage">Please wait while we process your request.</p>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.email-management {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxl);
}

.service-status .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--text-muted);
}

.status-dot.online {
    background-color: #28a745;
}

.status-dot.offline {
    background-color: #dc3545;
}

.status-dot.processing {
    background-color: #ffc107;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.account-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
}

.account-card.connected {
    border-color: #28a745;
}

.account-card.disconnected {
    border-color: #dc3545;
}

.config-form {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    max-width: 500px;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.form-group input[type="number"] {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.form-group small {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-primary);
    padding: var(--spacing-xxl);
    border-radius: var(--border-radius);
    text-align: center;
    max-width: 400px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.activity-log {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: var(--font-size-lg);
}

.activity-content {
    flex: 1;
}

.activity-title {
    margin: 0;
    font-weight: 500;
}

.activity-time {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Email List Styles */
.email-list {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
}

.email-item {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s ease;
}

.email-item:hover {
    background-color: var(--bg-primary);
}

.email-item:last-child {
    border-bottom: none;
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
}

.email-sender strong {
    color: var(--text-primary);
}

.email-address {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-left: var(--spacing-xs);
}

.email-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.priority-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.priority-critical {
    background-color: #dc3545;
    color: white;
}

.priority-high {
    background-color: #fd7e14;
    color: white;
}

.priority-medium {
    background-color: #ffc107;
    color: #000;
}

.priority-low {
    background-color: #28a745;
    color: white;
}

.priority-automated {
    background-color: #6c757d;
    color: white;
}

.urgency-score {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.email-subject {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.email-snippet {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
}

.email-analysis {
    background-color: var(--bg-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.analysis-item {
    margin-bottom: var(--spacing-xs);
}

.analysis-item:last-child {
    margin-bottom: 0;
}

.email-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.email-received {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    text-align: right;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

/* Inbox Controls */
.inbox-controls {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

/* Response Generator */
.response-form {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    max-width: 600px;
}

.email-context {
    background-color: var(--bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-lg);
}

.context-info p {
    margin: 0;
    margin-bottom: var(--spacing-xs);
}

.context-info p:last-child {
    margin-bottom: 0;
}

.form-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.response-preview {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.response-subject {
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.response-body {
    background-color: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-sm);
    white-space: pre-wrap;
    font-family: inherit;
}

.response-meta {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.response-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Utility Classes */
.loading-placeholder, .error-message, .no-emails {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-secondary);
}

.error-message {
    color: #dc3545;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Gmail Email Intelligence Manager
class GmailEmailManager {
    constructor() {
        this.currentEmailId = null;
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadGmailStatus();

        // Refresh status every 30 seconds
        setInterval(() => this.loadGmailStatus(), 30000);
    }

    setupEventListeners() {
        // Main action buttons
        document.getElementById('initializeBtn').addEventListener('click', () => this.initializeGmail());
        document.getElementById('syncEmailsBtn').addEventListener('click', () => this.syncRecentEmails());
        document.getElementById('priorityInboxBtn').addEventListener('click', () => this.showPriorityInbox());
        document.getElementById('unreadEmailsBtn').addEventListener('click', () => this.showUnreadEmails());

        // Inbox controls
        document.getElementById('refreshPriorityBtn')?.addEventListener('click', () => this.refreshPriorityInbox());
        document.getElementById('hidePriorityBtn')?.addEventListener('click', () => this.hidePriorityInbox());
        document.getElementById('refreshUnreadBtn')?.addEventListener('click', () => this.refreshUnreadEmails());
        document.getElementById('hideUnreadBtn')?.addEventListener('click', () => this.hideUnreadEmails());

        // Response generator
        document.getElementById('userDecision').addEventListener('change', (e) => this.handleDecisionChange(e));
        document.getElementById('generateResponseBtn').addEventListener('click', () => this.generateResponse());
        document.getElementById('cancelResponseBtn').addEventListener('click', () => this.hideResponseGenerator());
        document.getElementById('sendResponseBtn')?.addEventListener('click', () => this.sendResponse());
        document.getElementById('copyResponseBtn')?.addEventListener('click', () => this.copyResponse());

        // Settings
        document.getElementById('saveConfigBtn').addEventListener('click', () => this.saveConfiguration());
    }

    async loadGmailStatus() {
        try {
            const response = await fetch('/api/gmail/status');
            const data = await response.json();

            if (data.status === 'success') {
                this.updateStatusDisplay(data.gmail_status);
            } else {
                this.showError('Failed to load Gmail status');
            }
        } catch (error) {
            console.error('Error loading Gmail status:', error);
            this.showError('Error loading Gmail status');
        }
    }

    updateStatusDisplay(status) {
        // Update service indicator
        const indicator = document.getElementById('serviceIndicator');
        const dot = indicator.querySelector('.status-dot');
        const text = indicator.querySelector('.status-text');

        if (status.authenticated) {
            dot.className = 'status-dot online';
            text.textContent = 'Connected';
        } else {
            dot.className = 'status-dot offline';
            text.textContent = 'Not Connected';
        }

        // Update last sync time
        const lastSyncElement = document.getElementById('lastSyncTime');
        if (status.last_sync) {
            lastSyncElement.textContent = new Date(status.last_sync).toLocaleString();
        } else {
            lastSyncElement.textContent = 'Never';
        }

        // Update user email
        const userEmailElement = document.getElementById('userEmail');
        userEmailElement.textContent = status.user_email || 'Not connected';

        // Update analysis count (placeholder for now)
        document.getElementById('analysisCount').textContent = '0';

        // Update settings
        document.getElementById('autoLabelEmails').checked = status.auto_label_enabled;
        document.getElementById('priorityThreshold').value = status.priority_threshold;
    }

    async initializeGmail() {
        this.showLoading('Connecting to Gmail...');

        try {
            const response = await fetch('/api/gmail/initialize', {
                method: 'POST'
            });
            const data = await response.json();

            this.hideLoading();

            if (data.status === 'success') {
                this.addActivity('🚀', `Gmail connected: ${data.user_email}`);
                app.showNotification('Gmail connected successfully!', 'success');
                await this.loadGmailStatus();
            } else {
                this.showError(`Gmail connection failed: ${data.message}`);
            }
        } catch (error) {
            this.hideLoading();
            this.showError(`Gmail connection error: ${error.message}`);
        }
    }

    async syncRecentEmails() {
        this.showLoading('Syncing recent emails...');

        try {
            const response = await fetch('/api/gmail/sync?hours=1', {
                method: 'POST'
            });
            const data = await response.json();

            this.hideLoading();

            if (data.status === 'success') {
                this.addActivity('🔄', `Synced ${data.processed_count} emails`);
                app.showNotification(`Synced ${data.processed_count} emails!`, 'success');
                await this.loadGmailStatus();
            } else {
                this.showError(`Email sync failed: ${data.message}`);
            }
        } catch (error) {
            this.hideLoading();
            this.showError(`Email sync error: ${error.message}`);
        }
    }

    async showPriorityInbox() {
        document.getElementById('priorityInboxSection').style.display = 'block';
        document.getElementById('unreadEmailsSection').style.display = 'none';
        document.getElementById('responseGeneratorSection').style.display = 'none';

        await this.loadPriorityEmails();
    }

    async showUnreadEmails() {
        document.getElementById('unreadEmailsSection').style.display = 'block';
        document.getElementById('priorityInboxSection').style.display = 'none';
        document.getElementById('responseGeneratorSection').style.display = 'none';

        await this.loadUnreadEmails();
    }

    async loadPriorityEmails() {
        const container = document.getElementById('priorityEmailList');
        container.innerHTML = '<div class="loading-placeholder">Loading priority emails...</div>';

        try {
            const response = await fetch('/api/gmail/priority-inbox');
            const data = await response.json();

            if (data.status === 'success') {
                this.renderEmailList(container, data.priority_emails, 'priority');
            } else {
                container.innerHTML = `<div class="error-message">Error: ${data.message}</div>`;
            }
        } catch (error) {
            container.innerHTML = `<div class="error-message">Error loading priority emails: ${error.message}</div>`;
        }
    }

    async loadUnreadEmails() {
        const container = document.getElementById('unreadEmailList');
        container.innerHTML = '<div class="loading-placeholder">Loading unread emails...</div>';

        try {
            const response = await fetch('/api/gmail/unread');
            const data = await response.json();

            if (data.status === 'success') {
                this.renderEmailList(container, data.emails, 'unread');
            } else {
                container.innerHTML = `<div class="error-message">Error: ${data.message}</div>`;
            }
        } catch (error) {
            container.innerHTML = `<div class="error-message">Error loading unread emails: ${error.message}</div>`;
        }
    }

    renderEmailList(container, emails, type) {
        if (!emails || emails.length === 0) {
            container.innerHTML = '<div class="no-emails">No emails found</div>';
            return;
        }

        const emailsHtml = emails.map(email => {
            const priorityClass = email.analysis.priority;
            const urgencyScore = Math.round(email.analysis.urgency_score * 100);
            const requiresAttention = email.requires_attention ? '⚠️' : '';

            return `
                <div class="email-item ${priorityClass}" data-email-id="${email.id}">
                    <div class="email-header">
                        <div class="email-sender">
                            <strong>${email.sender_name || email.sender_email}</strong>
                            <span class="email-address">${email.sender_email}</span>
                        </div>
                        <div class="email-meta">
                            <span class="priority-badge priority-${priorityClass}">${priorityClass.toUpperCase()}</span>
                            <span class="urgency-score">Urgency: ${urgencyScore}%</span>
                            ${requiresAttention}
                        </div>
                    </div>
                    <div class="email-subject">${email.subject}</div>
                    <div class="email-snippet">${email.snippet}</div>
                    <div class="email-analysis">
                        <div class="analysis-item">
                            <strong>Recommended Action:</strong> ${email.analysis.recommended_action.replace('_', ' ')}
                        </div>
                        ${email.analysis.questions_asked.length > 0 ?
                            `<div class="analysis-item"><strong>Questions:</strong> ${email.analysis.questions_asked.length}</div>` : ''}
                        ${email.analysis.action_items.length > 0 ?
                            `<div class="analysis-item"><strong>Action Items:</strong> ${email.analysis.action_items.length}</div>` : ''}
                        ${email.analysis.meeting_request ?
                            `<div class="analysis-item"><strong>Meeting Request:</strong> Yes</div>` : ''}
                    </div>
                    <div class="email-actions">
                        <button class="btn btn-sm btn-primary" onclick="emailManager.showResponseGenerator('${email.id}')">
                            Generate Response
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="emailManager.markAsProcessed('${email.id}')">
                            Mark Processed
                        </button>
                    </div>
                    <div class="email-received">${new Date(email.received_date).toLocaleString()}</div>
                </div>
            `;
        }).join('');

        container.innerHTML = emailsHtml;
    }

    async showResponseGenerator(emailId) {
        this.currentEmailId = emailId;
        document.getElementById('responseGeneratorSection').style.display = 'block';

        // Scroll to response generator
        document.getElementById('responseGeneratorSection').scrollIntoView({ behavior: 'smooth' });

        // Load email context (you would fetch this from your email data)
        document.getElementById('emailContext').innerHTML = `
            <div class="context-info">
                <p><strong>Generating response for email:</strong> ${emailId}</p>
                <p>Select your decision and tone below to generate a professional response.</p>
            </div>
        `;
    }

    handleDecisionChange(e) {
        const customGroup = document.getElementById('customResponseGroup');
        if (e.target.value === 'custom') {
            customGroup.style.display = 'block';
        } else {
            customGroup.style.display = 'none';
        }
    }

    async generateResponse() {
        if (!this.currentEmailId) {
            this.showError('No email selected');
            return;
        }

        const decision = document.getElementById('userDecision').value;
        const tone = document.getElementById('responseTone').value;
        const customResponse = document.getElementById('customResponse').value;

        let userDecision = decision;
        if (decision === 'custom' && customResponse) {
            userDecision = `custom: ${customResponse}`;
        } else {
            userDecision = `${decision} in ${tone} tone`;
        }

        this.showLoading('Generating response...');

        try {
            // This would need the actual email analysis data
            // For now, we'll show a placeholder
            const response = await fetch('/api/email/generate-response', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    email_analysis: { email_id: this.currentEmailId, priority: 'medium' },
                    user_decision: userDecision,
                    context: { subject: 'Email Subject', sender: '<EMAIL>' }
                })
            });

            const data = await response.json();
            this.hideLoading();

            if (data.status === 'success') {
                this.showResponsePreview(data.response_draft);
            } else {
                this.showError(`Response generation failed: ${data.message}`);
            }
        } catch (error) {
            this.hideLoading();
            this.showError(`Response generation error: ${error.message}`);
        }
    }

    showResponsePreview(responseDraft) {
        const preview = document.getElementById('responsePreview');
        const content = document.getElementById('responseContent');

        content.innerHTML = `
            <div class="response-subject"><strong>Subject:</strong> ${responseDraft.subject}</div>
            <div class="response-body">${responseDraft.body.replace(/\n/g, '<br>')}</div>
            <div class="response-meta">
                <small>Tone: ${responseDraft.tone} | Confidence: ${Math.round(responseDraft.confidence * 100)}%</small>
            </div>
        `;

        preview.style.display = 'block';
    }

    hideResponseGenerator() {
        document.getElementById('responseGeneratorSection').style.display = 'none';
        this.currentEmailId = null;
    }

    async sendResponse() {
        // This would integrate with Gmail API to send the response
        app.showNotification('Response sent! (Feature coming soon)', 'info');
        this.hideResponseGenerator();
    }

    async copyResponse() {
        const responseBody = document.getElementById('responseContent').querySelector('.response-body').textContent;
        await navigator.clipboard.writeText(responseBody);
        app.showNotification('Response copied to clipboard!', 'success');
    }

    async markAsProcessed(emailId) {
        // This would mark the email as processed
        app.showNotification('Email marked as processed!', 'success');
    }

    // Inbox control methods
    async refreshPriorityInbox() { await this.loadPriorityEmails(); }
    async refreshUnreadEmails() { await this.loadUnreadEmails(); }
    hidePriorityInbox() { document.getElementById('priorityInboxSection').style.display = 'none'; }
    hideUnreadEmails() { document.getElementById('unreadEmailsSection').style.display = 'none'; }

    async saveConfiguration() {
        app.showNotification('Settings saved!', 'success');
    }

    addActivity(icon, message) {
        const activityLog = document.getElementById('activityLog');
        const activityItem = document.createElement('div');
        activityItem.className = 'activity-item';
        
        activityItem.innerHTML = `
            <div class="activity-icon">${icon}</div>
            <div class="activity-content">
                <p class="activity-title">${message}</p>
                <p class="activity-time">${new Date().toLocaleString()}</p>
            </div>
        `;
        
        activityLog.insertBefore(activityItem, activityLog.firstChild);
        
        // Keep only last 10 items
        while (activityLog.children.length > 10) {
            activityLog.removeChild(activityLog.lastChild);
        }
    }

    showLoading(message) {
        document.getElementById('loadingMessage').textContent = message;
        document.getElementById('loadingModal').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingModal').style.display = 'none';
    }

    showError(message) {
        app.showNotification(message, 'error');
    }
}

// Initialize Gmail email manager when page loads
let emailManager;
document.addEventListener('DOMContentLoaded', () => {
    emailManager = new GmailEmailManager();
});
</script>
{% endblock %}
