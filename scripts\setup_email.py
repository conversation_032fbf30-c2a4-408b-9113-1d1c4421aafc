#!/usr/bin/env python3
"""Email integration setup script for Assistant."""

import json
import os
import sys
from pathlib import Path


def main():
    """Main setup function for email integration."""
    print("🚀 Setting up Assistant Email Integration...\n")
    
    # Check if we're in the right directory
    if not Path("config/settings.yaml").exists():
        print("❌ Please run this script from the Assistant project root directory")
        sys.exit(1)
    
    print("This script will help you set up email integration with Gmail and Proton Mail.")
    print("You'll need to have Google Cloud Console access for Gmail integration.\n")
    
    # Gmail setup
    setup_gmail = input("Do you want to set up Gmail integration? (y/n): ").lower().startswith('y')
    if setup_gmail:
        setup_gmail_integration()
    
    # Proton setup
    setup_proton = input("\nDo you want to set up Proton Mail integration? (y/n): ").lower().startswith('y')
    if setup_proton:
        setup_proton_integration()
    
    print("\n🎉 Email integration setup complete!")
    print("\nNext steps:")
    print("1. Start the Assistant: python -m src.api.web_app")
    print("2. Go to http://localhost:8000/email")
    print("3. Click 'Initialize Service' to authenticate")
    print("4. Click 'Sync Emails' to start processing")
    print("\nFor detailed setup instructions, see: docs/google_setup_guide.md")


def setup_gmail_integration():
    """Set up Gmail integration."""
    print("\n📧 Gmail Integration Setup")
    print("=" * 40)
    
    print("\n1. First, you need to set up Google Cloud Console:")
    print("   - Go to https://console.cloud.google.com/")
    print("   - Create a new project or select existing one")
    print("   - Enable Gmail API, Google Calendar API, and People API")
    print("   - Create OAuth 2.0 credentials (Desktop application)")
    print("   - Download the credentials JSON file")
    
    # Personal Gmail setup
    print("\n2. Personal Gmail Account Setup:")
    personal_creds = input("   Path to personal Gmail credentials JSON file: ").strip()
    if personal_creds and Path(personal_creds).exists():
        # Copy to config directory
        dest_path = Path("config/gmail_personal_credentials.json")
        dest_path.parent.mkdir(exist_ok=True)
        
        import shutil
        shutil.copy2(personal_creds, dest_path)
        print(f"   ✅ Copied credentials to {dest_path}")
    else:
        print("   ⚠️  Credentials file not found. You'll need to add it manually.")
    
    # Work Gmail setup
    work_setup = input("\n   Do you have a separate work Gmail account? (y/n): ").lower().startswith('y')
    if work_setup:
        work_creds = input("   Path to work Gmail credentials JSON file: ").strip()
        if work_creds and Path(work_creds).exists():
            dest_path = Path("config/gmail_work_credentials.json")
            import shutil
            shutil.copy2(work_creds, dest_path)
            print(f"   ✅ Copied work credentials to {dest_path}")
        else:
            print("   ⚠️  Work credentials file not found. You'll need to add it manually.")
    
    # Update configuration
    update_gmail_config(work_setup)


def setup_proton_integration():
    """Set up Proton Mail integration."""
    print("\n🔒 Proton Mail Integration Setup")
    print("=" * 40)
    
    print("\n1. First, set up Proton Bridge:")
    print("   - Download from https://proton.me/mail/bridge")
    print("   - Install and configure with your Proton account")
    print("   - Note the IMAP settings (usually localhost:1143)")
    
    # Get Proton settings
    proton_email = input("\n2. Proton email address: ").strip()
    proton_password = input("   Proton Bridge password: ").strip()
    proton_context = input("   Context (personal/work): ").strip() or "personal"
    
    if proton_email and proton_password:
        # Save password to file
        password_file = Path("config/proton_password.txt")
        password_file.parent.mkdir(exist_ok=True)
        password_file.write_text(proton_password)
        password_file.chmod(0o600)  # Restrict permissions
        print(f"   ✅ Saved password to {password_file}")
        
        # Update configuration
        update_proton_config(proton_email, proton_context)
    else:
        print("   ⚠️  Proton configuration incomplete. You'll need to configure manually.")


def update_gmail_config(has_work_account):
    """Update Gmail configuration in settings.yaml."""
    try:
        import yaml
        
        config_path = Path("config/settings.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Update Gmail configuration
        gmail_config = config['integrations']['email']['providers']['gmail']
        gmail_config['enabled'] = True
        
        # Ensure accounts section exists
        if 'accounts' not in gmail_config:
            gmail_config['accounts'] = {}
        
        # Personal account
        gmail_config['accounts']['personal'] = {
            'credentials_file': './config/gmail_personal_credentials.json',
            'token_file': './data/tokens/gmail_personal_token.json',
            'context': 'personal'
        }
        
        # Work account if needed
        if has_work_account:
            gmail_config['accounts']['work'] = {
                'credentials_file': './config/gmail_work_credentials.json',
                'token_file': './data/tokens/gmail_work_token.json',
                'context': 'work'
            }
        
        # Save updated configuration
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)
        
        print("   ✅ Updated Gmail configuration in settings.yaml")
        
    except Exception as e:
        print(f"   ⚠️  Could not update configuration: {e}")
        print("   You'll need to update config/settings.yaml manually")


def update_proton_config(email, context):
    """Update Proton configuration in settings.yaml."""
    try:
        import yaml
        
        config_path = Path("config/settings.yaml")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Update Proton configuration
        proton_config = config['integrations']['email']['providers']['proton']
        proton_config['enabled'] = True
        proton_config['username'] = email
        proton_config['context'] = context
        
        # Save updated configuration
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)
        
        print("   ✅ Updated Proton configuration in settings.yaml")
        
    except Exception as e:
        print(f"   ⚠️  Could not update configuration: {e}")
        print("   You'll need to update config/settings.yaml manually")


def create_sample_credentials():
    """Create sample credentials file for reference."""
    sample_creds = {
        "installed": ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }
    
    sample_path = Path("config/gmail_credentials_sample.json")
    sample_path.parent.mkdir(exist_ok=True)
    
    with open(sample_path, 'w') as f:
        json.dump(sample_creds, f, indent=2)
    
    print(f"   📝 Created sample credentials file: {sample_path}")
    print("   Replace the values with your actual Google Cloud Console credentials")


if __name__ == "__main__":
    main()
