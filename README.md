# Assistant - Local Knowledge Library

A constellation of AI agents powered by local LLM models (via Ollama) that manage and interact with your personal knowledge base. Think of it as a local library with specialized librarians for different domains.

## Architecture Overview

This system consists of:
- **Knowledge Store**: Vector database for RAG-based information retrieval
- **Metadata Database**: Structured storage for notes, tasks, projects, and relationships
- **Specialized Agents**: Domain-specific AI assistants that handle different types of tasks
- **Document Processing**: Automated ingestion and categorization of various file types
- **Local LLM Integration**: All AI processing happens locally via Ollama

## Project Structure

```
src/
├── core/                   # Core system components
├── agents/                 # Specialized AI agents
├── knowledge/              # Knowledge storage and retrieval
├── processing/             # Document ingestion and processing
├── api/                    # API endpoints and interfaces
└── utils/                  # Shared utilities
```

## Development Status

🚧 **Phase 1: Foundation** (In Progress)
- [ ] Project structure setup
- [ ] Ollama integration
- [ ] Basic vector database setup
- [ ] Document ingestion pipeline
- [ ] Metadata schema design

## Features (Planned)

### Phase 1: Foundation
- 📄 **Document Processing**: PDF, text, markdown, images, and URL bookmarks
- 🔍 **Semantic Search**: AI-powered search across your knowledge base
- 🧠 **Local AI**: Powered by DeepSeek-R1 via Ollama (fully local)
- 🌐 **Web Interface**: Clean, responsive web UI for easy interaction
- 📊 **Dashboard**: Overview of your knowledge library and system status

### Phase 1.5: Personal Productivity Integration
- 📧 **Email Integration**: Gmail, Outlook, and IMAP support with intelligent task creation
- 📅 **Calendar Management**: Multi-calendar sync with conflict resolution and availability optimization
- 👥 **Contact Management**: Unified contact system with CSV import and automatic updates
- 🤖 **Learning System**: Adapts to your preferences and learns from your corrections
- ⚡ **Smart Task Creation**: Automatically creates tasks from emails with AI-powered prioritization

### Future Phases
- 🤖 **Specialized Agents**: Archivist, Research, Task Manager, Note Keeper, Project Manager
- 🔗 **Smart Linking**: Automatic relationship discovery between documents
- 📝 **Note Management**: Intelligent note organization and cross-referencing
- ✅ **Task Management**: AI-assisted task tracking and project management
- 🧮 **Learning System**: Adapts to your preferences and usage patterns

## Prerequisites

- **Python 3.11+** (recommended for best AI library compatibility)
- **Ollama** installed and running ([Download here](https://ollama.ai/))
- **8GB+ RAM** (recommended for DeepSeek-R1 model)
- **10GB+ free disk space** (for models and data storage)

## Quick Start

### 1. Automatic Setup (Recommended)
```bash
# Clone the repository
git clone <your-repo-url>
cd assistant

# Run the setup script (handles dependencies and configuration)
python scripts/dev_setup.py
```

### 2. Manual Setup
```bash
# Install dependencies
pip install -e .
pip install -e .[dev]

# Create data directories
mkdir -p data/{documents,database,models,logs}

# Install and start Ollama (if not already done)
# Visit https://ollama.ai/ for installation instructions
ollama serve  # In a separate terminal
ollama pull deepseek-r1:latest
```

### 3. Start the Application

**Web Interface** (Recommended):
```bash
python -m src.api.web_app
# Open http://localhost:8000 in your browser
```

**Command Line Interface**:
```bash
python -m src.api.cli --help
python -m src.api.cli status
python -m src.api.cli init
```

## Usage Examples

### Web Interface
1. **Upload Documents**: Drag and drop files or use the upload page
2. **Search Knowledge**: Use natural language queries to find information
3. **Manage Notes**: Create and organize your thoughts and ideas
4. **Chat with AI**: Ask questions about your documents and get intelligent responses

### CLI Examples
```bash
# Check system status
assistant status

# Initialize the system
assistant init

# Ingest a document
assistant ingest /path/to/document.pdf

# Search your knowledge base
assistant search "machine learning concepts"

# Add a task
assistant add-task "Review the new research paper"

# List tasks
assistant list-tasks
```

## Configuration

The system is configured via `config/settings.yaml`. Key settings include:

```yaml
# Ollama Configuration
ollama:
  host: "http://localhost:11434"
  default_model: "deepseek-r1:latest"
  reasoning_model: "deepseek-r1:latest"
  fast_model: "deepseek-r1:latest"

# Supported document formats
processing:
  supported_formats:
    - "pdf"      # PDF documents
    - "txt"      # Plain text
    - "md"       # Markdown
    - "rst"      # reStructuredText
    - "html"     # HTML files
    - "jpg"      # Images (with OCR)
    - "png"      # Images (with OCR)
    - "url"      # Bookmarked URLs

# Web interface
api:
  host: "localhost"
  port: 8000
  enable_web_ui: true
```

## Development

### Project Structure
```
assistant/
├── src/
│   ├── core/           # Core system components
│   ├── agents/         # AI agents (future)
│   ├── knowledge/      # Knowledge storage and retrieval
│   ├── processing/     # Document processing pipeline
│   ├── api/           # Web and CLI interfaces
│   └── utils/         # Shared utilities
├── config/            # Configuration files
├── data/             # Local data storage
├── docs/             # Documentation
├── scripts/          # Setup and utility scripts
└── tests/            # Test suite
```

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
```

### Code Quality
```bash
# Format code
black .
isort .

# Lint code
flake8

# Type checking
mypy src/
```

### Contributing to Development
1. **Follow the technical specification** in `docs/technical_specification.md`
2. **Write tests** for new functionality
3. **Update documentation** as needed
4. **Use type hints** throughout the codebase
5. **Follow PEP 8** style guidelines

## Troubleshooting

### Common Issues

**Ollama Connection Failed**:
- Ensure Ollama is installed and running: `ollama serve`
- Check if the model is available: `ollama list`
- Verify the host configuration in `config/settings.yaml`

**Memory Issues**:
- DeepSeek-R1 requires significant RAM
- Consider using a smaller model for development
- Monitor system resources during operation

**Document Processing Errors**:
- Check file permissions and formats
- Ensure OCR dependencies are installed (tesseract)
- Review logs in `data/logs/assistant.log`

**Web Interface Not Loading**:
- Check if port 8000 is available
- Verify static files are properly served
- Check browser console for JavaScript errors

### Getting Help
- Check the logs in `data/logs/assistant.log`
- Review the technical specification in `docs/`
- Ensure all dependencies are properly installed

## Roadmap

### Phase 1: Foundation ✅ (Current)
- [x] Project structure and configuration
- [x] Basic web interface with navigation
- [ ] Ollama integration
- [ ] Document processing pipeline
- [ ] Vector database setup
- [ ] Basic search functionality

### Phase 1.5: Personal Productivity Integration (Weeks 3.5-4)
- [ ] Email provider integrations (Gmail, Outlook, IMAP)
- [ ] Calendar synchronization (Google Calendar, Outlook)
- [ ] Contact management system
- [ ] Task creation from emails with AI analysis
- [ ] Learning system for priority adaptation
- [ ] Availability management and meeting optimization

### Phase 2: Core Agents (Weeks 4-6)
- [ ] Agent framework and communication
- [ ] Archivist Agent (document categorization)
- [ ] Research Agent (information synthesis)
- [ ] Basic agent orchestration

### Phase 3: Specialized Agents (Weeks 7-10)
- [ ] Task Manager Agent
- [ ] Note Keeper Agent
- [ ] Project Manager Agent
- [ ] Enhanced inter-agent communication

### Phase 4: Intelligence & Learning (Weeks 11-14)
- [ ] Learning from user interactions
- [ ] Automatic metadata enhancement
- [ ] Relationship discovery
- [ ] Personalization features

## License

Private project for personal use.