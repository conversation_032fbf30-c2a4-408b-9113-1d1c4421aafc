# Gmail API Setup Guide

## 🚀 **Quick Setup for Your Personal Email Assistant**

Follow these steps to connect your Gmail account to the intelligent email processing system.

### **Step 1: Create Google Cloud Project**

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Create Project" or select an existing project
3. Name your project (e.g., "Personal Email Assistant")
4. Click "Create"

### **Step 2: Enable Gmail API**

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Gmail API"
3. Click on "Gmail API" and click "Enable"

### **Step 3: Create Credentials**

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. If prompted, configure the OAuth consent screen:
   - Choose "External" user type
   - Fill in required fields:
     - App name: "Personal Email Assistant"
     - User support email: Your email
     - Developer contact: Your email
   - Add your email to test users
4. For OAuth client ID:
   - Application type: "Desktop application"
   - Name: "Gmail Integration"
5. Click "Create"
6. Download the JSON file

### **Step 4: Install Credentials**

1. Rename the downloaded file to `gmail_credentials.json`
2. Move it to the `config/` directory in your project:
   ```
   C:/Users/<USER>/dev/local/config/gmail_credentials.json
   ```

### **Step 5: Test the Integration**

1. Start your web app (if not already running):
   ```bash
   python -m src.api.web_app
   ```

2. Initialize Gmail integration:
   ```bash
   curl -X POST http://localhost:8000/api/gmail/initialize
   ```

3. This will open a browser window for Gmail authentication
4. Sign in with your Google account
5. Grant permissions to the app

### **Step 6: Test Email Processing**

1. Get your unread emails with intelligence analysis:
   ```bash
   curl -X GET http://localhost:8000/api/gmail/unread
   ```

2. Get your priority inbox:
   ```bash
   curl -X GET http://localhost:8000/api/gmail/priority-inbox
   ```

## 🎯 **What You'll Get**

Once set up, your system will:

- ✅ **Analyze all your emails** for priority, urgency, and sentiment
- ✅ **Extract action items** and questions automatically
- ✅ **Detect meeting requests** and urgent communications
- ✅ **Auto-label emails** by priority (Critical, High, Medium, Low)
- ✅ **Generate professional responses** based on your decisions
- ✅ **Learn from your preferences** over time

## 🔒 **Security & Privacy**

- Your credentials are stored locally in `data/gmail_token.pickle`
- No email content is sent to external servers
- All processing happens locally on your machine
- You can revoke access anytime in your Google Account settings

## 🛠 **Troubleshooting**

### **"Authentication failed" error:**
- Check that `config/gmail_credentials.json` exists and is valid
- Ensure Gmail API is enabled in Google Cloud Console
- Verify your OAuth consent screen is configured

### **"Permission denied" error:**
- Make sure you granted all requested permissions during authentication
- Check that your email is added as a test user in OAuth consent screen

### **"Quota exceeded" error:**
- Gmail API has daily limits (1 billion quota units/day for free tier)
- Each email read uses ~5-10 quota units
- This should be sufficient for personal use

## 📞 **Need Help?**

If you encounter issues:
1. Check the logs in your terminal
2. Verify all steps above are completed
3. Try re-authenticating by deleting `data/gmail_token.pickle` and running initialize again

## 🚀 **Next Steps**

Once Gmail integration is working:
1. **Set up automated processing** - Schedule regular email syncs
2. **Configure auto-responses** - Enable automatic replies for certain email types
3. **Customize labels** - Modify priority labels and organization rules
4. **Add calendar integration** - Sync meeting requests with your calendar

Your personal email assistant is ready to handle your busywork while you focus on important decisions!
