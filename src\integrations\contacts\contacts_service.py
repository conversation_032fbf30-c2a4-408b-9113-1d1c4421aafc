"""Contacts service for managing contact integrations."""

from typing import Dict, List, Optional, Any

from ...core.logging import logger
from ...core.config import settings
from .google_contacts import GoogleContactsClient


class ContactsService:
    """
    Main contacts service that coordinates contact operations across providers.
    """
    
    def __init__(self):
        self.config = settings.integrations.contacts
        self.google_client = None
        
    async def initialize(self) -> bool:
        """Initialize contacts service and authenticate with providers."""
        logger.info("Initializing contacts service...")

        # Initialize Google Contacts client
        if self.config.providers.google.enabled:
            try:
                self.google_client = GoogleContactsClient()
                if await self.google_client.authenticate():
                    logger.info("Successfully initialized Google Contacts")
                else:
                    logger.warning("Failed to authenticate Google Contacts")
            except Exception as e:
                logger.warning(f"Google Contacts not available: {e}")
                self.google_client = None
                # Don't fail the entire service if Google isn't available

        return True  # Always return True, individual providers can fail gracefully
    
    async def sync_contacts(self) -> Dict[str, Any]:
        """
        Sync contacts from all configured providers.
        
        Returns:
            Dictionary with sync results
        """
        logger.info("Starting contacts sync...")
        
        results = {
            'status': 'success',
            'providers_synced': 0,
            'total_contacts': 0,
            'new_contacts': 0,
            'updated_contacts': 0,
            'errors': []
        }
        
        try:
            if self.google_client:
                # Sync Google Contacts
                google_result = await self._sync_google_contacts()
                results['providers_synced'] += 1
                results['total_contacts'] += google_result.get('contacts_synced', 0)
                results['new_contacts'] += google_result.get('new_contacts', 0)
                results['updated_contacts'] += google_result.get('updated_contacts', 0)
                
                if google_result.get('errors'):
                    results['errors'].extend(google_result['errors'])
            
        except Exception as e:
            error_msg = f"Error during contacts sync: {e}"
            logger.error(error_msg)
            results['errors'].append(error_msg)
            results['status'] = 'error'
        
        logger.info(f"Contacts sync completed: {results['total_contacts']} contacts from {results['providers_synced']} providers")
        
        return results
    
    async def get_contacts(
        self,
        search_query: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Get contacts from all providers.
        
        Args:
            search_query: Optional search query to filter contacts
            limit: Maximum number of contacts to return
            
        Returns:
            List of contacts
        """
        logger.info(f"Getting contacts (query: {search_query}, limit: {limit})")
        
        all_contacts = []
        
        try:
            if self.google_client:
                if search_query:
                    # Search for specific contacts
                    google_contacts = await self.google_client.search_contacts(search_query)
                else:
                    # Get all contacts
                    google_contacts = await self.google_client.get_all_contacts()
                
                all_contacts.extend(google_contacts)
            
            # Apply limit if specified
            if limit and len(all_contacts) > limit:
                all_contacts = all_contacts[:limit]
            
            logger.info(f"Retrieved {len(all_contacts)} contacts")
            
            return all_contacts
            
        except Exception as e:
            logger.error(f"Error getting contacts: {e}")
            return []
    
    async def create_contact(
        self,
        name: str,
        email: Optional[str] = None,
        phone: Optional[str] = None,
        organization: Optional[str] = None,
        notes: Optional[str] = None,
        context: str = 'personal'
    ) -> Optional[Dict[str, Any]]:
        """
        Create a new contact.
        
        Args:
            name: Contact name
            email: Email address
            phone: Phone number
            organization: Organization/company
            notes: Additional notes
            context: Contact context ('personal' or 'work')
            
        Returns:
            Created contact data or None if failed
        """
        logger.info(f"Creating contact: {name}")
        
        if not self.google_client:
            logger.error("Google Contacts not initialized")
            return None
        
        try:
            contact = await self.google_client.create_contact(
                name=name,
                email=email,
                phone=phone,
                organization=organization,
                notes=notes
            )
            
            if contact:
                contact['context'] = context
                logger.info(f"Successfully created contact: {name}")
            
            return contact
            
        except Exception as e:
            logger.error(f"Error creating contact: {e}")
            return None
    
    async def search_contacts(self, query: str) -> List[Dict[str, Any]]:
        """
        Search contacts by name or email.
        
        Args:
            query: Search query
            
        Returns:
            List of matching contacts
        """
        logger.info(f"Searching contacts for: {query}")
        
        if not self.google_client:
            logger.error("Google Contacts not initialized")
            return []
        
        try:
            contacts = await self.google_client.search_contacts(query)
            
            logger.info(f"Found {len(contacts)} contacts matching '{query}'")
            
            return contacts
            
        except Exception as e:
            logger.error(f"Error searching contacts: {e}")
            return []
    
    async def get_contact_stats(self) -> Dict[str, Any]:
        """
        Get statistics about contacts.
        
        Returns:
            Dictionary with contact statistics
        """
        logger.info("Getting contact statistics...")
        
        stats = {
            'total_contacts': 0,
            'contacts_with_email': 0,
            'contacts_with_phone': 0,
            'contacts_with_organization': 0,
            'contexts': {'personal': 0, 'work': 0}
        }
        
        try:
            if self.google_client:
                contacts = await self.google_client.get_all_contacts()
                
                stats['total_contacts'] = len(contacts)
                
                for contact in contacts:
                    if contact.get('emails'):
                        stats['contacts_with_email'] += 1
                    
                    if contact.get('phones'):
                        stats['contacts_with_phone'] += 1
                    
                    if contact.get('organization'):
                        stats['contacts_with_organization'] += 1
                    
                    context = contact.get('context', 'personal')
                    if context in stats['contexts']:
                        stats['contexts'][context] += 1
            
            logger.info(f"Contact stats: {stats['total_contacts']} total contacts")
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting contact stats: {e}")
            return stats
    
    async def _sync_google_contacts(self) -> Dict[str, Any]:
        """Sync contacts from Google Contacts."""
        result = {
            'contacts_synced': 0,
            'new_contacts': 0,
            'updated_contacts': 0,
            'errors': []
        }
        
        try:
            # Get all contacts from Google
            contacts = await self.google_client.get_all_contacts()
            
            result['contacts_synced'] = len(contacts)
            result['new_contacts'] = len(contacts)  # For now, treat all as new
            
            logger.info(f"Synced {len(contacts)} contacts from Google Contacts")
            
            # TODO: Implement actual sync logic with local storage
            # This would involve:
            # 1. Comparing with existing contacts in local database
            # 2. Identifying new, updated, and deleted contacts
            # 3. Updating local storage accordingly
            # 4. Handling duplicate detection and merging
            
        except Exception as e:
            error_msg = f"Error syncing Google Contacts: {e}"
            logger.error(error_msg)
            result['errors'].append(error_msg)
        
        return result
    
    async def import_from_csv(self, csv_data: str) -> Dict[str, Any]:
        """
        Import contacts from CSV data.
        
        Args:
            csv_data: CSV content as string
            
        Returns:
            Import results
        """
        logger.info("Importing contacts from CSV")
        
        result = {
            'status': 'success',
            'imported_contacts': 0,
            'errors': []
        }
        
        try:
            # TODO: Implement CSV parsing and contact creation
            # This would involve:
            # 1. Parsing CSV data
            # 2. Mapping CSV columns to contact fields
            # 3. Creating contacts via Google Contacts API
            # 4. Handling duplicates and validation
            
            logger.info("CSV import functionality not yet implemented")
            result['status'] = 'not_implemented'
            
        except Exception as e:
            error_msg = f"Error importing CSV: {e}"
            logger.error(error_msg)
            result['errors'].append(error_msg)
            result['status'] = 'error'
        
        return result


# Global contacts service instance
contacts_service = ContactsService()
