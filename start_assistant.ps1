# Personal Assistant - PowerShell Start Script
# This script provides advanced startup functionality for the Personal Assistant

param(
    [switch]$NoOllama,      # Skip Ollama check
    [switch]$DevMode,       # Enable development mode
    [switch]$Verbose,       # Enable verbose output
    [string]$Port = "8000"  # Custom port
)

# Set console title
$Host.UI.RawUI.WindowTitle = "Personal Assistant - Starting..."

# Color functions
function Write-Header($text) {
    Write-Host "`n========================================" -ForegroundColor Cyan
    Write-Host "   $text" -ForegroundColor Yellow
    Write-Host "========================================`n" -ForegroundColor Cyan
}

function Write-Success($text) {
    Write-Host "✅ $text" -ForegroundColor Green
}

function Write-Warning($text) {
    Write-Host "⚠️  $text" -ForegroundColor Yellow
}

function Write-Error($text) {
    Write-Host "❌ $text" -ForegroundColor Red
}

function Write-Info($text) {
    Write-Host "ℹ️  $text" -ForegroundColor Blue
}

# Main startup function
function Start-PersonalAssistant {
    Write-Header "Personal Assistant - Starting..."
    
    # Change to script directory
    $scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
    Set-Location $scriptPath
    Write-Info "Working directory: $scriptPath"
    
    # Check Python installation
    Write-Info "Checking Python installation..."
    try {
        $pythonVersion = python --version 2>&1
        Write-Success "Python found: $pythonVersion"
    }
    catch {
        Write-Error "Python is not installed or not in PATH"
        Write-Host "Please install Python 3.8+ and try again" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    # Check/create virtual environment
    if (-not (Test-Path "venv")) {
        Write-Info "Creating virtual environment..."
        python -m venv venv
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to create virtual environment"
            Read-Host "Press Enter to exit"
            exit 1
        }
        Write-Success "Virtual environment created"
    } else {
        Write-Success "Virtual environment found"
    }
    
    # Activate virtual environment
    Write-Info "Activating virtual environment..."
    & "venv\Scripts\Activate.ps1"
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to activate virtual environment"
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Success "Virtual environment activated"
    
    # Install/update dependencies
    Write-Info "Installing dependencies..."
    if (Test-Path "requirements.txt") {
        Write-Info "This may take a few minutes on first run..."

        # Upgrade pip first
        pip install --upgrade pip

        # Install requirements
        pip install -r requirements.txt
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Dependencies installed successfully"
        } else {
            Write-Error "Failed to install dependencies"
            Write-Host "Please check your internet connection and try again" -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit 1
        }
    } else {
        Write-Warning "requirements.txt not found, skipping dependency installation"
    }
    
    # Create necessary directories
    Write-Info "Creating data directories..."
    $directories = @("data", "data\database", "data\archive", "data\models", "logs")
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Success "Created directory: $dir"
        }
    }
    
    # Check Ollama service (unless skipped)
    if (-not $NoOllama) {
        Write-Info "Checking Ollama service..."
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:11434/api/version" -TimeoutSec 5 -ErrorAction Stop
            Write-Success "Ollama service is running"
        }
        catch {
            Write-Warning "Ollama service not detected at localhost:11434"
            Write-Host "Some AI features may not work without Ollama running" -ForegroundColor Yellow
            Write-Host "You can start Ollama separately if needed" -ForegroundColor Yellow
        }
    }
    
    # Check if port is already in use
    Write-Info "Checking if port $Port is available..."
    $portInUse = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($portInUse) {
        Write-Warning "Port $Port is already in use"
        $choice = Read-Host "Do you want to stop the existing process? (y/n)"
        if ($choice -eq 'y' -or $choice -eq 'Y') {
            Stop-ProcessOnPort -Port $Port
        } else {
            Write-Error "Cannot start application - port $Port is in use"
            Read-Host "Press Enter to exit"
            exit 1
        }
    }
    
    # Set environment variables for development mode
    if ($DevMode) {
        $env:ASSISTANT_DEV_MODE = "true"
        $env:ASSISTANT_LOG_LEVEL = "DEBUG"
        Write-Info "Development mode enabled"
    }
    
    # Start the application
    Write-Header "Starting Personal Assistant..."
    Write-Host "The application will be available at:" -ForegroundColor Green
    Write-Host "  http://localhost:$Port" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Yellow
    Write-Header ""
    
    # Update console title
    $Host.UI.RawUI.WindowTitle = "Personal Assistant - Running on port $Port"
    
    # Start the web application
    try {
        if ($DevMode) {
            python -m src.api.web_app --reload --port $Port
        } else {
            python -m src.api.web_app --port $Port
        }
    }
    catch {
        Write-Error "Failed to start the application"
        Write-Host $_.Exception.Message -ForegroundColor Red
    }
    finally {
        Write-Header "Personal Assistant has stopped"
        $Host.UI.RawUI.WindowTitle = "Personal Assistant - Stopped"
        Read-Host "Press Enter to exit"
    }
}

function Stop-ProcessOnPort {
    param([string]$Port)
    
    Write-Info "Finding processes using port $Port..."
    $connections = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    
    foreach ($conn in $connections) {
        $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
        if ($process) {
            Write-Info "Stopping process: $($process.ProcessName) (PID: $($process.Id))"
            Stop-Process -Id $process.Id -Force
            Write-Success "Process stopped"
        }
    }
}

# Check if running as administrator for better port management
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Main execution
try {
    if (-not (Test-Administrator)) {
        Write-Warning "Running without administrator privileges"
        Write-Host "Some port management features may be limited" -ForegroundColor Yellow
    }
    
    Start-PersonalAssistant
}
catch {
    Write-Error "An unexpected error occurred: $($_.Exception.Message)"
    Write-Host $_.ScriptStackTrace -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
