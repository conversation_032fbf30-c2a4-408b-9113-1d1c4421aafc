"""Task management service."""

import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc

from ..core.logging import logger
from .database import get_db, TaskNotFoundError
from .models import Task, TaskStatus, TaskPriority, SourceType, LearningFeedback, EmailTask


class TaskService:
    """Service for managing tasks and task-related operations."""
    
    def __init__(self):
        self.logger = logger
    
    async def create_task_from_email(
        self,
        email_task: EmailTask,
        session: Optional[Session] = None
    ) -> Task:
        """Create a task from an EmailTask object."""
        if session is None:
            with next(get_db()) as session:
                return await self._create_task_from_email_impl(email_task, session)
        else:
            return await self._create_task_from_email_impl(email_task, session)
    
    async def _create_task_from_email_impl(
        self,
        email_task: EmailTask,
        session: Session
    ) -> Task:
        """Implementation of task creation from email."""
        try:
            # Create new task
            task = Task(
                title=email_task.title,
                description=email_task.description,
                status=TaskStatus.SUGGESTED.value,  # Start as suggested
                priority=email_task.priority,
                importance_score=email_task.importance_score,
                due_date=email_task.due_date,
                source_type=SourceType.EMAIL.value,
                source_id=email_task.source_email_id,
                keywords_found=email_task.keywords_found,
                extraction_confidence=0.8,  # Default confidence
                task_metadata=email_task.metadata
            )
            
            session.add(task)
            session.commit()
            session.refresh(task)
            
            self.logger.info(f"Created task from email: {task.title} (ID: {task.id})")
            return task
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error creating task from email: {e}")
            raise
    
    async def create_task(
        self,
        title: str,
        description: Optional[str] = None,
        priority: int = TaskPriority.MEDIUM.value,
        due_date: Optional[datetime] = None,
        source_type: str = SourceType.MANUAL.value,
        source_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        session: Optional[Session] = None
    ) -> Task:
        """Create a new task manually."""
        if session is None:
            with next(get_db()) as session:
                return await self._create_task_impl(
                    title, description, priority, due_date, 
                    source_type, source_id, metadata, session
                )
        else:
            return await self._create_task_impl(
                title, description, priority, due_date, 
                source_type, source_id, metadata, session
            )
    
    async def _create_task_impl(
        self,
        title: str,
        description: Optional[str],
        priority: int,
        due_date: Optional[datetime],
        source_type: str,
        source_id: Optional[str],
        metadata: Optional[Dict[str, Any]],
        session: Session
    ) -> Task:
        """Implementation of manual task creation."""
        try:
            task = Task(
                title=title,
                description=description,
                status=TaskStatus.PENDING.value,  # Manual tasks start as pending
                priority=priority,
                due_date=due_date,
                source_type=source_type,
                source_id=source_id,
                task_metadata=metadata or {}
            )
            
            session.add(task)
            session.commit()
            session.refresh(task)
            
            self.logger.info(f"Created manual task: {task.title} (ID: {task.id})")
            return task
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error creating manual task: {e}")
            raise
    
    async def get_task(self, task_id: Union[str, uuid.UUID], session: Optional[Session] = None) -> Task:
        """Get a task by ID."""
        if session is None:
            with next(get_db()) as session:
                return await self._get_task_impl(task_id, session)
        else:
            return await self._get_task_impl(task_id, session)
    
    async def _get_task_impl(self, task_id: Union[str, uuid.UUID], session: Session) -> Task:
        """Implementation of get task."""
        if isinstance(task_id, str):
            task_id = uuid.UUID(task_id)
            
        task = session.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise TaskNotFoundError(f"Task not found: {task_id}")
        
        return task
    
    async def get_tasks(
        self,
        status: Optional[str] = None,
        priority: Optional[int] = None,
        source_type: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
        order_by: str = "created_at",
        order_desc: bool = True,
        session: Optional[Session] = None
    ) -> List[Task]:
        """Get tasks with filtering and pagination."""
        if session is None:
            with next(get_db()) as session:
                return await self._get_tasks_impl(
                    status, priority, source_type, limit, offset, 
                    order_by, order_desc, session
                )
        else:
            return await self._get_tasks_impl(
                status, priority, source_type, limit, offset, 
                order_by, order_desc, session
            )
    
    async def _get_tasks_impl(
        self,
        status: Optional[str],
        priority: Optional[int],
        source_type: Optional[str],
        limit: int,
        offset: int,
        order_by: str,
        order_desc: bool,
        session: Session
    ) -> List[Task]:
        """Implementation of get tasks with filtering."""
        query = session.query(Task)
        
        # Apply filters
        if status:
            query = query.filter(Task.status == status)
        if priority:
            query = query.filter(Task.priority == priority)
        if source_type:
            query = query.filter(Task.source_type == source_type)
        
        # Apply ordering
        order_column = getattr(Task, order_by, Task.created_at)
        if order_desc:
            query = query.order_by(desc(order_column))
        else:
            query = query.order_by(asc(order_column))
        
        # Apply pagination
        query = query.offset(offset).limit(limit)
        
        return query.all()
    
    async def update_task(
        self,
        task_id: Union[str, uuid.UUID],
        updates: Dict[str, Any],
        session: Optional[Session] = None
    ) -> Task:
        """Update a task with learning feedback tracking."""
        if session is None:
            with next(get_db()) as session:
                return await self._update_task_impl(task_id, updates, session)
        else:
            return await self._update_task_impl(task_id, updates, session)
    
    async def _update_task_impl(
        self,
        task_id: Union[str, uuid.UUID],
        updates: Dict[str, Any],
        session: Session
    ) -> Task:
        """Implementation of task update with learning tracking."""
        try:
            task = await self._get_task_impl(task_id, session)
            
            # Track changes for learning
            original_values = {}
            for field, new_value in updates.items():
                if hasattr(task, field):
                    original_values[field] = getattr(task, field)
            
            # Apply updates
            for field, value in updates.items():
                if hasattr(task, field):
                    setattr(task, field, value)
            
            task.updated_at = datetime.now()
            
            # Mark as completed if status changed to completed
            if updates.get('status') == TaskStatus.COMPLETED.value:
                task.completed_at = datetime.now()
            
            session.commit()
            session.refresh(task)
            
            # Create learning feedback
            if original_values:
                await self._create_learning_feedback(
                    task.id, "task_update", original_values, updates, session
                )
            
            self.logger.info(f"Updated task: {task.title} (ID: {task.id})")
            return task
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error updating task {task_id}: {e}")
            raise
    
    async def delete_task(
        self,
        task_id: Union[str, uuid.UUID],
        session: Optional[Session] = None
    ) -> bool:
        """Delete a task (soft delete by marking as deleted)."""
        if session is None:
            with next(get_db()) as session:
                return await self._delete_task_impl(task_id, session)
        else:
            return await self._delete_task_impl(task_id, session)
    
    async def _delete_task_impl(self, task_id: Union[str, uuid.UUID], session: Session) -> bool:
        """Implementation of task deletion."""
        try:
            task = await self._get_task_impl(task_id, session)
            
            # Soft delete by changing status
            original_status = task.status
            task.status = TaskStatus.DELETED.value
            task.updated_at = datetime.now()
            
            session.commit()
            
            # Create learning feedback for deletion
            await self._create_learning_feedback(
                task.id, "task_deletion", 
                {"status": original_status}, 
                {"status": TaskStatus.DELETED.value}, 
                session
            )
            
            self.logger.info(f"Deleted task: {task.title} (ID: {task.id})")
            return True
            
        except Exception as e:
            session.rollback()
            self.logger.error(f"Error deleting task {task_id}: {e}")
            raise
    
    async def approve_suggested_task(
        self,
        task_id: Union[str, uuid.UUID],
        session: Optional[Session] = None
    ) -> Task:
        """Approve a suggested task (change status from suggested to pending)."""
        return await self.update_task(
            task_id, 
            {"status": TaskStatus.PENDING.value},
            session
        )
    
    async def reject_suggested_task(
        self,
        task_id: Union[str, uuid.UUID],
        session: Optional[Session] = None
    ) -> bool:
        """Reject a suggested task (mark as cancelled)."""
        await self.update_task(
            task_id,
            {"status": TaskStatus.CANCELLED.value},
            session
        )
        return True
    
    async def get_suggested_tasks(self, session: Optional[Session] = None) -> List[Task]:
        """Get all tasks with suggested status."""
        return await self.get_tasks(status=TaskStatus.SUGGESTED.value, session=session)
    
    async def get_overdue_tasks(self, session: Optional[Session] = None) -> List[Task]:
        """Get all overdue tasks."""
        if session is None:
            with next(get_db()) as session:
                return await self._get_overdue_tasks_impl(session)
        else:
            return await self._get_overdue_tasks_impl(session)
    
    async def _get_overdue_tasks_impl(self, session: Session) -> List[Task]:
        """Implementation of get overdue tasks."""
        now = datetime.now()
        return session.query(Task).filter(
            and_(
                Task.due_date < now,
                Task.status.in_([TaskStatus.PENDING.value, TaskStatus.IN_PROGRESS.value])
            )
        ).all()
    
    async def _create_learning_feedback(
        self,
        task_id: uuid.UUID,
        feedback_type: str,
        original_values: Dict[str, Any],
        new_values: Dict[str, Any],
        session: Session
    ) -> None:
        """Create learning feedback for task changes."""
        try:
            feedback = LearningFeedback(
                entity_type="task",
                entity_id=task_id,
                feedback_type=feedback_type,
                original_value=original_values,
                corrected_value=new_values,
                user_action="manual_edit",
                metadata={"timestamp": datetime.now().isoformat()}
            )
            
            session.add(feedback)
            session.commit()
            
        except Exception as e:
            self.logger.error(f"Error creating learning feedback: {e}")
            # Don't raise - learning feedback is not critical


# Global task service instance
task_service = TaskService()
