{% extends "base.html" %}

{% block content %}
<div class="calendar-page">
    <header class="page-header">
        <h1>Calendar</h1>
        <p class="page-subtitle">Manage your schedule and events</p>
    </header>

    <!-- Calendar Controls -->
    <section class="calendar-controls">
        <div class="controls-left">
            <button class="btn btn-primary" id="todayBtn">Today</button>
            <button class="btn btn-secondary" id="prevBtn">‹</button>
            <button class="btn btn-secondary" id="nextBtn">›</button>
            <h2 id="currentMonth">Loading...</h2>
        </div>
        
        <div class="controls-right">
            <select id="viewMode">
                <option value="month">Month</option>
                <option value="week">Week</option>
                <option value="day">Day</option>
            </select>
            <button class="btn btn-primary" id="newEventBtn">+ New Event</button>
        </div>
    </section>

    <!-- Calendar View -->
    <section class="calendar-view">
        <div class="calendar-container" id="calendarContainer">
            <div class="loading-placeholder">Loading calendar...</div>
        </div>
    </section>

    <!-- Upcoming Events -->
    <section class="upcoming-events">
        <h2>Upcoming Events</h2>
        <div class="events-list" id="upcomingEvents">
            <div class="loading-placeholder">Loading events...</div>
        </div>
    </section>
</div>

<!-- Event Modal -->
<div class="modal" id="eventModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="eventModalTitle">New Event</h2>
            <button class="modal-close" id="closeEventModal">×</button>
        </div>
        
        <div class="modal-body">
            <div class="form-group">
                <label for="eventTitle">Event Title</label>
                <input type="text" id="eventTitle" placeholder="Enter event title">
            </div>
            
            <div class="form-group">
                <label for="eventDescription">Description</label>
                <textarea id="eventDescription" rows="3" placeholder="Event description"></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="eventDate">Date</label>
                    <input type="date" id="eventDate">
                </div>
                
                <div class="form-group">
                    <label for="eventTime">Time</label>
                    <input type="time" id="eventTime">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="eventDuration">Duration (minutes)</label>
                    <input type="number" id="eventDuration" value="60" min="15" step="15">
                </div>
                
                <div class="form-group">
                    <label for="eventType">Type</label>
                    <select id="eventType">
                        <option value="meeting">Meeting</option>
                        <option value="appointment">Appointment</option>
                        <option value="task">Task</option>
                        <option value="reminder">Reminder</option>
                        <option value="personal">Personal</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="eventAllDay">
                    All Day Event
                </label>
            </div>
        </div>
        
        <div class="modal-footer">
            <button class="btn btn-primary" id="saveEventBtn">Save Event</button>
            <button class="btn btn-secondary" id="cancelEventBtn">Cancel</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.calendar-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.calendar-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.controls-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.controls-left h2 {
    margin: 0;
    color: var(--text-primary);
    margin-left: var(--spacing-lg);
}

.controls-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.controls-right select {
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.calendar-container {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    min-height: 600px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: var(--border-color);
    border: 1px solid var(--border-color);
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: var(--border-color);
    margin-bottom: 1px;
}

.day-header {
    background-color: var(--bg-tertiary);
    padding: var(--spacing-sm);
    text-align: center;
    font-weight: 600;
    color: var(--text-primary);
}

.calendar-day {
    background-color: var(--bg-primary);
    min-height: 100px;
    padding: var(--spacing-xs);
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;
}

.calendar-day:hover {
    background-color: var(--bg-secondary);
}

.calendar-day.other-month {
    background-color: var(--bg-tertiary);
    color: var(--text-muted);
}

.calendar-day.today {
    background-color: var(--accent-primary);
    color: white;
}

.calendar-day.selected {
    background-color: var(--accent-primary);
    color: white;
}

.day-number {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.day-events {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.event-item {
    background-color: var(--accent-primary);
    color: white;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: var(--font-size-xs);
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.event-meeting {
    background-color: #007bff;
}

.event-appointment {
    background-color: #28a745;
}

.event-task {
    background-color: #ffc107;
    color: #000;
}

.event-reminder {
    background-color: #fd7e14;
}

.event-personal {
    background-color: #6f42c1;
}

.upcoming-events {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
}

.events-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    max-height: 300px;
    overflow-y: auto;
}

.upcoming-event {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.event-info {
    flex: 1;
}

.event-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.event-time {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.event-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.event-action {
    padding: var(--spacing-xs);
    border: none;
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: all 0.2s ease;
}

.event-action:hover {
    background-color: var(--accent-primary);
    color: white;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    cursor: pointer;
    color: var(--text-secondary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.form-group input, .form-group textarea, .form-group select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

.loading-placeholder, .no-events {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class CalendarManager {
    constructor() {
        this.currentDate = new Date();
        this.selectedDate = null;
        this.currentEvent = null;
        this.events = [];
        this.viewMode = 'month';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.renderCalendar();
        this.loadEvents();
        this.loadUpcomingEvents();
    }

    setupEventListeners() {
        // Navigation
        document.getElementById('todayBtn').addEventListener('click', () => this.goToToday());
        document.getElementById('prevBtn').addEventListener('click', () => this.navigatePrev());
        document.getElementById('nextBtn').addEventListener('click', () => this.navigateNext());
        document.getElementById('viewMode').addEventListener('change', (e) => this.changeViewMode(e.target.value));

        // Event modal
        document.getElementById('newEventBtn').addEventListener('click', () => this.showEventModal());
        document.getElementById('closeEventModal').addEventListener('click', () => this.hideEventModal());
        document.getElementById('saveEventBtn').addEventListener('click', () => this.saveEvent());
        document.getElementById('cancelEventBtn').addEventListener('click', () => this.hideEventModal());

        // All day checkbox
        document.getElementById('eventAllDay').addEventListener('change', (e) => {
            const timeInput = document.getElementById('eventTime');
            const durationInput = document.getElementById('eventDuration');
            timeInput.disabled = e.target.checked;
            durationInput.disabled = e.target.checked;
        });

        // Close modal on outside click
        document.getElementById('eventModal').addEventListener('click', (e) => {
            if (e.target.id === 'eventModal') {
                this.hideEventModal();
            }
        });
    }

    renderCalendar() {
        const container = document.getElementById('calendarContainer');
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        
        document.getElementById('currentMonth').textContent = 
            `${monthNames[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;

        if (this.viewMode === 'month') {
            this.renderMonthView(container);
        } else if (this.viewMode === 'week') {
            this.renderWeekView(container);
        } else {
            this.renderDayView(container);
        }
    }

    renderMonthView(container) {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        
        let calendarHtml = `
            <div class="calendar-header">
                ${dayNames.map(day => `<div class="day-header">${day}</div>`).join('')}
            </div>
            <div class="calendar-grid">
        `;

        const today = new Date();
        const currentDate = new Date(startDate);

        for (let week = 0; week < 6; week++) {
            for (let day = 0; day < 7; day++) {
                const isCurrentMonth = currentDate.getMonth() === month;
                const isToday = currentDate.toDateString() === today.toDateString();
                const isSelected = this.selectedDate && currentDate.toDateString() === this.selectedDate.toDateString();
                
                const dayEvents = this.getEventsForDate(currentDate);
                const eventsHtml = dayEvents.slice(0, 3).map(event => 
                    `<div class="event-item event-${event.type}" onclick="event.stopPropagation(); calendarManager.editEvent('${event.id}')">${event.title}</div>`
                ).join('');
                
                const moreEvents = dayEvents.length > 3 ? `<div class="event-item">+${dayEvents.length - 3} more</div>` : '';

                calendarHtml += `
                    <div class="calendar-day ${!isCurrentMonth ? 'other-month' : ''} ${isToday ? 'today' : ''} ${isSelected ? 'selected' : ''}"
                         onclick="calendarManager.selectDate('${currentDate.toISOString()}')">
                        <div class="day-number">${currentDate.getDate()}</div>
                        <div class="day-events">
                            ${eventsHtml}
                            ${moreEvents}
                        </div>
                    </div>
                `;

                currentDate.setDate(currentDate.getDate() + 1);
            }
        }

        calendarHtml += '</div>';
        container.innerHTML = calendarHtml;
    }

    renderWeekView(container) {
        container.innerHTML = '<div class="loading-placeholder">Week view coming soon!</div>';
    }

    renderDayView(container) {
        container.innerHTML = '<div class="loading-placeholder">Day view coming soon!</div>';
    }

    getEventsForDate(date) {
        const dateStr = date.toDateString();
        return this.events.filter(event => {
            const eventDate = new Date(event.date);
            return eventDate.toDateString() === dateStr;
        });
    }

    selectDate(dateStr) {
        this.selectedDate = new Date(dateStr);
        this.renderCalendar();
    }

    goToToday() {
        this.currentDate = new Date();
        this.selectedDate = new Date();
        this.renderCalendar();
    }

    navigatePrev() {
        if (this.viewMode === 'month') {
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
        } else if (this.viewMode === 'week') {
            this.currentDate.setDate(this.currentDate.getDate() - 7);
        } else {
            this.currentDate.setDate(this.currentDate.getDate() - 1);
        }
        this.renderCalendar();
    }

    navigateNext() {
        if (this.viewMode === 'month') {
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
        } else if (this.viewMode === 'week') {
            this.currentDate.setDate(this.currentDate.getDate() + 7);
        } else {
            this.currentDate.setDate(this.currentDate.getDate() + 1);
        }
        this.renderCalendar();
    }

    changeViewMode(mode) {
        this.viewMode = mode;
        this.renderCalendar();
    }

    showEventModal(event = null) {
        this.currentEvent = event;
        
        if (event) {
            document.getElementById('eventModalTitle').textContent = 'Edit Event';
            document.getElementById('eventTitle').value = event.title || '';
            document.getElementById('eventDescription').value = event.description || '';
            document.getElementById('eventDate').value = event.date || '';
            document.getElementById('eventTime').value = event.time || '';
            document.getElementById('eventDuration').value = event.duration || 60;
            document.getElementById('eventType').value = event.type || 'meeting';
            document.getElementById('eventAllDay').checked = event.all_day || false;
        } else {
            document.getElementById('eventModalTitle').textContent = 'New Event';
            document.getElementById('eventTitle').value = '';
            document.getElementById('eventDescription').value = '';
            document.getElementById('eventDate').value = this.selectedDate ? this.selectedDate.toISOString().split('T')[0] : '';
            document.getElementById('eventTime').value = '';
            document.getElementById('eventDuration').value = 60;
            document.getElementById('eventType').value = 'meeting';
            document.getElementById('eventAllDay').checked = false;
        }
        
        document.getElementById('eventModal').style.display = 'flex';
        document.getElementById('eventTitle').focus();
    }

    hideEventModal() {
        document.getElementById('eventModal').style.display = 'none';
        this.currentEvent = null;
    }

    async saveEvent() {
        const eventData = {
            title: document.getElementById('eventTitle').value.trim(),
            description: document.getElementById('eventDescription').value.trim(),
            date: document.getElementById('eventDate').value,
            time: document.getElementById('eventTime').value,
            duration: parseInt(document.getElementById('eventDuration').value),
            type: document.getElementById('eventType').value,
            all_day: document.getElementById('eventAllDay').checked
        };

        if (!eventData.title) {
            app.showNotification('Event title is required', 'error');
            return;
        }

        if (!eventData.date) {
            app.showNotification('Event date is required', 'error');
            return;
        }

        try {
            let response;
            if (this.currentEvent) {
                response = await fetch(`/api/calendar/events/${this.currentEvent.id}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(eventData)
                });
            } else {
                response = await fetch('/api/calendar/events', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(eventData)
                });
            }

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Event saved successfully!', 'success');
                this.hideEventModal();
                await this.loadEvents();
                await this.loadUpcomingEvents();
                this.renderCalendar();
            } else {
                app.showNotification(`Error saving event: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error saving event: ${error.message}`, 'error');
        }
    }

    async loadEvents() {
        try {
            const response = await fetch('/api/calendar/events');
            const data = await response.json();

            if (data.status === 'success') {
                this.events = data.events || [];
                this.renderCalendar();
            }
        } catch (error) {
            console.error('Error loading events:', error);
        }
    }

    async loadUpcomingEvents() {
        try {
            const response = await fetch('/api/calendar/events/upcoming');
            const data = await response.json();

            const container = document.getElementById('upcomingEvents');

            if (data.status === 'success' && data.events && data.events.length > 0) {
                const eventsHtml = data.events.map(event => {
                    const eventDate = new Date(event.date);
                    const timeStr = event.all_day ? 'All Day' : event.time || 'No time';
                    
                    return `
                        <div class="upcoming-event">
                            <div class="event-info">
                                <div class="event-title">${event.title}</div>
                                <div class="event-time">${eventDate.toLocaleDateString()} - ${timeStr}</div>
                            </div>
                            <div class="event-actions">
                                <button class="event-action" onclick="calendarManager.editEvent('${event.id}')">✏️</button>
                                <button class="event-action" onclick="calendarManager.deleteEvent('${event.id}')">🗑️</button>
                            </div>
                        </div>
                    `;
                }).join('');

                container.innerHTML = eventsHtml;
            } else {
                container.innerHTML = '<div class="no-events">No upcoming events</div>';
            }
        } catch (error) {
            console.error('Error loading upcoming events:', error);
            document.getElementById('upcomingEvents').innerHTML = '<div class="no-events">Error loading events</div>';
        }
    }

    async editEvent(eventId) {
        try {
            const response = await fetch(`/api/calendar/events/${eventId}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.showEventModal(data.event);
            }
        } catch (error) {
            app.showNotification('Error loading event', 'error');
        }
    }

    async deleteEvent(eventId) {
        if (!confirm('Are you sure you want to delete this event?')) return;

        try {
            const response = await fetch(`/api/calendar/events/${eventId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Event deleted successfully!', 'success');
                await this.loadEvents();
                await this.loadUpcomingEvents();
                this.renderCalendar();
            } else {
                app.showNotification(`Error deleting event: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error deleting event: ${error.message}`, 'error');
        }
    }
}

// Initialize calendar manager
let calendarManager;
document.addEventListener('DOMContentLoaded', () => {
    calendarManager = new CalendarManager();
});
</script>
{% endblock %}
