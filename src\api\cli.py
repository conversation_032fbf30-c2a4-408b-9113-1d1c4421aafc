"""Command-line interface for the Personal Assistant."""

import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from ..core.config import settings
from ..core.logging import logger

console = Console()


@click.group()
@click.version_option(version="0.1.0")
def main():
    """Assistant - Your local knowledge librarian."""
    # Ensure directories exist
    settings.ensure_directories()

    # Display welcome message
    welcome_text = Text()
    welcome_text.append("Assistant", style="bold blue")
    welcome_text.append("\nYour local knowledge librarian powered by AI agents", style="dim")

    console.print(Panel(welcome_text, title="Welcome", border_style="blue"))


@main.command()
def status():
    """Show system status and configuration."""
    console.print("\n[bold]System Status[/bold]")
    console.print(f"• Ollama Host: {settings.ollama.host}")
    console.print(f"• Default Model: {settings.ollama.default_model}")
    console.print(f"• Vector DB: {settings.knowledge.vector_db.type}")
    console.print(f"• Metadata DB: {settings.knowledge.metadata_db.type}")
    
    console.print("\n[bold]Enabled Agents[/bold]")
    agents = settings.agents
    for agent_name in ["archivist", "research", "task_manager", "note_keeper", "project_manager"]:
        agent_config = getattr(agents, agent_name)
        status_icon = "✅" if agent_config.enabled else "❌"
        console.print(f"• {status_icon} {agent_name.replace('_', ' ').title()}")


@main.command()
@click.argument("file_path", type=click.Path(exists=True))
def ingest(file_path):
    """Ingest a document into the knowledge base."""
    console.print(f"[yellow]Ingesting document: {file_path}[/yellow]")
    # TODO: Implement document ingestion
    console.print("[red]Document ingestion not yet implemented[/red]")


@main.command()
@click.argument("query")
def search(query):
    """Search the knowledge base."""
    console.print(f"[yellow]Searching for: {query}[/yellow]")
    # TODO: Implement search functionality
    console.print("[red]Search functionality not yet implemented[/red]")


@main.command()
@click.argument("task")
def add_task(task):
    """Add a new task."""
    console.print(f"[yellow]Adding task: {task}[/yellow]")
    # TODO: Implement task management
    console.print("[red]Task management not yet implemented[/red]")


@main.command()
def list_tasks():
    """List all tasks."""
    console.print("[yellow]Listing tasks...[/yellow]")
    # TODO: Implement task listing
    console.print("[red]Task management not yet implemented[/red]")


@main.command()
def init():
    """Initialize the system and check dependencies."""
    console.print("[bold]Initializing Assistant...[/bold]")

    # Check Ollama connection
    console.print("• Checking Ollama connection...")
    # TODO: Implement Ollama connection check

    # Initialize databases
    console.print("• Initializing databases...")
    # TODO: Implement database initialization

    # Initialize email service
    console.print("• Initializing email service...")
    try:
        import asyncio
        from ..integrations.email.email_service import email_service

        async def init_email():
            return await email_service.initialize()

        success = asyncio.run(init_email())
        if success:
            console.print("[green]✅ Email service initialized[/green]")
        else:
            console.print("[yellow]⚠️ Email service initialization failed[/yellow]")
    except Exception as e:
        console.print(f"[red]❌ Email service error: {e}[/red]")

    # Check document processing dependencies
    console.print("• Checking document processing dependencies...")
    # TODO: Implement dependency checks

    console.print("[green]✅ Initialization complete![/green]")


@main.command()
@click.option('--force', is_flag=True, help='Force full sync (ignore last sync time)')
def sync_email(force):
    """Synchronize emails from all configured accounts."""
    console.print("[yellow]Starting email synchronization...[/yellow]")

    try:
        import asyncio
        from ..integrations.email.email_service import email_service

        async def sync():
            return await email_service.sync_all_emails(force_full_sync=force)

        result = asyncio.run(sync())

        if result['status'] == 'success':
            console.print(f"[green]✅ Email sync completed![/green]")
            console.print(f"• Processed {result['total_emails']} emails")
            console.print(f"• Created {result['new_tasks']} tasks")
            console.print(f"• Updated {result['new_contacts']} contacts")
            console.print(f"• Processing time: {result['processing_time']:.2f}s")

            if result['errors']:
                console.print(f"[yellow]⚠️ {len(result['errors'])} errors occurred[/yellow]")
        else:
            console.print(f"[red]❌ Email sync failed: {result.get('errors', ['Unknown error'])}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Email sync error: {e}[/red]")


@main.command()
def email_status():
    """Show email service status and configuration."""
    try:
        import asyncio
        from ..integrations.email.email_service import email_service

        async def get_status():
            return await email_service.get_sync_status()

        status = asyncio.run(get_status())

        console.print("\n[bold]Email Service Status[/bold]")
        console.print(f"• Processing: {'Yes' if status['is_processing'] else 'No'}")

        console.print("\n[bold]Configured Accounts[/bold]")
        accounts = status['configured_accounts']
        for account, enabled in accounts.items():
            icon = "✅" if enabled else "❌"
            console.print(f"• {icon} {account.replace('_', ' ').title()}")

        console.print("\n[bold]Last Sync Times[/bold]")
        sync_times = status['last_sync_times']
        if sync_times:
            for account, time in sync_times.items():
                console.print(f"• {account}: {time}")
        else:
            console.print("• No syncs performed yet")

        console.print("\n[bold]Configuration[/bold]")
        config = status['processing_config']
        console.print(f"• Check interval: {config['check_interval_minutes']} minutes")
        console.print(f"• Batch size: {config['batch_size']}")
        console.print(f"• Auto-create tasks: {'Yes' if config['auto_create_tasks'] else 'No'}")
        console.print(f"• Auto-tag emails: {'Yes' if config['auto_tag'] else 'No'}")

    except Exception as e:
        console.print(f"[red]❌ Error getting email status: {e}[/red]")


if __name__ == "__main__":
    main()
