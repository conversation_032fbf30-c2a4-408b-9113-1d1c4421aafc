{% extends "base.html" %}

{% block content %}
<div class="settings-page">
    <header class="page-header">
        <h1>Settings</h1>
        <p class="page-subtitle">Configure your personal assistant</p>
    </header>

    <!-- Settings Navigation -->
    <section class="settings-nav">
        <div class="nav-tabs">
            <button class="tab-btn active" data-tab="general">General</button>
            <button class="tab-btn" data-tab="integrations">Integrations</button>
            <button class="tab-btn" data-tab="ai">AI Settings</button>
            <button class="tab-btn" data-tab="privacy">Privacy</button>
            <button class="tab-btn" data-tab="advanced">Advanced</button>
        </div>
    </section>

    <!-- General Settings -->
    <section class="settings-panel active" id="general">
        <h2>General Settings</h2>
        
        <div class="setting-group">
            <h3>Appearance</h3>
            <div class="setting-item">
                <label for="theme">Theme</label>
                <select id="theme">
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                    <option value="auto">Auto (System)</option>
                </select>
            </div>
            
            <div class="setting-item">
                <label for="language">Language</label>
                <select id="language">
                    <option value="en">English</option>
                    <option value="es">Spanish</option>
                    <option value="fr">French</option>
                    <option value="de">German</option>
                </select>
            </div>
        </div>

        <div class="setting-group">
            <h3>Notifications</h3>
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="emailNotifications" checked>
                    Email notifications
                </label>
            </div>
            
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="taskReminders" checked>
                    Task reminders
                </label>
            </div>
            
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="calendarAlerts" checked>
                    Calendar alerts
                </label>
            </div>
        </div>
    </section>

    <!-- Integrations Settings -->
    <section class="settings-panel" id="integrations">
        <h2>Integrations</h2>
        
        <div class="integration-list">
            <div class="integration-item">
                <div class="integration-info">
                    <div class="integration-icon">📧</div>
                    <div class="integration-details">
                        <h3>Gmail</h3>
                        <p>Connect your Gmail account for email intelligence</p>
                        <span class="status" id="gmailStatus">Not connected</span>
                    </div>
                </div>
                <button class="btn btn-primary" id="connectGmail">Connect</button>
            </div>
            
            <div class="integration-item">
                <div class="integration-info">
                    <div class="integration-icon">📅</div>
                    <div class="integration-details">
                        <h3>Google Calendar</h3>
                        <p>Sync your calendar events and meetings</p>
                        <span class="status" id="calendarStatus">Not connected</span>
                    </div>
                </div>
                <button class="btn btn-primary" id="connectCalendar">Connect</button>
            </div>
            
            <div class="integration-item">
                <div class="integration-info">
                    <div class="integration-icon">👥</div>
                    <div class="integration-details">
                        <h3>Google Contacts</h3>
                        <p>Import and sync your contacts</p>
                        <span class="status" id="contactsStatus">Not connected</span>
                    </div>
                </div>
                <button class="btn btn-primary" id="connectContacts">Connect</button>
            </div>
        </div>
    </section>

    <!-- AI Settings -->
    <section class="settings-panel" id="ai">
        <h2>AI Settings</h2>
        
        <div class="setting-group">
            <h3>Email Intelligence</h3>
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="autoEmailAnalysis" checked>
                    Automatic email analysis
                </label>
            </div>
            
            <div class="setting-item">
                <label for="priorityThreshold">Priority threshold</label>
                <input type="range" id="priorityThreshold" min="0" max="100" value="70">
                <span id="priorityValue">70%</span>
            </div>
            
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="autoTaskCreation" checked>
                    Auto-create tasks from emails
                </label>
            </div>
        </div>

        <div class="setting-group">
            <h3>Response Generation</h3>
            <div class="setting-item">
                <label for="responseStyle">Default response style</label>
                <select id="responseStyle">
                    <option value="professional">Professional</option>
                    <option value="friendly">Friendly</option>
                    <option value="formal">Formal</option>
                    <option value="casual">Casual</option>
                </select>
            </div>
            
            <div class="setting-item">
                <label for="responseLength">Response length</label>
                <select id="responseLength">
                    <option value="short">Short</option>
                    <option value="medium">Medium</option>
                    <option value="detailed">Detailed</option>
                </select>
            </div>
        </div>
    </section>

    <!-- Privacy Settings -->
    <section class="settings-panel" id="privacy">
        <h2>Privacy & Security</h2>
        
        <div class="setting-group">
            <h3>Data Storage</h3>
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="localStorage" checked>
                    Store data locally only
                </label>
            </div>
            
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="encryptData" checked>
                    Encrypt sensitive data
                </label>
            </div>
        </div>

        <div class="setting-group">
            <h3>Data Retention</h3>
            <div class="setting-item">
                <label for="dataRetention">Keep data for</label>
                <select id="dataRetention">
                    <option value="30">30 days</option>
                    <option value="90">90 days</option>
                    <option value="365">1 year</option>
                    <option value="forever">Forever</option>
                </select>
            </div>
        </div>

        <div class="setting-group">
            <h3>Data Management</h3>
            <div class="setting-actions">
                <button class="btn btn-secondary" id="exportData">Export Data</button>
                <button class="btn btn-danger" id="clearData">Clear All Data</button>
            </div>
        </div>
    </section>

    <!-- Advanced Settings -->
    <section class="settings-panel" id="advanced">
        <h2>Advanced Settings</h2>
        
        <div class="setting-group">
            <h3>Performance</h3>
            <div class="setting-item">
                <label for="maxDocuments">Max documents to index</label>
                <input type="number" id="maxDocuments" value="1000" min="100" max="10000">
            </div>
            
            <div class="setting-item">
                <label for="syncInterval">Sync interval (minutes)</label>
                <input type="number" id="syncInterval" value="15" min="5" max="60">
            </div>
        </div>

        <div class="setting-group">
            <h3>Debug</h3>
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="debugMode">
                    Enable debug mode
                </label>
            </div>
            
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="verboseLogging">
                    Verbose logging
                </label>
            </div>
        </div>

        <div class="setting-group">
            <h3>System</h3>
            <div class="setting-actions">
                <button class="btn btn-secondary" id="resetSettings">Reset to Defaults</button>
                <button class="btn btn-secondary" id="downloadLogs">Download Logs</button>
            </div>
        </div>
    </section>

    <!-- Save Button -->
    <div class="settings-footer">
        <button class="btn btn-primary btn-large" id="saveSettings">Save Settings</button>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.settings-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.settings-nav {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
}

.nav-tabs {
    display: flex;
    gap: var(--spacing-xs);
}

.tab-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    background-color: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.tab-btn.active {
    background-color: var(--accent-primary);
    color: white;
}

.settings-panel {
    display: none;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-xl);
}

.settings-panel.active {
    display: block;
}

.settings-panel h2 {
    margin: 0 0 var(--spacing-xl) 0;
    color: var(--text-primary);
}

.setting-group {
    margin-bottom: var(--spacing-xxl);
}

.setting-group h3 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-sm);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-sm);
}

.setting-item label {
    font-weight: 500;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.setting-item input, .setting-item select {
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.setting-item input[type="range"] {
    width: 150px;
}

.setting-item input[type="checkbox"] {
    width: auto;
}

.integration-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.integration-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.integration-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.integration-icon {
    font-size: var(--font-size-xxl);
}

.integration-details h3 {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-primary);
}

.integration-details p {
    margin: 0 0 var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.status {
    font-size: var(--font-size-xs);
    padding: 2px 8px;
    border-radius: 12px;
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

.status.connected {
    background-color: #28a745;
    color: white;
}

.setting-actions {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

.settings-footer {
    display: flex;
    justify-content: center;
    padding: var(--spacing-xl);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-xxl);
    font-size: var(--font-size-lg);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class SettingsManager {
    constructor() {
        this.settings = {};
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSettings();
        this.checkIntegrationStatus();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // Settings inputs
        document.getElementById('priorityThreshold').addEventListener('input', (e) => {
            document.getElementById('priorityValue').textContent = e.target.value + '%';
        });

        // Integration buttons
        document.getElementById('connectGmail').addEventListener('click', () => this.connectIntegration('gmail'));
        document.getElementById('connectCalendar').addEventListener('click', () => this.connectIntegration('calendar'));
        document.getElementById('connectContacts').addEventListener('click', () => this.connectIntegration('contacts'));

        // Action buttons
        document.getElementById('saveSettings').addEventListener('click', () => this.saveSettings());
        document.getElementById('exportData').addEventListener('click', () => this.exportData());
        document.getElementById('clearData').addEventListener('click', () => this.clearData());
        document.getElementById('resetSettings').addEventListener('click', () => this.resetSettings());
        document.getElementById('downloadLogs').addEventListener('click', () => this.downloadLogs());
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update panels
        document.querySelectorAll('.settings-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
    }

    async loadSettings() {
        try {
            const response = await fetch('/api/settings');
            const data = await response.json();

            if (data.status === 'success') {
                this.settings = data.settings;
                this.populateSettings();
            }
        } catch (error) {
            console.error('Error loading settings:', error);
            // Load defaults
            this.loadDefaultSettings();
        }
    }

    populateSettings() {
        // General settings
        document.getElementById('theme').value = this.settings.theme || 'light';
        document.getElementById('language').value = this.settings.language || 'en';
        document.getElementById('emailNotifications').checked = this.settings.email_notifications !== false;
        document.getElementById('taskReminders').checked = this.settings.task_reminders !== false;
        document.getElementById('calendarAlerts').checked = this.settings.calendar_alerts !== false;

        // AI settings
        document.getElementById('autoEmailAnalysis').checked = this.settings.auto_email_analysis !== false;
        document.getElementById('priorityThreshold').value = this.settings.priority_threshold || 70;
        document.getElementById('priorityValue').textContent = (this.settings.priority_threshold || 70) + '%';
        document.getElementById('autoTaskCreation').checked = this.settings.auto_task_creation !== false;
        document.getElementById('responseStyle').value = this.settings.response_style || 'professional';
        document.getElementById('responseLength').value = this.settings.response_length || 'medium';

        // Privacy settings
        document.getElementById('localStorage').checked = this.settings.local_storage !== false;
        document.getElementById('encryptData').checked = this.settings.encrypt_data !== false;
        document.getElementById('dataRetention').value = this.settings.data_retention || 'forever';

        // Advanced settings
        document.getElementById('maxDocuments').value = this.settings.max_documents || 1000;
        document.getElementById('syncInterval').value = this.settings.sync_interval || 15;
        document.getElementById('debugMode').checked = this.settings.debug_mode || false;
        document.getElementById('verboseLogging').checked = this.settings.verbose_logging || false;
    }

    loadDefaultSettings() {
        this.settings = {
            theme: 'light',
            language: 'en',
            email_notifications: true,
            task_reminders: true,
            calendar_alerts: true,
            auto_email_analysis: true,
            priority_threshold: 70,
            auto_task_creation: true,
            response_style: 'professional',
            response_length: 'medium',
            local_storage: true,
            encrypt_data: true,
            data_retention: 'forever',
            max_documents: 1000,
            sync_interval: 15,
            debug_mode: false,
            verbose_logging: false
        };
        this.populateSettings();
    }

    async checkIntegrationStatus() {
        try {
            const response = await fetch('/api/integrations/status');
            const data = await response.json();

            if (data.status === 'success') {
                this.updateIntegrationStatus('gmail', data.gmail);
                this.updateIntegrationStatus('calendar', data.calendar);
                this.updateIntegrationStatus('contacts', data.contacts);
            }
        } catch (error) {
            console.error('Error checking integration status:', error);
        }
    }

    updateIntegrationStatus(integration, status) {
        const statusElement = document.getElementById(`${integration}Status`);
        const buttonElement = document.getElementById(`connect${integration.charAt(0).toUpperCase() + integration.slice(1)}`);
        
        if (status && status.connected) {
            statusElement.textContent = 'Connected';
            statusElement.classList.add('connected');
            buttonElement.textContent = 'Disconnect';
            buttonElement.classList.remove('btn-primary');
            buttonElement.classList.add('btn-secondary');
        } else {
            statusElement.textContent = 'Not connected';
            statusElement.classList.remove('connected');
            buttonElement.textContent = 'Connect';
            buttonElement.classList.remove('btn-secondary');
            buttonElement.classList.add('btn-primary');
        }
    }

    async connectIntegration(integration) {
        try {
            const response = await fetch(`/api/integrations/${integration}/connect`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.status === 'success') {
                if (data.auth_url) {
                    // Redirect to OAuth
                    window.location.href = data.auth_url;
                } else {
                    app.showNotification(`${integration} connected successfully!`, 'success');
                    this.checkIntegrationStatus();
                }
            } else {
                app.showNotification(`Error connecting ${integration}: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error connecting ${integration}: ${error.message}`, 'error');
        }
    }

    async saveSettings() {
        const settingsData = {
            theme: document.getElementById('theme').value,
            language: document.getElementById('language').value,
            email_notifications: document.getElementById('emailNotifications').checked,
            task_reminders: document.getElementById('taskReminders').checked,
            calendar_alerts: document.getElementById('calendarAlerts').checked,
            auto_email_analysis: document.getElementById('autoEmailAnalysis').checked,
            priority_threshold: parseInt(document.getElementById('priorityThreshold').value),
            auto_task_creation: document.getElementById('autoTaskCreation').checked,
            response_style: document.getElementById('responseStyle').value,
            response_length: document.getElementById('responseLength').value,
            local_storage: document.getElementById('localStorage').checked,
            encrypt_data: document.getElementById('encryptData').checked,
            data_retention: document.getElementById('dataRetention').value,
            max_documents: parseInt(document.getElementById('maxDocuments').value),
            sync_interval: parseInt(document.getElementById('syncInterval').value),
            debug_mode: document.getElementById('debugMode').checked,
            verbose_logging: document.getElementById('verboseLogging').checked
        };

        try {
            const response = await fetch('/api/settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settingsData)
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Settings saved successfully!', 'success');
                this.settings = settingsData;
                
                // Apply theme change immediately
                if (settingsData.theme !== 'auto') {
                    document.documentElement.setAttribute('data-theme', settingsData.theme);
                    localStorage.setItem('theme', settingsData.theme);
                }
            } else {
                app.showNotification(`Error saving settings: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error saving settings: ${error.message}`, 'error');
        }
    }

    async exportData() {
        try {
            const response = await fetch('/api/data/export');
            const blob = await response.blob();
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `assistant-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            app.showNotification('Data exported successfully!', 'success');
        } catch (error) {
            app.showNotification(`Error exporting data: ${error.message}`, 'error');
        }
    }

    async clearData() {
        if (!confirm('Are you sure you want to clear all data? This action cannot be undone.')) return;
        
        if (!confirm('This will delete all your documents, notes, tasks, and settings. Are you absolutely sure?')) return;

        try {
            const response = await fetch('/api/data/clear', {
                method: 'POST'
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('All data cleared successfully!', 'success');
                // Reload page to reset everything
                setTimeout(() => window.location.reload(), 2000);
            } else {
                app.showNotification(`Error clearing data: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error clearing data: ${error.message}`, 'error');
        }
    }

    async resetSettings() {
        if (!confirm('Reset all settings to defaults?')) return;

        this.loadDefaultSettings();
        app.showNotification('Settings reset to defaults. Click Save to apply.', 'info');
    }

    async downloadLogs() {
        try {
            const response = await fetch('/api/logs/download');
            const blob = await response.blob();
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `assistant-logs-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            app.showNotification('Logs downloaded successfully!', 'success');
        } catch (error) {
            app.showNotification(`Error downloading logs: ${error.message}`, 'error');
        }
    }
}

// Initialize settings manager
let settingsManager;
document.addEventListener('DOMContentLoaded', () => {
    settingsManager = new SettingsManager();
});
</script>
{% endblock %}
