/* Assistant - Main Stylesheet */

:root {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --accent-primary: #007bff;
    --accent-secondary: #6c757d;
    --border-color: #dee2e6;
    --shadow: rgba(0, 0, 0, 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-xxl: 1.5rem;
    --font-size-xxxl: 2rem;
    
    /* Layout */
    --navbar-height: 60px;
    --container-max-width: 1200px;
    --border-radius: 8px;
    --border-radius-sm: 4px;
}

/* Dark theme */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;
    --accent-primary: #4dabf7;
    --border-color: #404040;
    --shadow: rgba(0, 0, 0, 0.3);
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Layout */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.main-content {
    min-height: calc(100vh - var(--navbar-height) - 60px);
    padding: var(--spacing-xl) 0;
}

/* Navigation */
.navbar {
    height: var(--navbar-height);
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.nav-brand .nav-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-lg);
}

.logo-icon {
    margin-right: var(--spacing-sm);
    font-size: var(--font-size-xl);
}

.nav-menu {
    display: flex;
    gap: var(--spacing-sm);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    text-decoration: none;
    color: var(--text-secondary);
    border-radius: var(--border-radius-sm);
    transition: all 0.2s ease;
    font-size: var(--font-size-sm);
}

.nav-link:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-link.active {
    background-color: var(--accent-primary);
    color: white;
}

.nav-icon {
    margin-right: var(--spacing-xs);
}

.theme-toggle {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.theme-toggle:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Dashboard */
.dashboard {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xxl);
}

.page-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.page-header h1 {
    font-size: var(--font-size-xxxl);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* Status Grid */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

.status-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow);
}

.status-icon {
    font-size: var(--font-size-xxxl);
}

.status-content h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.status-number {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--accent-primary);
    margin-bottom: var(--spacing-xs);
}

.status-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Action Grid */
.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.action-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.2s ease;
    text-align: center;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow);
    border-color: var(--accent-primary);
}

.action-icon {
    font-size: var(--font-size-xxxl);
    margin-bottom: var(--spacing-md);
}

.action-card h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.action-card p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Agent Grid */
.agent-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.agent-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    text-align: center;
    transition: all 0.2s ease;
}

.agent-card.enabled {
    border-color: #28a745;
}

.agent-card.disabled {
    opacity: 0.6;
}

.agent-icon {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-sm);
}

.agent-card h3 {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-xs);
}

.agent-status {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* Footer */
.footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg) 0;
    text-align: center;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-menu {
        display: none; /* TODO: Add mobile menu */
    }
    
    .status-grid,
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 0 var(--spacing-sm);
    }
}
