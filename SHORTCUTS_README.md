# Personal Assistant - Desktop Shortcuts Guide

This guide explains how to set up and use desktop shortcuts for the Personal Assistant application.

## 🚀 Quick Setup

### Option 1: Automatic Setup (Recommended)
1. Double-click `setup_shortcuts.bat`
2. Follow the prompts
3. Shortcuts will be created on your desktop automatically

### Option 2: Manual PowerShell Setup
1. Right-click `create_desktop_shortcuts.ps1`
2. Select "Run with PowerShell"
3. If prompted about execution policy, type `Y` and press Enter

### Option 3: Manual Batch Setup
1. Copy the `.bat` files to your desktop manually
2. Create shortcuts to them as needed

## 📁 Available Files

### Startup Scripts
- **`start_assistant.bat`** - Simple batch file to start the application
- **`start_assistant.ps1`** - Advanced PowerShell script with more features
- **`stop_assistant.bat`** - Simple batch file to stop the application  
- **`stop_assistant.ps1`** - Advanced PowerShell script to stop the application

### Setup Scripts
- **`setup_shortcuts.bat`** - Automatically creates desktop shortcuts
- **`create_desktop_shortcuts.ps1`** - PowerShell script for advanced shortcut creation

## 🖥️ Desktop Shortcuts Created

After running the setup, you'll have these shortcuts on your desktop:

### 🟢 Start Personal Assistant
- **Purpose**: Starts the Personal Assistant application
- **What it does**: 
  - Activates Python virtual environment
  - Installs/updates dependencies
  - Checks for Ollama service
  - Starts the web server on port 8000
- **Usage**: Double-click to start the application

### 🔴 Stop Personal Assistant  
- **Purpose**: Stops the Personal Assistant application
- **What it does**:
  - Finds and stops Python processes running the application
  - Frees up port 8000
  - Gracefully shuts down the service
- **Usage**: Double-click to stop the application

### 🌐 Open Personal Assistant
- **Purpose**: Opens the application in your web browser
- **What it does**: Opens http://localhost:8000 in your default browser
- **Usage**: Double-click after starting the application

### 📂 Personal Assistant Folder
- **Purpose**: Opens the application folder
- **What it does**: Opens the folder containing all application files
- **Usage**: Double-click to access logs, data, and configuration files

### 🔧 Start Personal Assistant (Batch)
- **Purpose**: Alternative starter for systems with PowerShell restrictions
- **What it does**: Same as the main starter but uses batch files only
- **Usage**: Use if the PowerShell version doesn't work

## 📋 Usage Instructions

### Starting the Application
1. **Double-click** "Start Personal Assistant" on your desktop
2. **Wait** for the console window to show "Starting Personal Assistant..."
3. **Look for** the message "The application will be available at: http://localhost:8000"
4. **Double-click** "Open Personal Assistant" to use the web interface

### Using the Application
- The web interface will open in your default browser
- Navigate through the different features using the web UI
- All data is stored locally on your computer

### Stopping the Application
1. **Double-click** "Stop Personal Assistant" on your desktop
2. **Wait** for confirmation that processes have been stopped
3. **Close** any remaining console windows

## ⚙️ Advanced Options

### PowerShell Script Parameters

The PowerShell scripts support additional parameters:

#### Start Script (`start_assistant.ps1`)
```powershell
# Start with custom port
.\start_assistant.ps1 -Port 9000

# Start in development mode
.\start_assistant.ps1 -DevMode

# Skip Ollama check
.\start_assistant.ps1 -NoOllama

# Enable verbose output
.\start_assistant.ps1 -Verbose
```

#### Stop Script (`stop_assistant.ps1`)
```powershell
# Force stop all Python processes
.\stop_assistant.ps1 -Force

# Stop with verbose output
.\stop_assistant.ps1 -Verbose

# Stop application on custom port
.\stop_assistant.ps1 -Port 9000
```

### Creating Shortcuts for All Users
To create shortcuts for all users on the computer (requires administrator privileges):

```powershell
# Run as Administrator
.\create_desktop_shortcuts.ps1 -AllUsers -Force
```

## 🔧 Troubleshooting

### Common Issues

#### "PowerShell execution policy" error
**Solution**: Run this command in PowerShell as Administrator:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### "Python not found" error
**Solution**: 
1. Install Python 3.8+ from https://python.org
2. Make sure "Add Python to PATH" is checked during installation
3. Restart your computer

#### "Port 8000 already in use" error
**Solution**:
1. Run the stop script first
2. Or use a different port: `.\start_assistant.ps1 -Port 9000`

#### Application doesn't start
**Solution**:
1. Check the console output for error messages
2. Ensure you have internet connection for dependency installation
3. Try running `start_assistant.bat` directly to see detailed errors

#### Shortcuts don't work
**Solution**:
1. Re-run `setup_shortcuts.bat` as Administrator
2. Check that the script files exist in the application folder
3. Manually create shortcuts pointing to the `.bat` files

### Getting Help

If you encounter issues:

1. **Check the console output** - Error messages provide helpful information
2. **Look at log files** - Check the `logs/` folder for detailed error logs
3. **Try the batch version** - If PowerShell scripts fail, use the `.bat` files
4. **Run as Administrator** - Some operations may require elevated privileges

## 📝 Customization

### Changing the Default Port
Edit the scripts and change `8000` to your preferred port number.

### Adding Custom Icons
You can change the shortcut icons by:
1. Right-clicking the shortcut
2. Selecting "Properties"
3. Clicking "Change Icon"
4. Choosing a new icon

### Creating Additional Shortcuts
You can create shortcuts to specific features:
- Logs folder: `%APPDATA%\Personal Assistant\logs`
- Data folder: `%APPDATA%\Personal Assistant\data`
- Configuration: `config\settings.yaml`

## 🔒 Security Notes

- The application runs locally on your computer
- No data is sent to external servers (except for Ollama if configured)
- All processing happens on your local machine
- You can run the application completely offline (except for initial dependency installation)

## 🆘 Emergency Stop

If the application becomes unresponsive:

1. **Use the stop shortcut** - Try the "Stop Personal Assistant" shortcut first
2. **Force stop via PowerShell** - Run: `.\stop_assistant.ps1 -Force`
3. **Task Manager** - Open Task Manager and end Python processes
4. **Command line** - Run: `taskkill /f /im python.exe`

---

**Enjoy using your Personal Assistant! 🎉**
