"""Gmail API client for email integration."""

import base64
import json
import pickle
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from ...core.logging import logger
from ...core.config import settings


class GmailClient:
    """
    Gmail API client for accessing and processing emails.
    
    Handles OAuth2 authentication, email fetching, and basic operations
    for both personal and work Gmail accounts.
    """
    
    # Gmail API scopes needed
    SCOPES = [
        'https://www.googleapis.com/auth/gmail.readonly',
        'https://www.googleapis.com/auth/gmail.modify',  # For adding labels/tags
        'https://www.googleapis.com/auth/gmail.labels'   # For managing labels
    ]
    
    def __init__(self, account_type: str = "personal"):
        """
        Initialize Gmail client for specific account type.
        
        Args:
            account_type: "personal" or "work"
        """
        self.account_type = account_type
        self.config = settings.integrations.email.providers.gmail.accounts

        if not hasattr(self.config, account_type):
            raise ValueError(f"Account type '{account_type}' not configured")

        self.account_config = getattr(self.config, account_type)

        # Check if the account config is None (not configured in settings.yaml)
        if self.account_config is None:
            raise ValueError(f"Account type '{account_type}' not configured")
        self.credentials_file = self.account_config.credentials_file
        self.token_file = self.account_config.token_file
        self.context = self.account_config.context
        
        self.service = None
        self.credentials = None
        
    async def authenticate(self) -> bool:
        """
        Authenticate with Gmail API using OAuth2.
        
        Returns:
            True if authentication successful, False otherwise
        """
        try:
            # Load existing credentials if available
            if Path(self.token_file).exists():
                with open(self.token_file, 'rb') as token:
                    self.credentials = pickle.load(token)
            
            # If there are no valid credentials, get new ones
            if not self.credentials or not self.credentials.valid:
                if self.credentials and self.credentials.expired and self.credentials.refresh_token:
                    logger.info(f"Refreshing {self.account_type} Gmail credentials")
                    self.credentials.refresh(Request())
                else:
                    logger.info(f"Starting OAuth flow for {self.account_type} Gmail")
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_file, self.SCOPES
                    )
                    self.credentials = flow.run_local_server(port=0)
                
                # Save credentials for next run
                Path(self.token_file).parent.mkdir(parents=True, exist_ok=True)
                with open(self.token_file, 'wb') as token:
                    pickle.dump(self.credentials, token)
            
            # Build the Gmail service
            self.service = build('gmail', 'v1', credentials=self.credentials)
            
            # Test the connection
            profile = self.service.users().getProfile(userId='me').execute()
            logger.info(f"Successfully authenticated {self.account_type} Gmail: {profile.get('emailAddress')}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to authenticate {self.account_type} Gmail: {e}")
            return False
    
    async def get_messages(
        self,
        query: str = "in:inbox",
        max_results: int = 100,
        since_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """
        Get messages from Gmail based on query.
        
        Args:
            query: Gmail search query (default: inbox only)
            max_results: Maximum number of messages to retrieve
            since_date: Only get messages since this date
            
        Returns:
            List of message dictionaries with full content
        """
        if not self.service:
            if not await self.authenticate():
                return []
        
        try:
            # Build query with date filter if provided
            if since_date:
                date_str = since_date.strftime('%Y/%m/%d')
                query = f"{query} after:{date_str}"
            
            logger.info(f"Fetching {self.account_type} Gmail messages with query: {query}")
            
            # Get message list
            result = self.service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results
            ).execute()
            
            messages = result.get('messages', [])
            logger.info(f"Found {len(messages)} messages")
            
            # Get full message details
            full_messages = []
            for message in messages:
                try:
                    full_message = await self._get_message_details(message['id'])
                    if full_message:
                        full_messages.append(full_message)
                except Exception as e:
                    logger.error(f"Error getting message {message['id']}: {e}")
                    continue
            
            logger.info(f"Successfully retrieved {len(full_messages)} full messages")
            return full_messages
            
        except HttpError as e:
            logger.error(f"Gmail API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching messages: {e}")
            return []
    
    async def _get_message_details(self, message_id: str) -> Optional[Dict[str, Any]]:
        """Get full details for a specific message."""
        try:
            message = self.service.users().messages().get(
                userId='me',
                id=message_id,
                format='full'
            ).execute()
            
            # Parse message into our standard format
            parsed_message = self._parse_message(message)
            parsed_message['account_type'] = self.account_type
            parsed_message['context'] = self.context
            
            return parsed_message
            
        except Exception as e:
            logger.error(f"Error getting message details for {message_id}: {e}")
            return None
    
    def _parse_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Gmail message into our standard format."""
        headers = {h['name']: h['value'] for h in message['payload'].get('headers', [])}
        
        # Extract basic information
        parsed = {
            'message_id': message['id'],
            'thread_id': message.get('threadId'),
            'subject': headers.get('Subject', ''),
            'sender_email': self._extract_email(headers.get('From', '')),
            'sender_name': self._extract_name(headers.get('From', '')),
            'recipients': self._parse_recipients(headers.get('To', '')),
            'cc_recipients': self._parse_recipients(headers.get('Cc', '')),
            'bcc_recipients': self._parse_recipients(headers.get('Bcc', '')),
            'received_date': self._parse_date(headers.get('Date')),
            'labels': message.get('labelIds', []),
            'snippet': message.get('snippet', ''),
            'body_text': '',
            'body_html': '',
            'has_attachments': False,
            'is_read': 'UNREAD' not in message.get('labelIds', []),
            'raw_message': message  # Keep original for reference
        }
        
        # Extract body content
        body_text, body_html, has_attachments = self._extract_body(message['payload'])
        parsed['body_text'] = body_text
        parsed['body_html'] = body_html
        parsed['has_attachments'] = has_attachments
        
        return parsed
    
    def _extract_email(self, from_header: str) -> str:
        """Extract email address from From header."""
        if '<' in from_header and '>' in from_header:
            return from_header.split('<')[1].split('>')[0].strip()
        return from_header.strip()
    
    def _extract_name(self, from_header: str) -> str:
        """Extract name from From header."""
        if '<' in from_header:
            return from_header.split('<')[0].strip().strip('"')
        return ''
    
    def _parse_recipients(self, recipients_header: str) -> List[str]:
        """Parse recipients from To/Cc/Bcc headers."""
        if not recipients_header:
            return []
        
        recipients = []
        for recipient in recipients_header.split(','):
            email = self._extract_email(recipient.strip())
            if email:
                recipients.append(email)
        
        return recipients
    
    def _parse_date(self, date_header: str) -> Optional[datetime]:
        """Parse date from email header."""
        if not date_header:
            return None
        
        try:
            # Gmail dates are in RFC 2822 format
            from email.utils import parsedate_to_datetime
            return parsedate_to_datetime(date_header)
        except Exception as e:
            logger.warning(f"Could not parse date '{date_header}': {e}")
            return None
    
    def _extract_body(self, payload: Dict[str, Any]) -> Tuple[str, str, bool]:
        """Extract text and HTML body from message payload."""
        body_text = ""
        body_html = ""
        has_attachments = False
        
        def extract_parts(part):
            nonlocal body_text, body_html, has_attachments
            
            if part.get('filename'):
                has_attachments = True
            
            if part.get('mimeType') == 'text/plain':
                data = part.get('body', {}).get('data')
                if data:
                    body_text += base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
            
            elif part.get('mimeType') == 'text/html':
                data = part.get('body', {}).get('data')
                if data:
                    body_html += base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
            
            # Recursively process multipart messages
            if 'parts' in part:
                for subpart in part['parts']:
                    extract_parts(subpart)
        
        extract_parts(payload)
        
        return body_text.strip(), body_html.strip(), has_attachments
    
    async def add_label(self, message_id: str, label_name: str) -> bool:
        """Add a label to a message (for tagging)."""
        if not self.service:
            return False
        
        try:
            # Get or create label
            label_id = await self._get_or_create_label(label_name)
            if not label_id:
                return False
            
            # Add label to message
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'addLabelIds': [label_id]}
            ).execute()
            
            logger.info(f"Added label '{label_name}' to message {message_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding label to message: {e}")
            return False
    
    async def _get_or_create_label(self, label_name: str) -> Optional[str]:
        """Get existing label ID or create new label."""
        try:
            # List existing labels
            labels_result = self.service.users().labels().list(userId='me').execute()
            labels = labels_result.get('labels', [])
            
            # Check if label exists
            for label in labels:
                if label['name'] == label_name:
                    return label['id']
            
            # Create new label
            label_object = {
                'name': label_name,
                'labelListVisibility': 'labelShow',
                'messageListVisibility': 'show'
            }
            
            created_label = self.service.users().labels().create(
                userId='me',
                body=label_object
            ).execute()
            
            logger.info(f"Created new Gmail label: {label_name}")
            return created_label['id']
            
        except Exception as e:
            logger.error(f"Error managing label '{label_name}': {e}")
            return None
    
    async def get_unread_count(self) -> int:
        """Get count of unread messages in inbox."""
        if not self.service:
            return 0
        
        try:
            result = self.service.users().messages().list(
                userId='me',
                q='in:inbox is:unread',
                maxResults=1
            ).execute()
            
            return result.get('resultSizeEstimate', 0)
            
        except Exception as e:
            logger.error(f"Error getting unread count: {e}")
            return 0
