' Personal Assistant - Create Desktop Shortcuts
' This VBScript creates desktop shortcuts reliably on Windows

Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Get current directory and desktop path
strCurrentDir = objFSO.GetAbsolutePathName(".")
strDesktop = objShell.SpecialFolders("Desktop")

WScript.Echo "Creating Personal Assistant desktop shortcuts..."
WScript.Echo "Application directory: " & strCurrentDir
WScript.Echo "Desktop directory: " & strDesktop

' Create Start shortcut
Set objStartLink = objShell.CreateShortcut(strDesktop & "\Start Personal Assistant.lnk")
objStartLink.TargetPath = strCurrentDir & "\start_assistant.bat"
objStartLink.WorkingDirectory = strCurrentDir
objStartLink.Description = "Start the Personal Assistant application"
objStartLink.IconLocation = "shell32.dll,25"
objStartLink.Save
WScript.Echo "Created: Start Personal Assistant.lnk"

' Create Stop shortcut
Set objStopLink = objShell.CreateShortcut(strDesktop & "\Stop Personal Assistant.lnk")
objStopLink.TargetPath = strCurrentDir & "\stop_assistant.bat"
objStopLink.WorkingDirectory = strCurrentDir
objStopLink.Description = "Stop the Personal Assistant application"
objStopLink.IconLocation = "shell32.dll,28"
objStopLink.Save
WScript.Echo "Created: Stop Personal Assistant.lnk"

' Create Web shortcut
Set objWebLink = objShell.CreateShortcut(strDesktop & "\Open Personal Assistant.lnk")
objWebLink.TargetPath = "http://localhost:8000"
objWebLink.Description = "Open Personal Assistant in web browser"
objWebLink.IconLocation = "shell32.dll,14"
objWebLink.Save
WScript.Echo "Created: Open Personal Assistant.lnk"

' Create Folder shortcut
Set objFolderLink = objShell.CreateShortcut(strDesktop & "\Personal Assistant Folder.lnk")
objFolderLink.TargetPath = strCurrentDir
objFolderLink.Description = "Open Personal Assistant application folder"
objFolderLink.IconLocation = "shell32.dll,4"
objFolderLink.Save
WScript.Echo "Created: Personal Assistant Folder.lnk"

WScript.Echo ""
WScript.Echo "Desktop shortcuts created successfully!"
WScript.Echo ""
WScript.Echo "Usage:"
WScript.Echo "1. Double-click 'Start Personal Assistant' to launch"
WScript.Echo "2. Wait for the application to start (console window will appear)"
WScript.Echo "3. Double-click 'Open Personal Assistant' to use the web interface"
WScript.Echo "4. Double-click 'Stop Personal Assistant' when finished"
WScript.Echo ""
WScript.Echo "Press OK to continue..."
