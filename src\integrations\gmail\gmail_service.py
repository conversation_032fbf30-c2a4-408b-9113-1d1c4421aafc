"""Gmail API integration service."""

import os
import json
import base64
import email
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

import pickle
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from ...core.logging import logger
from ...core.config import settings


# Gmail API scopes
SCOPES = [
    'https://www.googleapis.com/auth/gmail.readonly',
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/gmail.modify',
    'https://www.googleapis.com/auth/gmail.labels'
]


@dataclass
class GmailMessage:
    """Gmail message data structure."""
    id: str
    thread_id: str
    subject: str
    sender_email: str
    sender_name: str
    recipient_emails: List[str]
    body_text: str
    body_html: str
    received_date: datetime
    labels: List[str]
    is_unread: bool
    is_important: bool
    has_attachments: bool
    snippet: str
    raw_message: Dict[str, Any]


class GmailService:
    """Gmail API service for email operations."""
    
    def __init__(self):
        self.logger = logger
        self.service = None
        self.credentials = None
        self.user_email = None
        
        # Paths for credentials
        self.credentials_file = "config/gmail_credentials.json"
        self.token_file = "data/gmail_token.pickle"
        
    async def authenticate(self) -> bool:
        """Authenticate with Gmail API."""
        try:
            # Load existing credentials
            if os.path.exists(self.token_file):
                with open(self.token_file, 'rb') as token:
                    self.credentials = pickle.load(token)
            
            # If there are no (valid) credentials available, let the user log in
            if not self.credentials or not self.credentials.valid:
                if self.credentials and self.credentials.expired and self.credentials.refresh_token:
                    self.credentials.refresh(Request())
                else:
                    if not os.path.exists(self.credentials_file):
                        self.logger.error(f"Gmail credentials file not found: {self.credentials_file}")
                        self.logger.info("Please download credentials from Google Cloud Console and save as config/gmail_credentials.json")
                        return False
                    
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.credentials_file, SCOPES
                    )
                    self.credentials = flow.run_local_server(port=0)
                
                # Save the credentials for the next run
                os.makedirs(os.path.dirname(self.token_file), exist_ok=True)
                with open(self.token_file, 'wb') as token:
                    pickle.dump(self.credentials, token)
            
            # Build the service
            self.service = build('gmail', 'v1', credentials=self.credentials)
            
            # Get user email
            profile = self.service.users().getProfile(userId='me').execute()
            self.user_email = profile['emailAddress']
            
            self.logger.info(f"Gmail authentication successful for: {self.user_email}")
            return True
            
        except Exception as e:
            self.logger.error(f"Gmail authentication failed: {e}")
            return False
    
    async def get_messages(
        self, 
        query: str = "", 
        max_results: int = 10,
        label_ids: Optional[List[str]] = None
    ) -> List[GmailMessage]:
        """Get messages from Gmail."""
        try:
            if not self.service:
                if not await self.authenticate():
                    return []
            
            # Build query parameters
            params = {
                'userId': 'me',
                'q': query,
                'maxResults': max_results
            }
            
            if label_ids:
                params['labelIds'] = label_ids
            
            # Get message list
            result = self.service.users().messages().list(**params).execute()
            messages = result.get('messages', [])
            
            # Get full message details
            gmail_messages = []
            for msg in messages:
                try:
                    full_message = await self._get_message_details(msg['id'])
                    if full_message:
                        gmail_messages.append(full_message)
                except Exception as e:
                    self.logger.error(f"Error getting message {msg['id']}: {e}")
                    continue
            
            self.logger.info(f"Retrieved {len(gmail_messages)} messages from Gmail")
            return gmail_messages
            
        except HttpError as e:
            self.logger.error(f"Gmail API error: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Error getting Gmail messages: {e}")
            return []
    
    async def get_unread_messages(self, max_results: int = 50) -> List[GmailMessage]:
        """Get unread messages from Gmail."""
        return await self.get_messages(
            query="is:unread",
            max_results=max_results,
            label_ids=['UNREAD']
        )
    
    async def get_important_messages(self, max_results: int = 20) -> List[GmailMessage]:
        """Get important messages from Gmail."""
        return await self.get_messages(
            query="is:important",
            max_results=max_results,
            label_ids=['IMPORTANT']
        )
    
    async def get_recent_messages(self, hours: int = 24, max_results: int = 50) -> List[GmailMessage]:
        """Get recent messages from the last N hours."""
        # Gmail query for recent messages
        query = f"newer_than:{hours}h"
        return await self.get_messages(query=query, max_results=max_results)
    
    async def _get_message_details(self, message_id: str) -> Optional[GmailMessage]:
        """Get detailed information for a specific message."""
        try:
            message = self.service.users().messages().get(
                userId='me', 
                id=message_id,
                format='full'
            ).execute()
            
            # Extract headers
            headers = {h['name']: h['value'] for h in message['payload'].get('headers', [])}
            
            # Extract basic info
            subject = headers.get('Subject', '(No Subject)')
            sender = headers.get('From', '')
            to = headers.get('To', '')
            date_str = headers.get('Date', '')
            
            # Parse sender
            sender_email, sender_name = self._parse_email_address(sender)
            
            # Parse recipients
            recipient_emails = self._parse_recipients(to)
            
            # Parse date
            received_date = self._parse_date(date_str)
            
            # Extract body
            body_text, body_html = self._extract_body(message['payload'])
            
            # Extract labels
            labels = message.get('labelIds', [])
            
            # Check flags
            is_unread = 'UNREAD' in labels
            is_important = 'IMPORTANT' in labels
            has_attachments = self._has_attachments(message['payload'])
            
            # Get snippet
            snippet = message.get('snippet', '')
            
            return GmailMessage(
                id=message_id,
                thread_id=message['threadId'],
                subject=subject,
                sender_email=sender_email,
                sender_name=sender_name,
                recipient_emails=recipient_emails,
                body_text=body_text,
                body_html=body_html,
                received_date=received_date,
                labels=labels,
                is_unread=is_unread,
                is_important=is_important,
                has_attachments=has_attachments,
                snippet=snippet,
                raw_message=message
            )
            
        except Exception as e:
            self.logger.error(f"Error getting message details for {message_id}: {e}")
            return None
    
    def _parse_email_address(self, email_str: str) -> tuple[str, str]:
        """Parse email address and name from 'Name <<EMAIL>>' format."""
        if not email_str:
            return '', ''
        
        # Handle different formats
        if '<' in email_str and '>' in email_str:
            # <AUTHOR> <EMAIL>"
            parts = email_str.split('<')
            name = parts[0].strip().strip('"')
            email_addr = parts[1].strip().rstrip('>')
        else:
            # Format: "<EMAIL>" or just email
            name = ''
            email_addr = email_str.strip()
        
        return email_addr, name
    
    def _parse_recipients(self, to_str: str) -> List[str]:
        """Parse recipient email addresses."""
        if not to_str:
            return []
        
        # Split by comma and extract email addresses
        recipients = []
        for recipient in to_str.split(','):
            email_addr, _ = self._parse_email_address(recipient.strip())
            if email_addr:
                recipients.append(email_addr)
        
        return recipients
    
    def _parse_date(self, date_str: str) -> datetime:
        """Parse email date string to datetime."""
        try:
            # Gmail dates are in RFC 2822 format
            from email.utils import parsedate_to_datetime
            return parsedate_to_datetime(date_str)
        except Exception:
            # Fallback to current time
            return datetime.now(timezone.utc)
    
    def _extract_body(self, payload: Dict[str, Any]) -> tuple[str, str]:
        """Extract text and HTML body from message payload."""
        body_text = ""
        body_html = ""
        
        def extract_from_part(part):
            nonlocal body_text, body_html
            
            mime_type = part.get('mimeType', '')
            
            if mime_type == 'text/plain':
                data = part.get('body', {}).get('data', '')
                if data:
                    body_text += base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
            
            elif mime_type == 'text/html':
                data = part.get('body', {}).get('data', '')
                if data:
                    body_html += base64.urlsafe_b64decode(data).decode('utf-8', errors='ignore')
            
            elif mime_type.startswith('multipart/'):
                # Recursively process multipart
                for subpart in part.get('parts', []):
                    extract_from_part(subpart)
        
        extract_from_part(payload)
        
        # If no text body, try to extract from HTML
        if not body_text and body_html:
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(body_html, 'html.parser')
                body_text = soup.get_text()
            except ImportError:
                # Fallback: simple HTML tag removal
                import re
                body_text = re.sub(r'<[^>]+>', '', body_html)
        
        return body_text.strip(), body_html.strip()
    
    def _has_attachments(self, payload: Dict[str, Any]) -> bool:
        """Check if message has attachments."""
        def check_part(part):
            if part.get('filename'):
                return True
            for subpart in part.get('parts', []):
                if check_part(subpart):
                    return True
            return False
        
        return check_part(payload)
    
    async def send_reply(
        self, 
        original_message_id: str, 
        reply_body: str,
        reply_subject: Optional[str] = None
    ) -> bool:
        """Send a reply to an email."""
        try:
            if not self.service:
                if not await self.authenticate():
                    return False
            
            # Get original message for reply context
            original = self.service.users().messages().get(
                userId='me', 
                id=original_message_id,
                format='full'
            ).execute()
            
            # Extract headers from original
            headers = {h['name']: h['value'] for h in original['payload'].get('headers', [])}
            
            # Build reply
            original_subject = headers.get('Subject', '')
            if not reply_subject:
                reply_subject = f"Re: {original_subject}" if not original_subject.startswith('Re:') else original_subject
            
            original_from = headers.get('From', '')
            original_to = headers.get('To', '')
            message_id = headers.get('Message-ID', '')
            
            # Create reply message
            reply_message = self._create_reply_message(
                to=original_from,
                subject=reply_subject,
                body=reply_body,
                in_reply_to=message_id,
                references=message_id,
                thread_id=original['threadId']
            )
            
            # Send the reply
            result = self.service.users().messages().send(
                userId='me',
                body=reply_message
            ).execute()
            
            self.logger.info(f"Reply sent successfully: {result['id']}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending reply: {e}")
            return False
    
    def _create_reply_message(
        self,
        to: str,
        subject: str,
        body: str,
        in_reply_to: str,
        references: str,
        thread_id: str
    ) -> Dict[str, Any]:
        """Create a reply message in Gmail format."""
        import email.mime.text
        import email.mime.multipart
        
        # Create message
        msg = email.mime.text.MIMEText(body)
        msg['To'] = to
        msg['Subject'] = subject
        msg['In-Reply-To'] = in_reply_to
        msg['References'] = references
        
        # Encode message
        raw_message = base64.urlsafe_b64encode(msg.as_bytes()).decode('utf-8')
        
        return {
            'raw': raw_message,
            'threadId': thread_id
        }
    
    async def mark_as_read(self, message_id: str) -> bool:
        """Mark a message as read."""
        try:
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'removeLabelIds': ['UNREAD']}
            ).execute()
            return True
        except Exception as e:
            self.logger.error(f"Error marking message as read: {e}")
            return False
    
    async def add_label(self, message_id: str, label_name: str) -> bool:
        """Add a label to a message."""
        try:
            # Get or create label
            label_id = await self._get_or_create_label(label_name)
            if not label_id:
                return False
            
            self.service.users().messages().modify(
                userId='me',
                id=message_id,
                body={'addLabelIds': [label_id]}
            ).execute()
            return True
        except Exception as e:
            self.logger.error(f"Error adding label: {e}")
            return False
    
    async def _get_or_create_label(self, label_name: str) -> Optional[str]:
        """Get existing label ID or create new label."""
        try:
            # Get existing labels
            labels = self.service.users().labels().list(userId='me').execute()
            
            # Check if label exists
            for label in labels.get('labels', []):
                if label['name'] == label_name:
                    return label['id']
            
            # Create new label
            label_object = {
                'name': label_name,
                'labelListVisibility': 'labelShow',
                'messageListVisibility': 'show'
            }
            
            created_label = self.service.users().labels().create(
                userId='me',
                body=label_object
            ).execute()
            
            return created_label['id']
            
        except Exception as e:
            self.logger.error(f"Error getting/creating label: {e}")
            return None


# Global Gmail service instance
gmail_service = GmailService()
