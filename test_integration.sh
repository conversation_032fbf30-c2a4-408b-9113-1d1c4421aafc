#!/bin/bash

# Web Interface Integration Test Script
echo "🚀 Starting Web Interface Integration Tests"
echo "=================================================="

BASE_URL="http://localhost:8000"
TESTS_PASSED=0
TESTS_TOTAL=0

# Function to test API endpoint
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -n "Testing $description... "
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$BASE_URL$endpoint")
    elif [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -o /tmp/response.json -X POST -H "Content-Type: application/json" -d "$data" "$BASE_URL$endpoint")
    fi
    
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ PASS"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        
        # Show some response data if available
        if [ -f /tmp/response.json ]; then
            status=$(cat /tmp/response.json | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
            if [ "$status" = "success" ]; then
                echo "   Status: success"
            fi
        fi
    else
        echo "❌ FAIL (HTTP $http_code)"
    fi
}

echo ""
echo "📊 Testing System Status"
test_api "GET" "/api/status" "" "System Status"

echo ""
echo "✅ Testing Tasks API"
test_api "GET" "/api/tasks" "" "Get Tasks"
test_api "GET" "/api/tasks/stats" "" "Task Statistics"
test_api "POST" "/api/tasks/extract" '{"text":"I need to follow up with the client about the project proposal and schedule a meeting."}' "Task Extraction"

echo ""
echo "👥 Testing Contacts API"
test_api "GET" "/api/contacts" "" "Get Contacts"
test_api "GET" "/api/contacts/quality/scan" "" "Contact Quality Scan"

echo ""
echo "📝 Testing Notes API"
test_api "GET" "/api/notes" "" "Get Notes"
test_api "POST" "/api/notes" '{"title":"Integration Test Note","content":"This is a test note.","category":"test"}' "Create Note"

echo ""
echo "📁 Testing Projects API"
test_api "GET" "/api/projects" "" "Get Projects"
test_api "GET" "/api/projects/stats" "" "Project Statistics"

echo ""
echo "📧 Testing Email/Gmail API"
test_api "GET" "/api/gmail/status" "" "Gmail Status"

echo ""
echo "💬 Testing Chat API"
test_api "POST" "/api/chat" '{"message":"Hello, this is a test message."}' "Chat Message"

echo ""
echo "🔍 Testing Search API"
test_api "POST" "/api/search" '{"query":"test","limit":5}' "Search"

echo ""
echo "⚙️ Testing Settings API"
test_api "GET" "/api/settings" "" "Get Settings"

echo ""
echo "📅 Testing Calendar API"
test_api "GET" "/api/calendar/events/upcoming" "" "Upcoming Events"

echo ""
echo "=================================================="
echo "🎯 Integration Test Results"
echo "Tests Passed: $TESTS_PASSED/$TESTS_TOTAL"

if [ $TESTS_PASSED -eq $TESTS_TOTAL ]; then
    echo "🎉 All tests passed! Web interface is fully integrated."
    SUCCESS_RATE=100
else
    FAILED=$((TESTS_TOTAL - TESTS_PASSED))
    echo "⚠️  $FAILED tests failed."
    SUCCESS_RATE=$((TESTS_PASSED * 100 / TESTS_TOTAL))
fi

echo "Success Rate: $SUCCESS_RATE%"

echo ""
echo "🌐 Web Interface URLs:"
echo "   Dashboard: $BASE_URL/"
echo "   Email: $BASE_URL/email"
echo "   Tasks: $BASE_URL/tasks"
echo "   Contacts: $BASE_URL/contacts"
echo "   Notes: $BASE_URL/notes"
echo "   Projects: $BASE_URL/projects"
echo "   Chat: $BASE_URL/chat"
echo "   Search: $BASE_URL/search"
echo "   Settings: $BASE_URL/settings"
echo "   Calendar: $BASE_URL/calendar"
echo "   Upload: $BASE_URL/upload"

# Cleanup
rm -f /tmp/response.json

echo ""
echo "✨ Integration testing complete!"
