"""Research Agent for the Assistant."""

import re
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Set, Tuple
from dataclasses import dataclass

from ..core.logging import logger
from ..core.config import settings


@dataclass
class ResearchTopic:
    """Represents a research topic."""
    topic: str
    keywords: List[str]
    confidence: float
    sources: List[str]
    related_topics: List[str]


@dataclass
class ResearchInsight:
    """Represents a research insight."""
    topic: str
    insight: str
    confidence: float
    sources: List[str]
    evidence: List[str]
    created_at: datetime


@dataclass
class KnowledgeGap:
    """Represents an identified knowledge gap."""
    topic: str
    gap_description: str
    suggested_research: List[str]
    priority: str  # 'low', 'medium', 'high'
    confidence: float


class ResearchAgent:
    """
    Agent responsible for intelligent research and knowledge discovery.
    
    Key responsibilities:
    - Analyze content to identify research topics and trends
    - Extract insights from documents and notes
    - Identify knowledge gaps and suggest research directions
    - Track research progress and findings
    - Generate research summaries and reports
    """
    
    def __init__(self):
        self.config = settings.agents.research
        self.auto_analysis = getattr(self.config, 'auto_analysis', True)
        
        # Research domains and keywords
        self.research_domains = {
            'technology': {
                'keywords': ['ai', 'machine learning', 'blockchain', 'cloud', 'api', 'software', 'programming'],
                'weight': 1.0
            },
            'business': {
                'keywords': ['strategy', 'market', 'revenue', 'customer', 'growth', 'competition', 'analysis'],
                'weight': 0.9
            },
            'science': {
                'keywords': ['research', 'study', 'experiment', 'data', 'analysis', 'hypothesis', 'methodology'],
                'weight': 0.8
            },
            'health': {
                'keywords': ['health', 'medical', 'treatment', 'diagnosis', 'wellness', 'fitness', 'nutrition'],
                'weight': 0.7
            },
            'education': {
                'keywords': ['learning', 'education', 'training', 'course', 'skill', 'knowledge', 'tutorial'],
                'weight': 0.6
            }
        }
    
    async def analyze_content_for_research(self, content_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze content items to identify research topics and insights.
        
        Args:
            content_items: List of content dictionaries (notes, documents, etc.)
            
        Returns:
            Dictionary with research analysis results
        """
        logger.info(f"Analyzing {len(content_items)} content items for research insights")
        
        result = {
            'status': 'success',
            'content_analyzed': len(content_items),
            'topics_identified': 0,
            'insights_generated': 0,
            'knowledge_gaps_found': 0,
            'research_suggestions': [],
            'errors': []
        }
        
        try:
            # Identify research topics
            topics = await self._identify_research_topics(content_items)
            result['topics_identified'] = len(topics)
            
            # Generate insights
            insights = await self._generate_insights(content_items, topics)
            result['insights_generated'] = len(insights)
            
            # Identify knowledge gaps
            knowledge_gaps = await self._identify_knowledge_gaps(topics, content_items)
            result['knowledge_gaps_found'] = len(knowledge_gaps)
            
            # Generate research suggestions
            suggestions = await self._generate_research_suggestions(topics, knowledge_gaps)
            result['research_suggestions'] = suggestions
            
        except Exception as e:
            logger.error(f"Error analyzing content for research: {e}")
            result['errors'].append(str(e))
            result['status'] = 'error'
        
        logger.info(f"Research analysis completed: {result}")
        return result
    
    async def extract_research_insights(self, content: str, source: str = "unknown") -> List[ResearchInsight]:
        """
        Extract research insights from a piece of content.
        
        Args:
            content: Text content to analyze
            source: Source identifier
            
        Returns:
            List of research insights
        """
        insights = []
        
        try:
            # Extract key findings
            findings = self._extract_key_findings(content)
            
            # Extract research topics
            topics = self._extract_topics_from_content(content)
            
            # Generate insights for each topic
            for topic in topics:
                topic_content = self._extract_topic_content(content, topic)
                
                if topic_content:
                    insight = ResearchInsight(
                        topic=topic,
                        insight=self._summarize_topic_content(topic_content),
                        confidence=self._calculate_insight_confidence(topic_content),
                        sources=[source],
                        evidence=self._extract_evidence(topic_content),
                        created_at=datetime.now()
                    )
                    insights.append(insight)
            
        except Exception as e:
            logger.error(f"Error extracting research insights: {e}")
        
        return insights
    
    async def suggest_research_directions(self, current_topics: List[str], knowledge_base: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Suggest new research directions based on current topics and knowledge base.
        
        Args:
            current_topics: List of current research topics
            knowledge_base: List of existing knowledge items
            
        Returns:
            List of research direction suggestions
        """
        suggestions = []
        
        try:
            # Analyze topic trends
            topic_analysis = await self._analyze_topic_trends(current_topics, knowledge_base)
            
            # Identify emerging topics
            emerging_topics = await self._identify_emerging_topics(knowledge_base)
            
            # Find research gaps
            gaps = await self._find_research_gaps(current_topics, knowledge_base)
            
            # Generate suggestions
            for gap in gaps:
                suggestion = {
                    'topic': gap.topic,
                    'description': gap.gap_description,
                    'suggested_research': gap.suggested_research,
                    'priority': gap.priority,
                    'confidence': gap.confidence,
                    'reasoning': self._generate_research_reasoning(gap, topic_analysis)
                }
                suggestions.append(suggestion)
            
            # Add emerging topic suggestions
            for topic in emerging_topics:
                suggestion = {
                    'topic': topic['topic'],
                    'description': f"Emerging topic with growing interest",
                    'suggested_research': [f"Investigate {topic['topic']} trends and applications"],
                    'priority': 'medium',
                    'confidence': topic['confidence'],
                    'reasoning': f"Detected increasing mentions of {topic['topic']} in recent content"
                }
                suggestions.append(suggestion)
            
        except Exception as e:
            logger.error(f"Error suggesting research directions: {e}")
        
        return suggestions
    
    async def generate_research_summary(self, topic: str, content_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate a research summary for a specific topic.
        
        Args:
            topic: Research topic to summarize
            content_items: List of relevant content items
            
        Returns:
            Dictionary with research summary
        """
        try:
            # Filter content relevant to topic
            relevant_content = self._filter_content_by_topic(content_items, topic)
            
            # Extract key points
            key_points = self._extract_key_points(relevant_content, topic)
            
            # Identify sources
            sources = list(set(item.get('source', 'unknown') for item in relevant_content))
            
            # Generate timeline if dates available
            timeline = self._generate_research_timeline(relevant_content)
            
            # Calculate confidence
            confidence = self._calculate_summary_confidence(relevant_content, key_points)
            
            return {
                'status': 'success',
                'topic': topic,
                'summary': self._generate_topic_summary(key_points),
                'key_points': key_points,
                'sources': sources,
                'timeline': timeline,
                'confidence': confidence,
                'content_count': len(relevant_content),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating research summary for {topic}: {e}")
            return {'status': 'error', 'message': str(e)}
    
    async def _identify_research_topics(self, content_items: List[Dict[str, Any]]) -> List[ResearchTopic]:
        """Identify research topics from content."""
        topic_scores = {}
        topic_sources = {}
        
        for item in content_items:
            content = item.get('content', '') + ' ' + item.get('title', '')
            source = item.get('source', 'unknown')
            
            # Score content against research domains
            for domain, domain_info in self.research_domains.items():
                score = 0
                matched_keywords = []
                
                for keyword in domain_info['keywords']:
                    if keyword.lower() in content.lower():
                        score += domain_info['weight']
                        matched_keywords.append(keyword)
                
                if score > 0:
                    if domain not in topic_scores:
                        topic_scores[domain] = 0
                        topic_sources[domain] = set()
                    
                    topic_scores[domain] += score
                    topic_sources[domain].add(source)
        
        # Convert to ResearchTopic objects
        topics = []
        for domain, score in topic_scores.items():
            if score >= 1.0:  # Minimum threshold
                topics.append(ResearchTopic(
                    topic=domain,
                    keywords=self.research_domains[domain]['keywords'],
                    confidence=min(score / 10.0, 1.0),  # Normalize to 0-1
                    sources=list(topic_sources[domain]),
                    related_topics=self._find_related_topics(domain, topic_scores)
                ))
        
        return topics
    
    async def _generate_insights(self, content_items: List[Dict[str, Any]], topics: List[ResearchTopic]) -> List[ResearchInsight]:
        """Generate insights from content and topics."""
        insights = []
        
        for topic in topics:
            # Find content related to this topic
            related_content = []
            for item in content_items:
                content = item.get('content', '') + ' ' + item.get('title', '')
                
                if any(keyword.lower() in content.lower() for keyword in topic.keywords):
                    related_content.append(item)
            
            if related_content:
                # Generate insight for this topic
                insight_text = self._generate_topic_insight(topic, related_content)
                
                insights.append(ResearchInsight(
                    topic=topic.topic,
                    insight=insight_text,
                    confidence=topic.confidence,
                    sources=[item.get('source', 'unknown') for item in related_content],
                    evidence=self._extract_evidence_for_topic(related_content, topic),
                    created_at=datetime.now()
                ))
        
        return insights
    
    async def _identify_knowledge_gaps(self, topics: List[ResearchTopic], content_items: List[Dict[str, Any]]) -> List[KnowledgeGap]:
        """Identify knowledge gaps in research topics."""
        gaps = []
        
        for topic in topics:
            # Analyze coverage of topic
            coverage_analysis = self._analyze_topic_coverage(topic, content_items)
            
            if coverage_analysis['coverage_score'] < 0.6:  # Less than 60% coverage
                gap = KnowledgeGap(
                    topic=topic.topic,
                    gap_description=coverage_analysis['gap_description'],
                    suggested_research=coverage_analysis['suggested_research'],
                    priority=self._determine_gap_priority(topic, coverage_analysis),
                    confidence=coverage_analysis['confidence']
                )
                gaps.append(gap)
        
        return gaps
    
    async def _generate_research_suggestions(self, topics: List[ResearchTopic], gaps: List[KnowledgeGap]) -> List[str]:
        """Generate research suggestions based on topics and gaps."""
        suggestions = []
        
        # Suggestions from knowledge gaps
        for gap in gaps:
            suggestions.extend(gap.suggested_research)
        
        # Suggestions from topic analysis
        for topic in topics:
            if topic.confidence > 0.7:  # High confidence topics
                suggestions.append(f"Deep dive into {topic.topic} applications and trends")
            
            if topic.related_topics:
                suggestions.append(f"Explore connections between {topic.topic} and {', '.join(topic.related_topics[:2])}")
        
        return list(set(suggestions))  # Remove duplicates
    
    def _extract_key_findings(self, content: str) -> List[str]:
        """Extract key findings from content."""
        findings = []
        
        # Look for conclusion patterns
        conclusion_patterns = [
            r'(?:conclusion|findings?|results?|discovered?|found that)[\s:]+([^.!?]+[.!?])',
            r'(?:shows?|indicates?|suggests?|demonstrates?)[\s]+(?:that\s+)?([^.!?]+[.!?])',
            r'(?:therefore|thus|hence|consequently)[\s,]+([^.!?]+[.!?])'
        ]
        
        for pattern in conclusion_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            findings.extend(matches)
        
        return findings[:5]  # Return top 5 findings
    
    def _extract_topics_from_content(self, content: str) -> List[str]:
        """Extract research topics from content."""
        topics = []
        
        for domain, domain_info in self.research_domains.items():
            score = sum(1 for keyword in domain_info['keywords'] if keyword.lower() in content.lower())
            
            if score >= 2:  # At least 2 keywords from domain
                topics.append(domain)
        
        return topics
    
    def _extract_topic_content(self, content: str, topic: str) -> str:
        """Extract content relevant to a specific topic."""
        keywords = self.research_domains.get(topic, {}).get('keywords', [])
        
        sentences = content.split('.')
        relevant_sentences = []
        
        for sentence in sentences:
            if any(keyword.lower() in sentence.lower() for keyword in keywords):
                relevant_sentences.append(sentence.strip())
        
        return '. '.join(relevant_sentences)
    
    def _summarize_topic_content(self, content: str) -> str:
        """Summarize content for a topic."""
        # Simple summarization - take first and most relevant sentences
        sentences = content.split('.')[:3]  # First 3 sentences
        return '. '.join(s.strip() for s in sentences if s.strip())
    
    def _calculate_insight_confidence(self, content: str) -> float:
        """Calculate confidence score for an insight."""
        # Base confidence on content length and keyword density
        word_count = len(content.split())
        
        if word_count > 100:
            return 0.8
        elif word_count > 50:
            return 0.6
        else:
            return 0.4
    
    def _extract_evidence(self, content: str) -> List[str]:
        """Extract evidence statements from content."""
        evidence = []
        
        # Look for evidence patterns
        evidence_patterns = [
            r'(?:according to|based on|research shows|studies indicate)[\s]+([^.!?]+[.!?])',
            r'(?:data|statistics|numbers|figures)[\s]+(?:show|indicate|suggest)[\s]+([^.!?]+[.!?])'
        ]
        
        for pattern in evidence_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            evidence.extend(matches)
        
        return evidence[:3]  # Return top 3 evidence statements
    
    def _find_related_topics(self, topic: str, topic_scores: Dict[str, float]) -> List[str]:
        """Find topics related to the given topic."""
        related = []
        
        # Simple approach: topics with similar scores
        topic_score = topic_scores.get(topic, 0)
        
        for other_topic, score in topic_scores.items():
            if other_topic != topic and abs(score - topic_score) < 2.0:
                related.append(other_topic)
        
        return related[:3]  # Return top 3 related topics
    
    def _generate_topic_insight(self, topic: ResearchTopic, content_items: List[Dict[str, Any]]) -> str:
        """Generate an insight for a research topic."""
        content_count = len(content_items)
        
        if content_count == 1:
            return f"Single source discusses {topic.topic} with focus on {', '.join(topic.keywords[:3])}"
        else:
            return f"Analysis of {content_count} sources reveals {topic.topic} trends across {', '.join(topic.keywords[:3])}"
    
    def _extract_evidence_for_topic(self, content_items: List[Dict[str, Any]], topic: ResearchTopic) -> List[str]:
        """Extract evidence for a specific topic."""
        evidence = []
        
        for item in content_items:
            content = item.get('content', '')
            item_evidence = self._extract_evidence(content)
            evidence.extend(item_evidence)
        
        return evidence[:5]  # Return top 5 evidence items
    
    def _analyze_topic_coverage(self, topic: ResearchTopic, content_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze how well a topic is covered in the content."""
        keyword_coverage = {}
        
        for keyword in topic.keywords:
            keyword_coverage[keyword] = 0
            
            for item in content_items:
                content = item.get('content', '') + ' ' + item.get('title', '')
                if keyword.lower() in content.lower():
                    keyword_coverage[keyword] += 1
        
        covered_keywords = sum(1 for count in keyword_coverage.values() if count > 0)
        coverage_score = covered_keywords / len(topic.keywords)
        
        missing_keywords = [k for k, count in keyword_coverage.items() if count == 0]
        
        return {
            'coverage_score': coverage_score,
            'gap_description': f"Limited coverage of {', '.join(missing_keywords[:3])} aspects",
            'suggested_research': [f"Research {keyword} in context of {topic.topic}" for keyword in missing_keywords[:3]],
            'confidence': 0.8
        }
    
    def _determine_gap_priority(self, topic: ResearchTopic, coverage_analysis: Dict[str, Any]) -> str:
        """Determine priority level for a knowledge gap."""
        if topic.confidence > 0.8 and coverage_analysis['coverage_score'] < 0.3:
            return 'high'
        elif topic.confidence > 0.6 and coverage_analysis['coverage_score'] < 0.5:
            return 'medium'
        else:
            return 'low'


# Create global instance
research_agent = ResearchAgent()
