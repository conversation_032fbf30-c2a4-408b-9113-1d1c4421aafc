# Assistant Configuration

# Ollama Configuration
ollama:
  host: "http://localhost:11434"
  default_model: "deepseek-r1:latest"
  reasoning_model: "deepseek-r1:latest"  # For complex reasoning tasks
  fast_model: "deepseek-r1:latest"       # For quick responses
  timeout: 60  # Increased for reasoning model
  max_retries: 3

# Knowledge Storage
knowledge:
  vector_db:
    type: "chromadb"  # Options: chromadb, qdrant, weaviate
    path: "./data/database/vector_db"
    collection_name: "knowledge_base"
  
  metadata_db:
    type: "sqlite"  # Options: sqlite, postgresql
    path: "./data/database/metadata.db"
  
  embedding:
    model: "sentence-transformers/all-MiniLM-L6-v2"
    chunk_size: 512
    chunk_overlap: 50

# Document Processing
processing:
  supported_formats:
    - "pdf"
    - "txt"
    - "md"
    - "rst"
    - "html"
    - "jpg"
    - "jpeg"
    - "png"
    - "gif"
    - "webp"
    - "url"  # For bookmarked URLs
  
  ocr:
    enabled: true
    engine: "tesseract"

  image_processing:
    enabled: true
    extract_text: true  # OCR from images
    generate_descriptions: true  # AI-generated image descriptions

  url_processing:
    enabled: true
    fetch_content: true
    extract_metadata: true
    screenshot: false  # Optional: take screenshots of web pages
  
  max_file_size_mb: 100

# External Integrations
integrations:
  email:
    enabled: true
    providers:
      gmail:
        enabled: true
        accounts:
          personal:
            credentials_file: "./config/gmail_personal_credentials.json"
            token_file: "./data/tokens/gmail_personal_token.json"
            context: "personal"
          # work:
          #   credentials_file: "./config/gmail_work_credentials.json"
          #   token_file: "./data/tokens/gmail_work_token.json"
          #   context: "work"
      proton:
        enabled: false
        # Proton uses IMAP/SMTP bridge
        host: "127.0.0.1"
        port: 1143
        username: ""  # Will be configured during setup
        password_file: "./config/proton_password.txt"
        context: "personal"  # or "work" depending on account

    processing:
      batch_size: 100
      check_interval_minutes: 60  # Hourly checks
      max_age_days: 90  # Process last 3 months initially
      folders_to_monitor: ["INBOX"]  # Only inbox, ignore archived
      skip_folders: ["Spam", "Trash", "Drafts", "Archive", "All Mail"]
      auto_create_tasks: true
      task_keywords: ["todo", "action", "deadline", "follow up", "reminder", "please", "can you", "need", "asap", "urgent"]

      # Email tagging and categorization
      auto_tag: true
      importance_tags: ["high", "medium", "low"]
      urgency_tags: ["urgent", "normal", "low"]
      category_tags: ["work", "personal", "marketing", "newsletter", "social"]

      # Task creation logic
      create_tasks_for_received: true
      create_tasks_for_sent: false  # Less important as you mentioned
      duplicate_detection: true
      context_similarity_threshold: 0.8  # For grouping related emails

      # Marketing email handling
      auto_unsubscribe: true
      marketing_keywords: ["unsubscribe", "newsletter", "promotion", "deal", "sale", "offer"]
      marketing_confidence_threshold: 0.9

  calendar:
    enabled: true
    providers:
      google:
        enabled: true
        credentials_file: "./config/google_calendar_credentials.json"
        token_file: "./data/tokens/google_calendar_token.json"
        calendars:
          personal: "primary"
          work: ""  # Work calendar ID - to be configured
          car: ""   # Shared car calendar ID - to be configured

    sync:
      check_interval_minutes: 30
      conflict_resolution: "notify"  # Options: prefer_work, prefer_personal, notify
      notification_method: "web_ui"  # How to notify about conflicts
      availability_buffer_minutes: 15
      working_hours:
        start: "09:00"
        end: "17:00"
        timezone: "America/New_York"

      # Calendar coordination
      separate_but_coordinated: true
      cross_calendar_availability: true  # Check all calendars for availability
      meeting_optimization: true

  contacts:
    enabled: true
    providers:
      google:
        enabled: true
        credentials_file: "./config/google_contacts_credentials.json"
        token_file: "./data/tokens/google_contacts_token.json"
        contexts: ["personal", "work"]  # Separate contact groups
      csv:
        enabled: true
        import_directory: "./data/contacts/csv"
        auto_import: true
        appfolio_integration:
          enabled: true
          email_pattern: "*appfolio*"  # Pattern to identify Appfolio CSV emails
          context: "work"

    management:
      auto_add_from_email: true
      separate_work_personal: true
      duplicate_detection: true
      merge_strategy: "context_aware"  # Allow duplicates across work/personal
      backup_before_changes: true

      # Contact creation rules
      work_email_domains: []  # Domains that indicate work contacts
      personal_indicators: ["gmail.com", "yahoo.com", "hotmail.com"]

# Agents Configuration
agents:
  archivist:
    enabled: true
    auto_categorize: true
    extract_metadata: true

  research:
    enabled: true
    max_sources: 10
    synthesis_depth: "detailed"

  task_manager:
    enabled: true
    reminder_system: true
    email_integration: true
    auto_prioritize: true
    learning_enabled: true

  note_keeper:
    enabled: true
    auto_link: true

  project_manager:
    enabled: true
    milestone_tracking: true

  email_processor:
    enabled: true
    task_creation: true
    contact_extraction: true
    priority_learning: true
    sentiment_analysis: true
    email_drafting: true  # For future email response drafting
    writing_style_learning: true

  email_drafter:
    enabled: false  # Future feature
    learn_writing_style: true
    background_processing: true  # Process during idle time
    style_analysis_schedule: "02:00"  # 2 AM daily

  calendar_manager:
    enabled: true
    availability_sync: true
    meeting_optimization: true
    conflict_detection: true

  contact_manager:
    enabled: true
    auto_update: true
    duplicate_management: true
    relationship_tracking: true

# API Configuration
api:
  host: "localhost"
  port: 8000
  enable_web_ui: true
  web_ui_title: "Assistant - Knowledge Library"

# Logging
logging:
  level: "INFO"
  file: "./data/logs/assistant.log"
  max_size_mb: 10
  backup_count: 5

# Learning and Adaptation
learning:
  enabled: true
  feedback_sources:
    - "task_modifications"
    - "task_deletions"
    - "priority_changes"
    - "metadata_edits"
    - "tag_changes"
    - "manual_ratings"
    - "user_actions"  # Learn from all user interactions

  models:
    task_priority: "./data/models/task_priority_model.pkl"
    email_importance: "./data/models/email_importance_model.pkl"
    contact_relevance: "./data/models/contact_relevance_model.pkl"
    writing_style: "./data/models/writing_style_model.pkl"
    task_grouping: "./data/models/task_grouping_model.pkl"

  training:
    min_samples: 10  # Minimum samples before model training
    retrain_interval_days: 7
    backup_models: true

  # Change tracking for learning
  change_tracking:
    enabled: true
    track_all_modifications: true
    log_user_actions: true
    metadata_change_weight: 1.0  # How much to weight metadata changes

# Privacy and Security
privacy:
  email_content_retention_days: 365  # Keep for learning, local only
  exclude_keywords: []  # Learn as much as possible
  anonymize_contacts: false
  encrypt_credentials: true

  # Work/Personal separation
  context_separation:
    enabled: true
    strict_separation: true
    cross_context_learning: false  # Don't learn work patterns for personal tasks

# Data Directories
data:
  documents: "./data/documents"
  database: "./data/database"
  models: "./data/models"
  logs: "./data/logs"
  tokens: "./data/tokens"
  contacts: "./data/contacts"
  email_cache: "./data/email"
  calendar_cache: "./data/calendar"
