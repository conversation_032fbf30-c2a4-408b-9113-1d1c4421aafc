{% extends "base.html" %}

{% block content %}
<div class="chat-page">
    <header class="page-header">
        <h1>AI Assistant Chat</h1>
        <p class="page-subtitle">Ask questions about your knowledge base and get intelligent responses</p>
    </header>

    <div class="chat-container">
        <!-- Chat Messages -->
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant-message">
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div class="message-text">
                        Hello! I'm your AI assistant. I can help you with:
                        <ul>
                            <li>Searching your documents and notes</li>
                            <li>Creating and managing tasks</li>
                            <li>Analyzing emails and contacts</li>
                            <li>Answering questions about your data</li>
                        </ul>
                        What would you like to know?
                    </div>
                    <div class="message-time">Just now</div>
                </div>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="chat-input-container">
            <div class="chat-input-wrapper">
                <textarea 
                    id="chatInput" 
                    placeholder="Ask me anything about your knowledge base..."
                    rows="1"
                ></textarea>
                <button class="send-button" id="sendButton">
                    <span class="send-icon">📤</span>
                </button>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="quick-action" data-query="What tasks do I have due today?">
                    📋 Today's Tasks
                </button>
                <button class="quick-action" data-query="Show me my recent emails">
                    📧 Recent Emails
                </button>
                <button class="quick-action" data-query="What documents did I upload recently?">
                    📄 Recent Documents
                </button>
                <button class="quick-action" data-query="Help me create a task">
                    ➕ Create Task
                </button>
            </div>
        </div>
    </div>

    <!-- Chat Settings -->
    <div class="chat-settings" id="chatSettings">
        <button class="settings-toggle" id="settingsToggle">⚙️</button>
        <div class="settings-panel" id="settingsPanel" style="display: none;">
            <h3>Chat Settings</h3>
            
            <div class="setting-group">
                <label>
                    <input type="checkbox" id="includeContext" checked>
                    Include document context in responses
                </label>
            </div>
            
            <div class="setting-group">
                <label>
                    <input type="checkbox" id="saveHistory" checked>
                    Save chat history
                </label>
            </div>
            
            <div class="setting-group">
                <label for="responseLength">Response Length:</label>
                <select id="responseLength">
                    <option value="short">Short</option>
                    <option value="medium" selected>Medium</option>
                    <option value="detailed">Detailed</option>
                </select>
            </div>
            
            <div class="setting-actions">
                <button class="btn btn-secondary" id="clearHistory">Clear History</button>
                <button class="btn btn-secondary" id="exportHistory">Export Chat</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.chat-page {
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.message {
    display: flex;
    gap: var(--spacing-md);
    max-width: 80%;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.assistant-message {
    align-self: flex-start;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.user-message .message-avatar {
    background-color: var(--accent-primary);
    color: white;
}

.assistant-message .message-avatar {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
}

.message-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    position: relative;
}

.user-message .message-content {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.message-text {
    line-height: 1.5;
    margin-bottom: var(--spacing-xs);
}

.message-text ul {
    margin: var(--spacing-sm) 0;
    padding-left: var(--spacing-lg);
}

.message-text li {
    margin-bottom: var(--spacing-xs);
}

.message-time {
    font-size: var(--font-size-xs);
    opacity: 0.7;
    text-align: right;
}

.chat-input-container {
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    background-color: var(--bg-primary);
}

.chat-input-wrapper {
    display: flex;
    gap: var(--spacing-sm);
    align-items: flex-end;
    margin-bottom: var(--spacing-md);
}

.chat-input-wrapper textarea {
    flex: 1;
    min-height: 40px;
    max-height: 120px;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    resize: none;
    font-family: inherit;
    font-size: var(--font-size-base);
}

.send-button {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--border-radius-sm);
    background-color: var(--accent-primary);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.send-button:hover {
    background-color: #0056b3;
}

.send-button:disabled {
    background-color: var(--bg-tertiary);
    cursor: not-allowed;
}

.quick-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.quick-action {
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: all 0.2s ease;
}

.quick-action:hover {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.chat-settings {
    position: fixed;
    top: 120px;
    right: var(--spacing-lg);
    z-index: 1000;
}

.settings-toggle {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.settings-panel {
    position: absolute;
    top: 50px;
    right: 0;
    width: 300px;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: 0 4px 12px var(--shadow);
}

.settings-panel h3 {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-primary);
}

.setting-group {
    margin-bottom: var(--spacing-lg);
}

.setting-group label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.setting-group select {
    width: 100%;
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.setting-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: all 0.2s ease;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 2px;
}

.typing-dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--text-secondary);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-xs);
}

.context-sources {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.context-sources h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.source-item {
    color: var(--accent-primary);
    text-decoration: none;
    display: block;
    margin-bottom: var(--spacing-xs);
}

.source-item:hover {
    text-decoration: underline;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class ChatManager {
    constructor() {
        this.chatHistory = JSON.parse(localStorage.getItem('chatHistory') || '[]');
        this.isTyping = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadChatHistory();
        this.autoResizeTextarea();
    }

    setupEventListeners() {
        // Send message
        document.getElementById('sendButton').addEventListener('click', () => this.sendMessage());
        document.getElementById('chatInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Quick actions
        document.querySelectorAll('.quick-action').forEach(button => {
            button.addEventListener('click', (e) => {
                const query = e.target.dataset.query;
                document.getElementById('chatInput').value = query;
                this.sendMessage();
            });
        });

        // Settings
        document.getElementById('settingsToggle').addEventListener('click', () => this.toggleSettings());
        document.getElementById('clearHistory').addEventListener('click', () => this.clearHistory());
        document.getElementById('exportHistory').addEventListener('click', () => this.exportHistory());

        // Auto-resize textarea
        document.getElementById('chatInput').addEventListener('input', () => this.autoResizeTextarea());
    }

    async sendMessage() {
        const input = document.getElementById('chatInput');
        const message = input.value.trim();
        
        if (!message || this.isTyping) return;

        // Add user message
        this.addMessage(message, 'user');
        input.value = '';
        this.autoResizeTextarea();

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send to AI
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    message: message,
                    include_context: document.getElementById('includeContext').checked,
                    response_length: document.getElementById('responseLength').value,
                    chat_history: this.chatHistory.slice(-10) // Last 10 messages for context
                })
            });

            const data = await response.json();
            this.hideTypingIndicator();

            if (data.status === 'success') {
                this.addMessage(data.response, 'assistant', data.sources);
            } else {
                this.addMessage('Sorry, I encountered an error processing your request. Please try again.', 'assistant', null, true);
            }
        } catch (error) {
            this.hideTypingIndicator();
            this.addMessage('Sorry, I\'m having trouble connecting right now. Please try again later.', 'assistant', null, true);
        }
    }

    addMessage(text, sender, sources = null, isError = false) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const avatar = sender === 'user' ? '👤' : '🤖';
        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        let sourcesHtml = '';
        if (sources && sources.length > 0) {
            sourcesHtml = `
                <div class="context-sources">
                    <h4>Sources:</h4>
                    ${sources.map(source => `
                        <a href="#" class="source-item">${source.title || source.filename}</a>
                    `).join('')}
                </div>
            `;
        }

        messageDiv.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-text">${this.formatMessage(text)}</div>
                ${sourcesHtml}
                ${isError ? '<div class="error-message">This response may not be accurate due to a processing error.</div>' : ''}
                <div class="message-time">${timestamp}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Save to history
        if (document.getElementById('saveHistory').checked) {
            this.chatHistory.push({
                text: text,
                sender: sender,
                timestamp: new Date().toISOString(),
                sources: sources
            });
            this.saveChatHistory();
        }
    }

    formatMessage(text) {
        // Basic markdown-like formatting
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }

    showTypingIndicator() {
        this.isTyping = true;
        const messagesContainer = document.getElementById('chatMessages');
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message assistant-message typing-indicator';
        typingDiv.id = 'typingIndicator';

        typingDiv.innerHTML = `
            <div class="message-avatar">🤖</div>
            <div class="message-content">
                <div class="message-text">
                    AI is thinking
                    <span class="typing-dots">
                        <span class="typing-dot"></span>
                        <span class="typing-dot"></span>
                        <span class="typing-dot"></span>
                    </span>
                </div>
            </div>
        `;

        messagesContainer.appendChild(typingDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    hideTypingIndicator() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    autoResizeTextarea() {
        const textarea = document.getElementById('chatInput');
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    toggleSettings() {
        const panel = document.getElementById('settingsPanel');
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }

    loadChatHistory() {
        if (this.chatHistory.length === 0) return;

        const messagesContainer = document.getElementById('chatMessages');
        // Clear welcome message if loading history
        messagesContainer.innerHTML = '';

        this.chatHistory.forEach(msg => {
            this.addMessageFromHistory(msg);
        });
    }

    addMessageFromHistory(msg) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${msg.sender}-message`;

        const avatar = msg.sender === 'user' ? '👤' : '🤖';
        const timestamp = new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        let sourcesHtml = '';
        if (msg.sources && msg.sources.length > 0) {
            sourcesHtml = `
                <div class="context-sources">
                    <h4>Sources:</h4>
                    ${msg.sources.map(source => `
                        <a href="#" class="source-item">${source.title || source.filename}</a>
                    `).join('')}
                </div>
            `;
        }

        messageDiv.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-text">${this.formatMessage(msg.text)}</div>
                ${sourcesHtml}
                <div class="message-time">${timestamp}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
    }

    saveChatHistory() {
        localStorage.setItem('chatHistory', JSON.stringify(this.chatHistory));
    }

    clearHistory() {
        if (confirm('Are you sure you want to clear the chat history?')) {
            this.chatHistory = [];
            localStorage.removeItem('chatHistory');
            
            // Reset to welcome message
            const messagesContainer = document.getElementById('chatMessages');
            messagesContainer.innerHTML = `
                <div class="message assistant-message">
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <div class="message-text">
                            Hello! I'm your AI assistant. I can help you with:
                            <ul>
                                <li>Searching your documents and notes</li>
                                <li>Creating and managing tasks</li>
                                <li>Analyzing emails and contacts</li>
                                <li>Answering questions about your data</li>
                            </ul>
                            What would you like to know?
                        </div>
                        <div class="message-time">Just now</div>
                    </div>
                </div>
            `;
        }
    }

    exportHistory() {
        if (this.chatHistory.length === 0) {
            app.showNotification('No chat history to export', 'info');
            return;
        }

        const exportData = {
            exported_at: new Date().toISOString(),
            message_count: this.chatHistory.length,
            messages: this.chatHistory
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        app.showNotification('Chat history exported!', 'success');
    }
}

// Initialize chat manager
let chatManager;
document.addEventListener('DOMContentLoaded', () => {
    chatManager = new ChatManager();
});
</script>
{% endblock %}
