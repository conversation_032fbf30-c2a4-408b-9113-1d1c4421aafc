# Assistant - Technical Specification

## Phase 1: Foundation (Weeks 1-3)

### Overview
Establish the core infrastructure for the personal assistant system, including local LLM integration, knowledge storage, and basic document processing capabilities.

### 1.1 Ollama Integration

**Objective**: Establish reliable communication with local Ollama service for LLM operations.

**Components**:
- `src/core/ollama_client.py`: HTTP client for Ollama API
- Connection pooling and retry logic
- Model management (list, pull, delete models)
- Streaming response handling for long conversations

**Key Features**:
- Automatic model availability checking
- Fallback model selection
- Request/response logging
- Error handling and recovery

**Deliverables**:
- [ ] Ollama client class with async support
- [ ] Model management utilities
- [ ] Connection health monitoring
- [ ] Unit tests for all client operations

### 1.2 Vector Database Setup

**Objective**: Implement semantic search capabilities using vector embeddings.

**Components**:
- `src/knowledge/vector_store.py`: Vector database abstraction
- `src/knowledge/embeddings.py`: Embedding generation and management
- ChromaDB integration (primary choice)
- Document chunking and indexing

**Key Features**:
- Configurable embedding models
- Efficient similarity search
- Metadata filtering
- Batch operations for large document sets

**Deliverables**:
- [ ] Vector store interface and ChromaDB implementation
- [ ] Embedding pipeline with chunking strategies
- [ ] Search and retrieval methods
- [ ] Performance benchmarks and optimization

### 1.3 Metadata Database

**Objective**: Store structured information about documents, notes, tasks, and relationships.

**Components**:
- `src/knowledge/metadata_store.py`: Database abstraction layer
- `src/knowledge/models.py`: SQLAlchemy models
- `src/knowledge/migrations/`: Database schema migrations
- Relationship mapping between entities

**Database Schema**:
```sql
-- Documents table
CREATE TABLE documents (
    id UUID PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_type VARCHAR(50),
    file_size INTEGER,
    created_at TIMESTAMP,
    modified_at TIMESTAMP,
    processed_at TIMESTAMP,
    content_hash VARCHAR(64),
    metadata JSONB
);

-- Notes table
CREATE TABLE notes (
    id UUID PRIMARY KEY,
    title VARCHAR(255),
    content TEXT,
    tags TEXT[],
    created_at TIMESTAMP,
    modified_at TIMESTAMP,
    parent_id UUID REFERENCES notes(id),
    metadata JSONB
);

-- Tasks table
CREATE TABLE tasks (
    id UUID PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    priority INTEGER DEFAULT 3,
    importance_score FLOAT,  -- AI-calculated importance
    user_priority INTEGER,   -- User-set priority for learning
    due_date TIMESTAMP,
    created_at TIMESTAMP,
    completed_at TIMESTAMP,
    project_id UUID,
    source_type VARCHAR(50), -- 'email', 'manual', 'document', etc.
    source_id VARCHAR(255),  -- Reference to source (email ID, etc.)
    metadata JSONB
);

-- Projects table
CREATE TABLE projects (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP,
    completed_at TIMESTAMP,
    metadata JSONB
);

-- Emails table
CREATE TABLE emails (
    id UUID PRIMARY KEY,
    message_id VARCHAR(255) UNIQUE NOT NULL,
    thread_id VARCHAR(255),
    subject VARCHAR(500),
    sender_email VARCHAR(255),
    sender_name VARCHAR(255),
    recipients TEXT[], -- Array of recipient emails
    cc_recipients TEXT[],
    bcc_recipients TEXT[],
    body_text TEXT,
    body_html TEXT,
    received_date TIMESTAMP,
    sent_date TIMESTAMP,
    folder VARCHAR(100),
    labels TEXT[],
    importance_score FLOAT,
    has_attachments BOOLEAN DEFAULT FALSE,
    is_read BOOLEAN DEFAULT FALSE,
    task_created BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP,
    metadata JSONB
);

-- Contacts table
CREATE TABLE contacts (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    display_name VARCHAR(255),
    company VARCHAR(255),
    job_title VARCHAR(255),
    phone_numbers JSONB, -- Array of phone objects
    addresses JSONB,     -- Array of address objects
    notes TEXT,
    tags TEXT[],
    last_contact_date TIMESTAMP,
    contact_frequency INTEGER DEFAULT 0,
    importance_score FLOAT,
    source VARCHAR(50), -- 'email', 'csv', 'manual', etc.
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    metadata JSONB
);

-- Calendar events table
CREATE TABLE calendar_events (
    id UUID PRIMARY KEY,
    event_id VARCHAR(255),
    calendar_id VARCHAR(255),
    calendar_type VARCHAR(50), -- 'personal', 'work', etc.
    title VARCHAR(500),
    description TEXT,
    location VARCHAR(500),
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    all_day BOOLEAN DEFAULT FALSE,
    recurrence_rule TEXT,
    attendees JSONB, -- Array of attendee objects
    organizer_email VARCHAR(255),
    status VARCHAR(50), -- 'confirmed', 'tentative', 'cancelled'
    visibility VARCHAR(50), -- 'public', 'private', etc.
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    metadata JSONB
);

-- Learning feedback table
CREATE TABLE learning_feedback (
    id UUID PRIMARY KEY,
    entity_type VARCHAR(50), -- 'task', 'email', 'contact'
    entity_id UUID,
    feedback_type VARCHAR(50), -- 'priority_change', 'deletion', 'rating'
    original_value JSONB,
    corrected_value JSONB,
    user_action VARCHAR(100),
    timestamp TIMESTAMP,
    applied_to_model BOOLEAN DEFAULT FALSE,
    metadata JSONB
);

-- Relationships table (for linking entities)
CREATE TABLE relationships (
    id UUID PRIMARY KEY,
    source_type VARCHAR(50),
    source_id UUID,
    target_type VARCHAR(50),
    target_id UUID,
    relationship_type VARCHAR(100),
    strength FLOAT DEFAULT 1.0,
    created_at TIMESTAMP,
    metadata JSONB
);
```

**Deliverables**:
- [ ] SQLAlchemy models for all entities
- [ ] Database migration system
- [ ] CRUD operations for all entities
- [ ] Relationship management utilities

### 1.4 Document Processing Pipeline

**Objective**: Automated ingestion and processing of various document types.

**Components**:
- `src/processing/document_processor.py`: Main processing orchestrator
- `src/processing/parsers/`: Format-specific parsers
- `src/processing/extractors.py`: Metadata and content extraction
- `src/processing/chunking.py`: Text chunking strategies

**Supported Formats (Phase 1)**:
- PDF documents (text and OCR)
- Plain text files (.txt, .md, .rst)
- HTML files
- Image files (.jpg, .jpeg, .png, .gif, .webp) with OCR and AI descriptions
- Bookmarked URLs with content extraction and metadata

**Processing Pipeline**:
1. File type detection
2. Content extraction
3. Metadata extraction (title, author, creation date, etc.)
4. Text cleaning and normalization
5. Content chunking for vector storage
6. Vector embedding generation
7. Database storage (both vector and metadata)

**Deliverables**:
- [ ] Modular parser system for different file types
- [ ] OCR integration for scanned documents
- [ ] Metadata extraction utilities
- [ ] Chunking strategies (fixed-size, semantic, paragraph-based)
- [ ] Processing queue for batch operations

### 1.5 Basic Query Interface

**Objective**: Provide initial search and retrieval capabilities.

**Components**:
- `src/knowledge/query_engine.py`: Query processing and routing
- `src/knowledge/retrieval.py`: Document retrieval and ranking
- Basic semantic search functionality
- Query preprocessing and enhancement

**Features**:
- Natural language query processing
- Hybrid search (vector + keyword)
- Result ranking and filtering
- Context-aware retrieval

**Deliverables**:
- [ ] Query processing pipeline
- [ ] Search result ranking algorithms
- [ ] Basic CLI search interface
- [ ] Query performance optimization

### 1.6 Web Interface

**Objective**: Provide an intuitive web-based interface for interacting with the knowledge library.

**Components**:
- `src/api/web_app.py`: FastAPI web application
- `src/api/static/`: Static assets (CSS, JavaScript, images)
- `src/api/templates/`: HTML templates
- WebSocket support for real-time interactions

**Key Features**:
- Document upload and management interface
- Search interface with filters and faceted search
- Note-taking and organization tools
- Task management dashboard
- Project overview and tracking
- Agent interaction chat interface

**Technology Stack**:
- **Backend**: FastAPI with WebSocket support
- **Frontend**: Modern vanilla JavaScript with Web Components
- **Styling**: CSS Grid/Flexbox with custom design system
- **Real-time**: WebSocket for live updates and chat
- **File Upload**: Drag-and-drop with progress indicators

**UI/UX Design Principles**:
- Clean, minimal interface focused on content
- Responsive design for desktop and tablet use
- Keyboard shortcuts for power users
- Dark/light theme support
- Accessibility compliance (WCAG 2.1)

**Deliverables**:
- [ ] FastAPI web application with routing
- [ ] Responsive HTML templates
- [ ] Interactive JavaScript components
- [ ] File upload and management interface
- [ ] Search and filtering interface
- [ ] Real-time chat interface for agent interactions

## Phase 1 Success Criteria

### Functional Requirements
1. **Document Ingestion**: Successfully process and store documents in supported formats
   - PDF documents with text extraction and OCR
   - Plain text and markup files (.txt, .md, .rst)
   - Image files with OCR and AI-generated descriptions
   - URL bookmarks with content extraction and metadata
2. **Search Capability**: Retrieve relevant documents based on natural language queries
3. **Ollama Integration**: Reliable communication with DeepSeek-R1 model
4. **Web Interface**: Functional dashboard and basic document management
5. **Data Persistence**: Proper storage and retrieval of both vector and metadata

### Performance Requirements
1. **Search Response Time**: < 3 seconds for typical queries
2. **Document Processing**:
   - Text documents: < 10 seconds
   - PDF documents: < 30 seconds (excluding very large files)
   - Images: < 15 seconds (including OCR and description generation)
   - URLs: < 20 seconds (including content extraction)
3. **Memory Usage**: < 4GB RAM for typical operation (accounting for DeepSeek-R1)
4. **Storage Efficiency**: Reasonable compression ratios for vector data

### Quality Requirements
1. **Test Coverage**: > 80% code coverage for core components
2. **Documentation**: Complete API documentation and user guides
3. **Error Handling**: Graceful handling of common error scenarios
4. **Logging**: Comprehensive logging for debugging and monitoring
5. **Security**: Basic input validation and sanitization

## Development Guidelines

### Code Organization
- Follow Python PEP 8 style guidelines
- Use type hints throughout the codebase
- Implement comprehensive error handling
- Write docstrings for all public methods
- Organize code into logical modules and packages

### Testing Strategy
- **Unit Tests**: All core components (knowledge storage, document processing, etc.)
- **Integration Tests**: Database operations and Ollama communication
- **End-to-End Tests**: Complete document processing pipeline
- **Performance Tests**: Search operations and document processing speed
- **Web Interface Tests**: Basic functionality and API endpoints

### Configuration Management
- All configuration through YAML files with validation
- Environment-specific overrides support
- Validation of configuration parameters at startup
- Default values for all settings
- Clear documentation of all configuration options

### Logging and Monitoring
- Structured logging with appropriate levels (DEBUG, INFO, WARNING, ERROR)
- Performance metrics collection for key operations
- Error tracking and alerting capabilities
- User activity logging (privacy-conscious)
- Separate log files for different components

### Security Considerations
- Input validation for all user-provided data
- File upload restrictions and scanning
- URL validation and safe content fetching
- No sensitive data in logs
- Local-only operation (no external API calls except for URL fetching)

## Implementation Priority

### Week 1: Core Infrastructure
1. **Day 1-2**: Ollama integration and model management
2. **Day 3-4**: Configuration system and logging setup
3. **Day 5-7**: Database schemas and basic CRUD operations

### Week 2: Document Processing
1. **Day 1-2**: Basic document parsers (PDF, text, markdown)
2. **Day 3-4**: Image processing with OCR integration
3. **Day 5-7**: URL processing and content extraction

### Week 3: Search and Interface
1. **Day 1-3**: Vector database setup and search implementation
2. **Day 4-5**: Web interface completion
3. **Day 6-7**: Testing and bug fixes

## Phase 1.5: External Integrations (Week 3.5-4)

### Email Integration
**Objective**: Connect to Gmail and Proton Mail for intelligent email processing with work/personal separation.

**Components**:
- `src/integrations/email/gmail_client.py`: Gmail API integration (personal + work accounts)
- `src/integrations/email/proton_client.py`: Proton Mail IMAP integration
- `src/integrations/email/parser.py`: Email content parsing and analysis
- `src/agents/email_processor.py`: Email processing agent with context awareness

**Key Features**:
- **Dual Gmail Account Support**: Separate personal and work Gmail accounts
- **Proton Mail Integration**: IMAP bridge support for Proton Mail
- **Intelligent Email Tagging**: Automatic importance/urgency tags
- **Smart Task Creation**: Context-aware task extraction with duplicate detection
- **Marketing Email Handling**: Auto-detection and unsubscribe suggestions
- **Work/Personal Separation**: Strict context isolation
- **Hourly Processing**: Batch processing every hour (configurable)
- **Inbox-Only Processing**: Ignores archived emails, focuses on active inbox

**Deliverables**:
- [ ] Gmail API integration with dual account support (personal/work)
- [ ] Proton Mail IMAP integration via Bridge
- [ ] Context-aware email parsing and categorization
- [ ] Smart task creation with duplicate detection and grouping
- [ ] Automatic email tagging (importance, urgency, category)
- [ ] Marketing email detection and auto-unsubscribe
- [ ] Contact extraction with work/personal separation
- [ ] Learning system integration for continuous improvement

### Calendar Integration
**Objective**: Manage Google Calendar with personal/work/car calendar coordination.

**Components**:
- `src/integrations/calendar/google_calendar.py`: Google Calendar API integration
- `src/agents/calendar_manager.py`: Calendar management agent with conflict notification

**Key Features**:
- **Triple Calendar Management**: Personal, work, and shared car calendar
- **Separate but Coordinated**: Maintains calendar separation while checking availability across all
- **Conflict Notification**: Web UI notifications when conflicts are detected
- **Smart Meeting Suggestions**: Optimal meeting times considering all calendars
- **Availability Optimization**: Cross-calendar availability checking with buffer times
- **Future Email Integration**: Foundation for AI email response drafting with calendar awareness

**Deliverables**:
- [ ] Google Calendar API integration
- [ ] Outlook Calendar API integration
- [ ] Calendar synchronization engine
- [ ] Conflict detection and resolution system
- [ ] Availability calculation and caching
- [ ] Meeting optimization algorithms

### Contact Management Integration
**Objective**: Unified contact management across multiple sources.

**Components**:
- `src/integrations/contacts/google_contacts.py`: Google Contacts API
- `src/integrations/contacts/outlook_contacts.py`: Outlook Contacts API
- `src/integrations/contacts/csv_manager.py`: CSV import/export
- `src/agents/contact_manager.py`: Contact management agent

**Key Features**:
- Multi-source contact synchronization
- Automatic contact updates from email interactions
- Duplicate detection and intelligent merging
- CSV import/export for bulk operations
- Contact relationship tracking and scoring

**Deliverables**:
- [ ] Google Contacts API integration
- [ ] Outlook Contacts API integration
- [ ] CSV import/export functionality
- [ ] Duplicate detection algorithms
- [ ] Contact merging and relationship tracking
- [ ] Automatic contact updates from emails

### Learning System
**Objective**: Implement adaptive learning from user feedback.

**Components**:
- `src/learning/feedback_collector.py`: User feedback collection
- `src/learning/priority_model.py`: Task priority prediction model
- `src/learning/importance_model.py`: Email importance scoring model
- `src/learning/trainer.py`: Model training and updating

**Key Features**:
- Feedback collection from user actions
- Machine learning models for priority prediction
- Continuous learning and model improvement
- Privacy-conscious data handling
- Model versioning and rollback capabilities

**Deliverables**:
- [ ] Feedback collection system
- [ ] Priority prediction models (scikit-learn based)
- [ ] Model training pipeline
- [ ] Performance monitoring and evaluation
- [ ] Privacy-compliant data handling

## Phase 2: Enhanced Agent System (Weeks 5-7)

Building on the foundation and integrations from Phase 1 and 1.5:

### Agent Framework
- **Base Agent Class**: Common interface for all agents
- **Communication Protocol**: Message passing between agents
- **Agent Registry**: Dynamic agent discovery and management
- **Task Queue**: Asynchronous task processing

### Initial Agents
1. **Archivist Agent**:
   - Intelligent document categorization
   - Automatic metadata extraction and enhancement
   - Duplicate detection and management

2. **Research Agent**:
   - Multi-document synthesis and summarization
   - Question answering across knowledge base
   - Citation and source tracking

3. **Query Router**:
   - Intelligent routing of user queries to appropriate agents
   - Context-aware query enhancement
   - Result aggregation and ranking

### Agent Orchestration
- **Central Coordinator**: Manages agent interactions and workflows
- **Context Management**: Maintains conversation and task context
- **Resource Management**: Prevents conflicts and manages system resources

## Getting Started

### Prerequisites
- Python 3.11 or higher
- Ollama installed and running
- At least 8GB RAM (recommended for DeepSeek-R1)
- 10GB free disk space for models and data

### Quick Setup
1. **Clone and setup**:
   ```bash
   git clone <repository>
   cd assistant
   python scripts/dev_setup.py
   ```

2. **Start the web interface**:
   ```bash
   python -m src.api.web_app
   ```

3. **Or use the CLI**:
   ```bash
   python -m src.api.cli --help
   ```

### Development Workflow
1. **Make changes** to the codebase
2. **Run tests**: `pytest`
3. **Check code quality**: `black . && isort . && flake8`
4. **Test manually** using the web interface or CLI
5. **Update documentation** as needed

This technical specification provides a comprehensive roadmap for Phase 1 development, ensuring a solid foundation for the multi-agent personal assistant system.

## Phase 1 Success Criteria

### Functional Requirements
1. **Document Ingestion**: Successfully process and store documents in supported formats
2. **Search Capability**: Retrieve relevant documents based on natural language queries
3. **Ollama Integration**: Reliable communication with local LLM models
4. **Data Persistence**: Proper storage and retrieval of both vector and metadata

### Performance Requirements
1. **Search Response Time**: < 2 seconds for typical queries
2. **Document Processing**: < 30 seconds per document (excluding large PDFs)
3. **Memory Usage**: < 2GB RAM for typical operation
4. **Storage Efficiency**: Reasonable compression ratios for vector data

### Quality Requirements
1. **Test Coverage**: > 80% code coverage
2. **Documentation**: Complete API documentation and user guides
3. **Error Handling**: Graceful handling of common error scenarios
4. **Logging**: Comprehensive logging for debugging and monitoring

## Development Guidelines

### Code Organization
- Follow Python PEP 8 style guidelines
- Use type hints throughout the codebase
- Implement comprehensive error handling
- Write docstrings for all public methods

### Testing Strategy
- Unit tests for all core components
- Integration tests for database operations
- End-to-end tests for document processing pipeline
- Performance tests for search operations

### Configuration Management
- All configuration through YAML files
- Environment-specific overrides
- Validation of configuration parameters
- Default values for all settings

### Logging and Monitoring
- Structured logging with appropriate levels
- Performance metrics collection
- Error tracking and alerting
- User activity logging (privacy-conscious)

## Next Steps After Phase 1

Once Phase 1 is complete, we'll move to Phase 2: Core Agents, which will include:
- Agent base class and communication framework
- Archivist Agent for intelligent document categorization
- Research Agent for information synthesis
- Basic agent orchestration system

The foundation established in Phase 1 will support all future agent development and ensure scalable, maintainable architecture.
