"""Email content parsing and analysis utilities."""

import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from bs4 import BeautifulSoup

from ...core.logging import logger
from ...core.config import settings


@dataclass
class ParsedEmail:
    """Structured representation of a parsed email."""
    message_id: str
    subject: str
    sender_email: str
    sender_name: Optional[str]
    recipients: List[str]
    received_date: datetime
    body_text: str
    body_html: str
    context: str  # 'work' or 'personal'
    
    # Extracted information
    questions: List[str]
    action_items: List[str]
    dates_mentioned: List[datetime]
    urls: List[str]
    phone_numbers: List[str]
    
    # Classification
    is_marketing: bool
    is_automated: bool
    is_reply: bool
    is_forward: bool
    
    # Scores
    importance_score: float
    urgency_score: float
    
    # Metadata
    metadata: Dict[str, Any]


class EmailParser:
    """
    Advanced email content parser for extracting actionable information.
    
    Analyzes email content to identify:
    - Questions that need responses
    - Action items and tasks
    - Important dates and deadlines
    - Contact information
    - Marketing/automated emails
    """
    
    def __init__(self):
        self.config = settings.integrations.email.processing
        
        # Compile regex patterns for efficiency
        self.question_patterns = [
            re.compile(r'[^.!?]*\?[^.!?]*', re.IGNORECASE),
            re.compile(r'\b(can you|could you|would you|will you|are you)\b.*?[.!?]', re.IGNORECASE),
            re.compile(r'\b(please|kindly)\s+\w+.*?[.!?]', re.IGNORECASE),
        ]
        
        self.action_patterns = [
            re.compile(r'\b(need to|have to|must|should|required to)\b.*?[.!?]', re.IGNORECASE),
            re.compile(r'\b(action required|please|kindly)\b.*?[.!?]', re.IGNORECASE),
            re.compile(r'\b(deadline|due|expires?|submit|complete)\b.*?[.!?]', re.IGNORECASE),
        ]
        
        self.date_patterns = [
            re.compile(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b'),  # MM/DD/YYYY or MM-DD-YYYY
            re.compile(r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b'),    # YYYY/MM/DD or YYYY-MM-DD
            re.compile(r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4}\b', re.IGNORECASE),
            re.compile(r'\b(today|tomorrow|next week|next month|this week|this month)\b', re.IGNORECASE),
        ]
        
        self.url_pattern = re.compile(r'https?://[^\s<>"]+')
        self.phone_pattern = re.compile(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b')
        
        # Marketing email indicators
        self.marketing_indicators = [
            'unsubscribe', 'newsletter', 'promotion', 'deal', 'sale', 'offer',
            'discount', 'limited time', 'act now', 'click here', 'buy now'
        ]
        
        # Automated email indicators
        self.automated_indicators = [
            'do not reply', 'noreply', 'no-reply', 'automated', 'system generated',
            'this is an automated', 'automatic notification'
        ]
    
    def parse_email(self, email_data: Dict[str, Any]) -> ParsedEmail:
        """
        Parse email data into structured format with extracted information.
        
        Args:
            email_data: Raw email data from email client
            
        Returns:
            ParsedEmail object with all extracted information
        """
        logger.debug(f"Parsing email: {email_data.get('subject', 'No Subject')}")
        
        # Clean and prepare text content
        clean_text = self._clean_text_content(email_data.get('body_text', ''))
        clean_html = email_data.get('body_html', '')
        
        # Extract actionable content
        questions = self._extract_questions(clean_text)
        action_items = self._extract_action_items(clean_text)
        dates_mentioned = self._extract_dates(clean_text)
        urls = self._extract_urls(clean_text + ' ' + clean_html)
        phone_numbers = self._extract_phone_numbers(clean_text)
        
        # Classify email type
        is_marketing = self._is_marketing_email(email_data, clean_text)
        is_automated = self._is_automated_email(email_data, clean_text)
        is_reply = self._is_reply(email_data.get('subject', ''))
        is_forward = self._is_forward(email_data.get('subject', ''))
        
        # Calculate scores
        importance_score = self._calculate_importance_score(
            email_data, clean_text, questions, action_items, dates_mentioned
        )
        urgency_score = self._calculate_urgency_score(
            email_data, clean_text, dates_mentioned
        )
        
        # Build metadata
        metadata = {
            'word_count': len(clean_text.split()),
            'has_attachments': email_data.get('has_attachments', False),
            'thread_length': 1,  # TODO: Calculate actual thread length
            'sender_frequency': 1,  # TODO: Calculate sender frequency
            'parsing_timestamp': datetime.now().isoformat(),
        }
        
        return ParsedEmail(
            message_id=email_data.get('message_id', ''),
            subject=email_data.get('subject', ''),
            sender_email=email_data.get('sender_email', ''),
            sender_name=email_data.get('sender_name'),
            recipients=email_data.get('recipients', []),
            received_date=email_data.get('received_date', datetime.now()),
            body_text=clean_text,
            body_html=clean_html,
            context=email_data.get('context', 'personal'),
            questions=questions,
            action_items=action_items,
            dates_mentioned=dates_mentioned,
            urls=urls,
            phone_numbers=phone_numbers,
            is_marketing=is_marketing,
            is_automated=is_automated,
            is_reply=is_reply,
            is_forward=is_forward,
            importance_score=importance_score,
            urgency_score=urgency_score,
            metadata=metadata
        )
    
    def _clean_text_content(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove email signatures (simple heuristic)
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            # Skip common signature indicators
            if any(indicator in line.lower() for indicator in [
                'sent from my', 'get outlook', 'confidential', 'disclaimer'
            ]):
                break
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines).strip()
    
    def _extract_questions(self, text: str) -> List[str]:
        """Extract questions from email text."""
        questions = []
        
        for pattern in self.question_patterns:
            matches = pattern.findall(text)
            for match in matches:
                question = match.strip()
                if len(question) > 10 and question not in questions:  # Filter out short/duplicate questions
                    questions.append(question)
        
        return questions[:5]  # Limit to top 5 questions
    
    def _extract_action_items(self, text: str) -> List[str]:
        """Extract action items and tasks from email text."""
        action_items = []
        
        for pattern in self.action_patterns:
            matches = pattern.findall(text)
            for match in matches:
                action = match.strip()
                if len(action) > 15 and action not in action_items:  # Filter out short/duplicate actions
                    action_items.append(action)
        
        return action_items[:5]  # Limit to top 5 action items
    
    def _extract_dates(self, text: str) -> List[datetime]:
        """Extract dates mentioned in email text."""
        dates = []
        
        for pattern in self.date_patterns:
            matches = pattern.findall(text)
            for match in matches:
                try:
                    # Try to parse the date
                    parsed_date = self._parse_date_string(match)
                    if parsed_date and parsed_date not in dates:
                        dates.append(parsed_date)
                except Exception as e:
                    logger.debug(f"Could not parse date '{match}': {e}")
                    continue
        
        return sorted(dates)[:10]  # Return up to 10 dates, sorted
    
    def _parse_date_string(self, date_str: str) -> Optional[datetime]:
        """Parse various date string formats."""
        date_str = date_str.lower().strip()
        
        # Handle relative dates
        if date_str == 'today':
            return datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        elif date_str == 'tomorrow':
            return datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        elif 'next week' in date_str:
            return datetime.now() + timedelta(weeks=1)
        elif 'next month' in date_str:
            return datetime.now() + timedelta(days=30)
        
        # Try common date formats
        formats = [
            '%m/%d/%Y', '%m-%d-%Y', '%m/%d/%y', '%m-%d-%y',
            '%Y/%m/%d', '%Y-%m-%d',
            '%B %d, %Y', '%B %d %Y',
            '%b %d, %Y', '%b %d %Y'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        return None
    
    def _extract_urls(self, text: str) -> List[str]:
        """Extract URLs from email content."""
        urls = self.url_pattern.findall(text)
        return list(set(urls))[:10]  # Remove duplicates, limit to 10
    
    def _extract_phone_numbers(self, text: str) -> List[str]:
        """Extract phone numbers from email content."""
        phones = self.phone_pattern.findall(text)
        return list(set(phones))[:5]  # Remove duplicates, limit to 5
    
    def _is_marketing_email(self, email_data: Dict[str, Any], text: str) -> bool:
        """Determine if email is marketing/promotional."""
        subject = email_data.get('subject', '').lower()
        text_lower = text.lower()
        
        # Count marketing indicators
        marketing_count = 0
        for indicator in self.marketing_indicators:
            if indicator in subject or indicator in text_lower:
                marketing_count += 1
        
        # Check for unsubscribe links (strong indicator)
        if 'unsubscribe' in text_lower and ('http' in text_lower or 'click' in text_lower):
            marketing_count += 2
        
        # Simple threshold
        return marketing_count >= 2
    
    def _is_automated_email(self, email_data: Dict[str, Any], text: str) -> bool:
        """Determine if email is automated/system generated."""
        sender = email_data.get('sender_email', '').lower()
        subject = email_data.get('subject', '').lower()
        text_lower = text.lower()
        
        # Check sender patterns
        if any(pattern in sender for pattern in ['noreply', 'no-reply', 'donotreply', 'system', 'automated']):
            return True
        
        # Check content patterns
        for indicator in self.automated_indicators:
            if indicator in subject or indicator in text_lower:
                return True
        
        return False
    
    def _is_reply(self, subject: str) -> bool:
        """Check if email is a reply."""
        return subject.lower().startswith(('re:', 'reply:', 'response:'))
    
    def _is_forward(self, subject: str) -> bool:
        """Check if email is a forward."""
        return subject.lower().startswith(('fwd:', 'fw:', 'forward:'))
    
    def _calculate_importance_score(
        self,
        email_data: Dict[str, Any],
        text: str,
        questions: List[str],
        action_items: List[str],
        dates: List[datetime]
    ) -> float:
        """Calculate importance score based on email content."""
        score = 0.5  # Base score
        
        # Boost for questions (indicates need for response)
        score += min(len(questions) * 0.1, 0.3)
        
        # Boost for action items
        score += min(len(action_items) * 0.15, 0.4)
        
        # Boost for dates (indicates time-sensitive content)
        score += min(len(dates) * 0.05, 0.2)
        
        # Boost for important keywords in subject
        subject = email_data.get('subject', '').lower()
        important_keywords = ['important', 'urgent', 'critical', 'asap', 'deadline']
        for keyword in important_keywords:
            if keyword in subject:
                score += 0.2
                break
        
        # Boost for personal emails (assuming they're more important)
        if email_data.get('context') == 'personal':
            score += 0.1
        
        return min(max(score, 0.0), 1.0)
    
    def _calculate_urgency_score(
        self,
        email_data: Dict[str, Any],
        text: str,
        dates: List[datetime]
    ) -> float:
        """Calculate urgency score based on time-sensitive indicators."""
        score = 0.3  # Base score
        
        subject = email_data.get('subject', '').lower()
        text_lower = text.lower()
        
        # High urgency keywords
        urgent_keywords = ['urgent', 'asap', 'immediate', 'emergency', 'critical']
        for keyword in urgent_keywords:
            if keyword in subject:
                score += 0.4
            elif keyword in text_lower:
                score += 0.2
        
        # Time-sensitive keywords
        time_keywords = ['deadline', 'due', 'expires', 'today', 'tomorrow']
        for keyword in time_keywords:
            if keyword in subject or keyword in text_lower:
                score += 0.2
        
        # Check for near-future dates
        now = datetime.now()
        for date in dates:
            if date > now:
                days_until = (date - now).days
                if days_until <= 1:
                    score += 0.3
                elif days_until <= 7:
                    score += 0.2
                elif days_until <= 30:
                    score += 0.1
        
        return min(max(score, 0.0), 1.0)
