"""Database connection and session management."""

import os
from pathlib import Path
from typing import Generator, Optional

from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from ..core.config import settings
from ..core.logging import logger
from .models import Base


class DatabaseManager:
    """Database connection and session manager."""
    
    def __init__(self):
        self.engine: Optional[Engine] = None
        self.SessionLocal: Optional[sessionmaker] = None
        self._initialized = False
    
    def initialize(self) -> None:
        """Initialize database connection."""
        if self._initialized:
            return
            
        # Create database directory if it doesn't exist
        db_path = Path(settings.knowledge.metadata_db.path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create database URL
        if settings.knowledge.metadata_db.type == "sqlite":
            database_url = f"sqlite:///{db_path}"
            # SQLite specific configuration
            self.engine = create_engine(
                database_url,
                poolclass=StaticPool,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 20
                },
                echo=False  # Set to True for SQL debugging
            )
            
            # Enable foreign key constraints for SQLite
            @event.listens_for(self.engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA cache_size=10000")
                cursor.execute("PRAGMA temp_store=MEMORY")
                cursor.close()
                
        elif settings.knowledge.metadata_db.type == "postgresql":
            # PostgreSQL configuration (for future use)
            database_url = f"postgresql://{os.getenv('DB_USER', 'user')}:{os.getenv('DB_PASSWORD', 'password')}@{os.getenv('DB_HOST', 'localhost')}:{os.getenv('DB_PORT', '5432')}/{os.getenv('DB_NAME', 'assistant')}"
            self.engine = create_engine(
                database_url,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                echo=False
            )
        else:
            raise ValueError(f"Unsupported database type: {settings.knowledge.metadata_db.type}")
        
        # Create session factory
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # Create tables
        self.create_tables()
        
        self._initialized = True
        logger.info(f"Database initialized: {database_url}")
    
    def create_tables(self) -> None:
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            raise
    
    def get_session(self) -> Generator[Session, None, None]:
        """Get database session with automatic cleanup."""
        if not self._initialized:
            self.initialize()
            
        session = self.SessionLocal()
        try:
            yield session
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_session_sync(self) -> Session:
        """Get database session for synchronous operations."""
        if not self._initialized:
            self.initialize()
            
        return self.SessionLocal()
    
    def close(self) -> None:
        """Close database connections."""
        if self.engine:
            self.engine.dispose()
            logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


def get_db() -> Generator[Session, None, None]:
    """Dependency for FastAPI to get database session."""
    yield from db_manager.get_session()


def init_database() -> None:
    """Initialize database on application startup."""
    db_manager.initialize()


def close_database() -> None:
    """Close database on application shutdown."""
    db_manager.close()


# Utility functions for common database operations
def create_session() -> Session:
    """Create a new database session."""
    return db_manager.get_session_sync()


def execute_with_session(func, *args, **kwargs):
    """Execute a function with a database session."""
    with db_manager.get_session() as session:
        return func(session, *args, **kwargs)


class DatabaseError(Exception):
    """Custom database error."""
    pass


class TaskNotFoundError(DatabaseError):
    """Task not found error."""
    pass


class ContactNotFoundError(DatabaseError):
    """Contact not found error."""
    pass


class EmailNotFoundError(DatabaseError):
    """Email not found error."""
    pass
