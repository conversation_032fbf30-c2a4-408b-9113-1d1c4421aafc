# Google Integration Setup Guide

This guide will help you set up Google account integration for Gmail, Calendar, and Contacts.

## Prerequisites

- Google account
- Access to Google Cloud Console
- Personal Assistant application installed and running

## Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: "Personal Assistant"
4. Click "Create"

## Step 2: Enable APIs

1. In Google Cloud Console, go to "APIs & Services" → "Library"
2. Search for and enable these APIs:
   - **Gmail API**
   - **Google Calendar API** 
   - **People API** (for Contacts)

## Step 3: Create Credentials

1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth client ID"
3. If prompted, configure OAuth consent screen:
   - Choose "External" user type
   - Fill in app name: "Personal Assistant"
   - Add your email as test user
   - Add scopes: `../auth/gmail.readonly`, `../auth/calendar.readonly`, `../auth/contacts.readonly`
4. For OAuth client ID:
   - Application type: "Desktop application"
   - Name: "Personal Assistant Desktop"
5. Click "Create"
6. Download the JSON file

## Step 4: Install Credentials

1. Rename the downloaded file to `gmail_personal_credentials.json`
2. Place it in your Personal Assistant `config/` folder:
   ```
   C:\Users\<USER>\dev\local\config\gmail_personal_credentials.json
   ```

## Step 5: Create Token Directory

Create the tokens directory:
```
C:\Users\<USER>\dev\local\data\tokens\
```

## Step 6: Test Connection

1. Start Personal Assistant
2. Go to Settings → Integrations
3. Click "Connect" for Gmail
4. Follow the OAuth flow in your browser
5. Grant permissions to your Personal Assistant

## Expected File Structure

```
C:\Users\<USER>\dev\local\
├── config/
│   └── gmail_personal_credentials.json
├── data/
│   └── tokens/
│       └── gmail_personal_token.json (created automatically)
└── ...
```

## Troubleshooting

### "Credentials file not found"
- Ensure `gmail_personal_credentials.json` is in the `config/` folder
- Check the file name matches exactly

### "OAuth consent screen not configured"
- Complete Step 3 OAuth consent screen setup
- Add your email as a test user

### "Access blocked: This app's request is invalid"
- Ensure all required APIs are enabled
- Check OAuth consent screen configuration
- Verify redirect URIs in credentials

### "Token has been expired or revoked"
- Delete the token file: `data/tokens/gmail_personal_token.json`
- Reconnect through the web interface

## Security Notes

- Keep your credentials file secure and never share it
- The token file contains access tokens - treat it as sensitive
- You can revoke access anytime in your Google Account settings
- The app only requests read-only access to your data

## Work Account Setup (Optional)

To add a work Google account:

1. Create separate credentials for work account
2. Save as `gmail_work_credentials.json`
3. Uncomment work section in `config/settings.yaml`:
   ```yaml
   work:
     credentials_file: "./config/gmail_work_credentials.json"
     token_file: "./data/tokens/gmail_work_token.json"
     context: "work"
   ```
4. Restart Personal Assistant

## Support

If you encounter issues:
1. Check the application logs in the console
2. Verify all files are in the correct locations
3. Ensure Google APIs are enabled
4. Try disconnecting and reconnecting

---

**Once set up, your Personal Assistant will be able to:**
- Read and analyze your emails
- Access your calendar events
- Sync your contacts
- Provide intelligent insights across all your Google data
