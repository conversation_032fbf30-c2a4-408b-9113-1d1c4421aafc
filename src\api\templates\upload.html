{% extends "base.html" %}

{% block content %}
<div class="upload-page">
    <header class="page-header">
        <h1>Document Upload</h1>
        <p class="page-subtitle">Add documents to your knowledge base</p>
    </header>

    <!-- Upload Interface -->
    <section class="upload-interface">
        <div class="upload-zone" id="uploadZone">
            <div class="upload-content">
                <div class="upload-icon">📄</div>
                <h3>Drag & Drop Files Here</h3>
                <p>Or click to browse files</p>
                <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.txt,.md">
                <button class="btn btn-primary" id="browseBtn">Browse Files</button>
            </div>
        </div>

        <div class="upload-options">
            <div class="option-group">
                <label>
                    <input type="checkbox" id="extractText" checked>
                    Extract text content
                </label>
            </div>
            <div class="option-group">
                <label>
                    <input type="checkbox" id="createTasks" checked>
                    Auto-create tasks from content
                </label>
            </div>
            <div class="option-group">
                <label for="category">Category:</label>
                <select id="category">
                    <option value="general">General</option>
                    <option value="work">Work</option>
                    <option value="personal">Personal</option>
                    <option value="research">Research</option>
                </select>
            </div>
        </div>
    </section>

    <!-- Upload Progress -->
    <section class="upload-progress" id="uploadProgress" style="display: none;">
        <h2>Upload Progress</h2>
        <div class="progress-list" id="progressList">
            <!-- Progress items will be added here -->
        </div>
    </section>

    <!-- Recent Uploads -->
    <section class="recent-uploads">
        <h2>Recent Uploads</h2>
        <div class="uploads-list" id="uploadsList">
            <div class="upload-item">
                <div class="upload-info">
                    <div class="upload-name">No recent uploads</div>
                    <div class="upload-meta">Upload some documents to get started</div>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<style>
.upload-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.upload-zone {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-xxl);
    text-align: center;
    background-color: var(--bg-secondary);
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-zone:hover, .upload-zone.dragover {
    border-color: var(--accent-primary);
    background-color: var(--bg-primary);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.upload-icon {
    font-size: 4rem;
    opacity: 0.5;
}

.upload-zone h3 {
    margin: 0;
    color: var(--text-primary);
}

.upload-zone p {
    margin: 0;
    color: var(--text-secondary);
}

#fileInput {
    display: none;
}

.upload-options {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
    background-color: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    margin-top: var(--spacing-lg);
}

.option-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.option-group label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
}

.option-group select {
    padding: var(--spacing-xs);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.progress-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.progress-item {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.progress-filename {
    font-weight: 500;
}

.progress-status {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: var(--accent-primary);
    transition: width 0.3s ease;
}

.uploads-list {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    max-height: 400px;
    overflow-y: auto;
}

.upload-item {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.upload-item:last-child {
    border-bottom: none;
}

.upload-info {
    flex: 1;
}

.upload-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.upload-meta {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.upload-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: var(--font-size-base);
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.status-success {
    color: #28a745;
}

.status-error {
    color: #dc3545;
}

.status-processing {
    color: var(--accent-primary);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class UploadManager {
    constructor() {
        this.uploadQueue = [];
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadRecentUploads();
    }

    setupEventListeners() {
        const uploadZone = document.getElementById('uploadZone');
        const fileInput = document.getElementById('fileInput');
        const browseBtn = document.getElementById('browseBtn');

        // Drag and drop
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            this.handleFiles(e.dataTransfer.files);
        });

        // Click to browse
        uploadZone.addEventListener('click', () => fileInput.click());
        browseBtn.addEventListener('click', () => fileInput.click());

        // File input change
        fileInput.addEventListener('change', (e) => {
            this.handleFiles(e.target.files);
        });
    }

    handleFiles(files) {
        if (files.length === 0) return;

        const progressSection = document.getElementById('uploadProgress');
        progressSection.style.display = 'block';

        Array.from(files).forEach(file => {
            this.uploadFile(file);
        });
    }

    async uploadFile(file) {
        const progressItem = this.createProgressItem(file);
        
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('extract_text', document.getElementById('extractText').checked);
            formData.append('create_tasks', document.getElementById('createTasks').checked);
            formData.append('category', document.getElementById('category').value);

            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.updateProgress(progressItem, 100, 'Upload completed', 'success');
                app.showNotification(`${file.name} uploaded successfully!`, 'success');
                this.loadRecentUploads();
            } else {
                this.updateProgress(progressItem, 0, `Error: ${data.message}`, 'error');
                app.showNotification(`Upload failed: ${data.message}`, 'error');
            }
        } catch (error) {
            this.updateProgress(progressItem, 0, `Error: ${error.message}`, 'error');
            app.showNotification(`Upload failed: ${error.message}`, 'error');
        }
    }

    createProgressItem(file) {
        const progressList = document.getElementById('progressList');
        const progressItem = document.createElement('div');
        progressItem.className = 'progress-item';
        
        progressItem.innerHTML = `
            <div class="progress-header">
                <div class="progress-filename">${file.name}</div>
                <div class="progress-status">Uploading...</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 0%"></div>
            </div>
        `;

        progressList.appendChild(progressItem);
        return progressItem;
    }

    updateProgress(progressItem, percentage, status, statusType = 'processing') {
        const progressFill = progressItem.querySelector('.progress-fill');
        const progressStatus = progressItem.querySelector('.progress-status');
        
        progressFill.style.width = `${percentage}%`;
        progressStatus.textContent = status;
        progressStatus.className = `progress-status status-${statusType}`;
    }

    async loadRecentUploads() {
        try {
            const response = await fetch('/api/documents/recent');
            const data = await response.json();

            const uploadsList = document.getElementById('uploadsList');

            if (data.status === 'success' && data.documents && data.documents.length > 0) {
                const uploadsHtml = data.documents.map(doc => `
                    <div class="upload-item">
                        <div class="upload-info">
                            <div class="upload-name">${doc.filename || doc.title}</div>
                            <div class="upload-meta">
                                Uploaded: ${new Date(doc.created_at).toLocaleDateString()} | 
                                Size: ${this.formatFileSize(doc.file_size)} |
                                Type: ${doc.file_type || 'Unknown'}
                            </div>
                        </div>
                        <div class="upload-actions">
                            <button class="btn btn-sm btn-secondary" onclick="uploadManager.viewDocument('${doc.id}')">
                                View
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="uploadManager.deleteDocument('${doc.id}')">
                                Delete
                            </button>
                        </div>
                    </div>
                `).join('');

                uploadsList.innerHTML = uploadsHtml;
            } else {
                uploadsList.innerHTML = `
                    <div class="upload-item">
                        <div class="upload-info">
                            <div class="upload-name">No recent uploads</div>
                            <div class="upload-meta">Upload some documents to get started</div>
                        </div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading recent uploads:', error);
        }
    }

    formatFileSize(bytes) {
        if (!bytes) return 'Unknown size';
        
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    async viewDocument(docId) {
        // This would open a document viewer
        app.showNotification('Document viewer coming soon!', 'info');
    }

    async deleteDocument(docId) {
        if (!confirm('Are you sure you want to delete this document?')) return;

        try {
            const response = await fetch(`/api/documents/${docId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                app.showNotification('Document deleted successfully!', 'success');
                this.loadRecentUploads();
            } else {
                app.showNotification(`Error: ${data.message}`, 'error');
            }
        } catch (error) {
            app.showNotification(`Error deleting document: ${error.message}`, 'error');
        }
    }
}

// Initialize upload manager
let uploadManager;
document.addEventListener('DOMContentLoaded', () => {
    uploadManager = new UploadManager();
});
</script>
{% endblock %}
